export const uploadClientPhoto = jest.fn(async (imageUri: string, clientId: string): Promise<string> => {
  return `https://mock-storage.supabase.co/client-photos/user-123/${clientId}/photo.jpg`;
});

export const uploadConsultationPhoto = jest.fn(async (imageUri: string): Promise<string> => {
  const timestamp = Date.now();
  return `https://mock-storage.supabase.co/consultations/user-123/${timestamp}.jpg`;
});

export const deleteClientPhoto = jest.fn(async (storagePath: string): Promise<void> => {
  // Mock delete - no-op
});

export const getSignedUrl = jest.fn(async (storagePath: string, expiresIn: number = 3600): Promise<string> => {
  return `https://mock-storage.supabase.co/signed-url/${storagePath}?expires=${expiresIn}`;
});

// Reset mocks between tests
beforeEach(() => {
  uploadClientPhoto.mockClear();
  uploadConsultationPhoto.mockClear();
  deleteClientPhoto.mockClear();
  getSignedUrl.mockClear();
});
