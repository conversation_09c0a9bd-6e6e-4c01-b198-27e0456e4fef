export const mockAIResponse = {
  role: 'assistant' as const,
  content: '<PERSON><PERSON><PERSON><PERSON> de cabello completado',
};

export const generateTextSafe = jest.fn(async (params: {
  systemPrompt: string;
  userPrompt: string;
  images?: string[];
  userId: string;
  context?: string;
}): Promise<string> => {
  // Default mock response for hair analysis
  if (params.systemPrompt.includes('analiza el color actual')) {
    return JSON.stringify({
      chemicalHistory: {
        lastProcessType: 'mechas',
        lastProcessDate: '2024-01-15',
        hasUsedHomeRemedies: false,
      },
      physicalMeasurements: {
        totalLength: 30,
        monthlyGrowth: 1.5,
      },
      generalCharacteristics: {
        thickness: 'medio',
        density: 'alta',
        predominantTone: 'Castaño',
        predominantReflection: 'Cálido',
        lighteningBase: 'Naranja',
      },
      roots: {
        level: 5,
        tone: 'Castaño',
        reflection: 'Neutral',
        lighteningBase: 'Naranja',
        state: 'bueno',
        pigmentAccumulation: false,
        cuticleState: 'cerrada',
        porosity: 'media',
        elasticity: 'buena',
        resistance: 'alta',
        damageLevel: 2,
      },
      mids: {
        level: 6,
        tone: 'Castaño claro',
        reflection: 'Cálido',
        lighteningBase: 'Naranja',
        state: 'bueno',
        pigmentAccumulation: false,
        cuticleState: 'semiabierta',
        porosity: 'media',
        elasticity: 'buena',
        resistance: 'media',
        damageLevel: 3,
      },
      ends: {
        level: 7,
        tone: 'Rubio oscuro',
        reflection: 'Dorado',
        lighteningBase: 'Amarillo-naranja',
        state: 'regular',
        pigmentAccumulation: false,
        cuticleState: 'abierta',
        porosity: 'alta',
        elasticity: 'media',
        resistance: 'baja',
        damageLevel: 5,
      },
      grayAnalysis: {
        percentage: 15,
        visibility: 7,
        type: 'normal',
        distributionPattern: 'sienes',
      },
      hasDemarcationBands: false,
    });
  }

  // Default mock response for chat
  return 'Respuesta del asistente AI';
});

export const generateText = jest.fn(async (): Promise<string> => {
  return 'Mock AI response';
});

// Reset mock between tests
beforeEach(() => {
  generateTextSafe.mockClear();
  generateText.mockClear();
});
