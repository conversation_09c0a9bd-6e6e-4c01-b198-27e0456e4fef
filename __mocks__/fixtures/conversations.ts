import type { Message } from '@/types';

export const mockMessages: Message[] = [
  {
    id: 'msg-1',
    role: 'assistant',
    content: '¡Hola! Soy tu asistente experto en coloración capilar. ¿En qué puedo ayudarte hoy?',
    timestamp: new Date('2024-10-25T10:00:00Z'),
  },
  {
    id: 'msg-2',
    role: 'user',
    content: 'Necesito ayuda con una corrección de color para una clienta con mechas muy decoloradas',
    timestamp: new Date('2024-10-25T10:01:00Z'),
  },
  {
    id: 'msg-3',
    role: 'assistant',
    content: 'Claro, puedo ayudarte. Para darte la mejor recomendación necesito saber:\n\n1. ¿Cuál es el nivel actual de las mechas?\n2. ¿Qué nivel y tono desea la clienta?\n3. ¿Tiene canas? ¿Qué porcentaje?\n4. ¿Hay alguna sensibilidad o alergia conocida?',
    timestamp: new Date('2024-10-25T10:01:30Z'),
  },
];

export const mockConversationRaw = {
  id: 'conv-1',
  title: 'Corrección de color - Mechas decoloradas',
  is_pinned: false,
  user_id: 'user-123',
  organization_id: null,
  created_at: '2024-10-25T10:00:00Z',
  updated_at: '2024-10-25T10:01:30Z',
};

export const mockMessageRaw = {
  id: 'msg-1',
  conversation_id: 'conv-1',
  role: 'assistant',
  content: '¡Hola! Soy tu asistente experto en coloración capilar. ¿En qué puedo ayudarte hoy?',
  images: null,
  timestamp: '2024-10-25T10:00:00Z',
};

export const mockMessagesRaw = [
  {
    id: 'msg-1',
    conversation_id: 'conv-1',
    role: 'assistant',
    content: '¡Hola! Soy tu asistente experto en coloración capilar. ¿En qué puedo ayudarte hoy?',
    images: null,
    timestamp: '2024-10-25T10:00:00Z',
  },
  {
    id: 'msg-2',
    conversation_id: 'conv-1',
    role: 'user',
    content: 'Necesito ayuda con una corrección de color para una clienta con mechas muy decoloradas',
    images: null,
    timestamp: '2024-10-25T10:01:00Z',
  },
];
