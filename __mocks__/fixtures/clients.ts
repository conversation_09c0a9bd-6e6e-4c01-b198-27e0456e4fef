import type { Client } from '@/types';

export const mockClients: Client[] = [
  {
    id: '550e8400-e29b-41d4-a716-446655440001',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+34 612 345 678',
    lastVisit: new Date('2024-10-15'),
    photo: 'https://example.com/maria.jpg',
    notes: 'Cliente regular, prefiere tonos cálidos',
    knownAllergies: 'Ninguna',
    isPregnantOrBreastfeeding: false,
    hasSensitiveScalp: false,
    chemicalTreatments: {
      henna: false,
      chemicalStraightening: false,
      keratin: false,
    },
    communicationPreferences: {
      acceptsReminders: true,
      preferredMethod: 'whatsapp',
    },
    additionalNotes: 'Prefiere citas por la mañana',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440002',
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+34 623 456 789',
    lastVisit: new Date('2024-10-20'),
    knownAllergies: 'PPD (p-Phenylenediamine)',
    isPregnantOrBreastfeeding: true, // CRITICAL: Safety test case
    hasSensitiveScalp: true,
    chemicalTreatments: {
      henna: true, // CRITICAL: Henna conflict test case
      chemicalStraightening: false,
      keratin: false,
    },
    communicationPreferences: {
      acceptsReminders: false,
      preferredMethod: 'sms',
    },
    additionalNotes: 'Embarazada - 2do trimestre. Solo productos sin amoníaco.',
  },
  {
    id: '550e8400-e29b-41d4-a716-446655440003',
    name: 'Carmen Rodríguez',
    email: '<EMAIL>',
    isPregnantOrBreastfeeding: false,
    hasSensitiveScalp: false,
    chemicalTreatments: {
      henna: false,
      chemicalStraightening: true, // Has chemical straightening
      keratin: true, // Has keratin treatment
    },
    communicationPreferences: {
      acceptsReminders: true,
      preferredMethod: 'whatsapp',
    },
    notes: 'Tiene alisado químico y keratina reciente (hace 3 meses)',
  },
];

export const mockClientRaw = {
  id: '550e8400-e29b-41d4-a716-446655440001',
  name: 'María García',
  email: '<EMAIL>',
  phone: '+34 612 345 678',
  last_visit: '2024-10-15T00:00:00Z',
  photo: 'https://example.com/maria.jpg',
  notes: 'Cliente regular, prefiere tonos cálidos',
  known_allergies: 'Ninguna',
  is_pregnant_or_breastfeeding: false,
  has_sensitive_scalp: false,
  chemical_treatments: {
    henna: false,
    chemicalStraightening: false,
    keratin: false,
  },
  communication_preferences: {
    acceptsReminders: true,
    preferredMethod: 'whatsapp',
  },
  additional_notes: 'Prefiere citas por la mañana',
  created_at: '2024-01-01T00:00:00Z',
  updated_at: '2024-10-15T00:00:00Z',
};
