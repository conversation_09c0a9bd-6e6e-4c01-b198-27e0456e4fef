import React, { useState, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
  TextInput,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Palette, Save, Search } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { BrandAccordion } from '@/components/BrandAccordion';
import brandsData from '@/assets/data/brands.json';
import { loadPreferences, savePreferences } from '@/lib/brands/preferences';
import type { ProfessionalBrand, UserBrandPreferences } from '@/types';

const ALL_BRANDS: ProfessionalBrand[] = brandsData as ProfessionalBrand[];

export default function BrandsScreen() {
  const [preferences, setPreferences] = useState<UserBrandPreferences>({
    brands: [],
    updatedAt: new Date(),
  });
  const [expandedBrandId, setExpandedBrandId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');

  // Load preferences on mount
  useEffect(() => {
    loadPreferences()
      .then(setPreferences)
      .finally(() => setIsLoading(false));
  }, []);

  const selectedBrandIds = preferences.brands.map(b => b.brandId);

  const isSelected = (brandId: string) => selectedBrandIds.includes(brandId);

  // Filter brands by search query
  const filteredBrands = ALL_BRANDS.filter((brand) =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const getPreferredLines = (brandId: string): string[] => {
    const pref = preferences.brands.find(b => b.brandId === brandId);
    return pref?.preferredLines || [];
  };

  const toggleBrand = (brandId: string) => {
    setPreferences(prev => {
      const exists = prev.brands.some(b => b.brandId === brandId);

      if (exists) {
        // Deselect brand
        return {
          ...prev,
          brands: prev.brands.filter(b => b.brandId !== brandId),
        };
      } else {
        // Select brand (default: all lines)
        return {
          ...prev,
          brands: [
            ...prev.brands,
            {
              brandId,
              preferredLines: [], // Empty = all lines
              isPrimary: prev.brands.length === 0, // First brand is primary
            },
          ],
        };
      }
    });

    // Auto-expand on selection
    if (!isSelected(brandId)) {
      setExpandedBrandId(brandId);
    }
  };

  const handleBrandToggle = (brandId: string) => {
    if (isSelected(brandId)) {
      // If already selected, toggle expansion
      setExpandedBrandId(expandedBrandId === brandId ? null : brandId);
    } else {
      // If not selected, select and expand
      toggleBrand(brandId);
    }
  };

  const handleLineSelect = (brandId: string, line: string) => {
    setPreferences(prev => ({
      ...prev,
      brands: prev.brands.map(b => {
        if (b.brandId === brandId) {
          // Empty string means "all lines" (AI decides)
          if (line === '') {
            return { ...b, preferredLines: [] };
          }

          // Toggle specific line
          const hasLine = b.preferredLines.includes(line);
          return {
            ...b,
            preferredLines: hasLine
              ? b.preferredLines.filter(l => l !== line)
              : [...b.preferredLines, line],
          };
        }
        return b;
      }),
    }));
  };

  const handleSave = async () => {
    setIsSaving(true);
    try {
      await savePreferences(preferences);
      Alert.alert('Éxito', 'Marcas preferidas guardadas correctamente', [
        { text: 'OK', onPress: () => router.back() },
      ]);
    } catch (error) {
      console.error('Error saving preferences:', error);
      Alert.alert('Error', 'No se pudieron guardar las preferencias. Intenta de nuevo.');
    } finally {
      setIsSaving(false);
    }
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
        <Text style={styles.loadingText}>Cargando preferencias...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen
        options={{
          title: 'Marcas Preferidas',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }}
      />

      <ScrollView
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Palette size={28} color={Colors.light.primary} />
          <Text style={styles.headerText}>
            Selecciona las marcas y líneas que utilizas en tu salón
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Todas las Marcas</Text>
          <Text style={styles.sectionSubtitle}>
            {selectedBrandIds.length} marca{selectedBrandIds.length !== 1 ? 's' : ''} seleccionada
            {selectedBrandIds.length !== 1 ? 's' : ''}
          </Text>

          {/* Search Bar */}
          <View style={styles.searchContainer}>
            <Search color={Colors.light.textLight} size={20} />
            <TextInput
              style={styles.searchInput}
              placeholder="Buscar marca..."
              placeholderTextColor={Colors.light.textLight}
              value={searchQuery}
              onChangeText={setSearchQuery}
            />
          </View>

          {/* Results Count */}
          <Text style={styles.resultsCount}>
            {filteredBrands.length} marca{filteredBrands.length !== 1 ? 's' : ''} encontrada
            {filteredBrands.length !== 1 ? 's' : ''}
          </Text>

          <View style={styles.brandsContainer}>
            {filteredBrands.map((brand) => {
              const selected = isSelected(brand.id);
              const preferredLines = getPreferredLines(brand.id);

              return (
                <View key={brand.id}>
                  {selected ? (
                    // Use accordion for selected brands (to configure lines)
                    <BrandAccordion
                      brand={brand}
                      isExpanded={expandedBrandId === brand.id}
                      isSelected={true}
                      onToggle={() => handleBrandToggle(brand.id)}
                      selectedLine={preferredLines.length === 0 ? '' : preferredLines[0]}
                      onLineSelect={(line) => handleLineSelect(brand.id, line)}
                      showLineSelection={true}
                    />
                  ) : (
                    // Simple card for unselected brands
                    <TouchableOpacity
                      style={styles.brandCard}
                      onPress={() => toggleBrand(brand.id)}
                      activeOpacity={0.7}
                    >
                      <View style={styles.brandHeader}>
                        <View style={styles.brandInfo}>
                          <Text style={styles.brandName}>{brand.name}</Text>
                          <Text style={styles.brandCountry}>{brand.country}</Text>
                        </View>
                      </View>
                    </TouchableOpacity>
                  )}
                </View>
              );
            })}
          </View>
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>💡 ¿Por qué seleccionar tus marcas?</Text>
          <Text style={styles.infoText}>
            Al seleccionar tus marcas preferidas, Salonier AI podrá:
          </Text>
          <Text style={styles.infoItem}>• Priorizar fórmulas con tus productos</Text>
          <Text style={styles.infoItem}>• Calcular inventario y pedidos</Text>
          <Text style={styles.infoItem}>• Optimizar costos por servicio</Text>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity
          style={[styles.saveButton, isSaving && styles.saveButtonDisabled]}
          onPress={handleSave}
          disabled={isSaving}
          activeOpacity={0.8}
        >
          {isSaving ? (
            <ActivityIndicator size="small" color="#fff" />
          ) : (
            <>
              <Save size={20} color="#fff" />
              <Text style={styles.saveButtonText}>Guardar Marcas</Text>
            </>
          )}
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
    gap: 16,
  },
  loadingText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    fontWeight: '500' as const,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
    backgroundColor: Colors.light.background,
    padding: 20,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  headerText: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.text,
    lineHeight: 22,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  sectionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500' as const,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 14,
    gap: 12,
    marginTop: 4,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
  },
  resultsCount: {
    fontSize: 14,
    color: Colors.light.textLight,
    fontWeight: '500' as const,
  },
  brandsContainer: {
    gap: 12,
  },
  brandCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 18,
    borderWidth: 2,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  brandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandCountry: {
    fontSize: 13,
    color: Colors.light.textLight,
    fontWeight: '500' as const,
  },
  infoCard: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 20,
    padding: 20,
    gap: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  infoItem: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 22,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonDisabled: {
    opacity: 0.6,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
});
