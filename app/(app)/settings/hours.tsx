import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Clock, Save, ChevronRight } from 'lucide-react-native';
import Colors from '@/constants/colors';
import type { DaySchedule } from '@/types';

type DayName = 'monday' | 'tuesday' | 'wednesday' | 'thursday' | 'friday' | 'saturday' | 'sunday';

const DAY_NAMES: { key: DayName; label: string }[] = [
  { key: 'monday', label: 'Lunes' },
  { key: 'tuesday', label: 'Mart<PERSON>' },
  { key: 'wednesday', label: '<PERSON><PERSON><PERSON><PERSON><PERSON>' },
  { key: 'thursday', label: 'Jueves' },
  { key: 'friday', label: '<PERSON>ier<PERSON>' },
  { key: 'saturday', label: '<PERSON><PERSON>bad<PERSON>' },
  { key: 'sunday', label: '<PERSON>' },
];

const TIME_OPTIONS = [
  '00:00', '00:30', '01:00', '01:30', '02:00', '02:30', '03:00', '03:30',
  '04:00', '04:30', '05:00', '05:30', '06:00', '06:30', '07:00', '07:30',
  '08:00', '08:30', '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',
  '12:00', '12:30', '13:00', '13:30', '14:00', '14:30', '15:00', '15:30',
  '16:00', '16:30', '17:00', '17:30', '18:00', '18:30', '19:00', '19:30',
  '20:00', '20:30', '21:00', '21:30', '22:00', '22:30', '23:00', '23:30',
];

export default function BusinessHoursScreen() {
  const [schedule, setSchedule] = useState<Record<DayName, DaySchedule>>({
    monday: { open: '09:00', close: '18:00', closed: false },
    tuesday: { open: '09:00', close: '18:00', closed: false },
    wednesday: { open: '09:00', close: '18:00', closed: false },
    thursday: { open: '09:00', close: '18:00', closed: false },
    friday: { open: '09:00', close: '18:00', closed: false },
    saturday: { open: '10:00', close: '14:00', closed: false },
    sunday: { open: '09:00', close: '18:00', closed: true },
  });

  const [editingDay, setEditingDay] = useState<DayName | null>(null);
  const [editingField, setEditingField] = useState<'open' | 'close' | null>(null);

  const toggleDay = (day: DayName) => {
    setSchedule(prev => ({
      ...prev,
      [day]: {
        ...prev[day],
        closed: !prev[day].closed,
      },
    }));
  };

  const handleTimeSelect = (day: DayName, field: 'open' | 'close') => {
    setEditingDay(day);
    setEditingField(field);
  };

  const selectTime = (time: string) => {
    if (editingDay && editingField) {
      setSchedule(prev => ({
        ...prev,
        [editingDay]: {
          ...prev[editingDay],
          [editingField]: time,
        },
      }));
      setEditingDay(null);
      setEditingField(null);
    }
  };

  const handleSave = () => {
    console.log('Saving schedule:', schedule);
    Alert.alert('Éxito', 'Horarios actualizados correctamente', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  if (editingDay && editingField) {
    return (
      <View style={styles.container}>
        <Stack.Screen 
          options={{
            title: 'Seleccionar Hora',
            headerStyle: { backgroundColor: Colors.light.background },
            headerTintColor: Colors.light.text,
          }} 
        />
        <ScrollView style={styles.timePickerContainer}>
          <View style={styles.timeGrid}>
            {TIME_OPTIONS.map((time) => (
              <TouchableOpacity
                key={time}
                style={[
                  styles.timeOption,
                  schedule[editingDay][editingField] === time && styles.timeOptionSelected,
                ]}
                onPress={() => selectTime(time)}
              >
                <Text style={[
                  styles.timeOptionText,
                  schedule[editingDay][editingField] === time && styles.timeOptionTextSelected,
                ]}>
                  {time}
                </Text>
              </TouchableOpacity>
            ))}
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Horarios de Atención',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <Clock size={32} color={Colors.light.primary} />
          <Text style={styles.headerTitle}>Configura tus Horarios</Text>
          <Text style={styles.headerSubtitle}>
            Define los días y horas de apertura de tu salón
          </Text>
        </View>

        <View style={styles.scheduleList}>
          {DAY_NAMES.map(({ key, label }) => (
            <View key={key} style={styles.dayCard}>
              <View style={styles.dayHeader}>
                <View style={styles.dayInfo}>
                  <Text style={styles.dayName}>{label}</Text>
                  {schedule[key].closed ? (
                    <Text style={styles.closedText}>Cerrado</Text>
                  ) : (
                    <View style={styles.timeRow}>
                      <TouchableOpacity
                        style={styles.timeButton}
                        onPress={() => handleTimeSelect(key, 'open')}
                      >
                        <Text style={styles.timeButtonText}>{schedule[key].open}</Text>
                        <ChevronRight size={16} color={Colors.light.textSecondary} />
                      </TouchableOpacity>
                      <Text style={styles.timeSeparator}>-</Text>
                      <TouchableOpacity
                        style={styles.timeButton}
                        onPress={() => handleTimeSelect(key, 'close')}
                      >
                        <Text style={styles.timeButtonText}>{schedule[key].close}</Text>
                        <ChevronRight size={16} color={Colors.light.textSecondary} />
                      </TouchableOpacity>
                    </View>
                  )}
                </View>
                <Switch
                  value={!schedule[key].closed}
                  onValueChange={() => toggleDay(key)}
                  trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
                  thumbColor="#fff"
                />
              </View>
            </View>
          ))}
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Guardar Horarios</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  header: {
    alignItems: 'center',
    gap: 12,
    paddingVertical: 20,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  headerSubtitle: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  scheduleList: {
    gap: 12,
  },
  dayCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  dayHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  dayInfo: {
    flex: 1,
    gap: 8,
  },
  dayName: {
    fontSize: 17,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  closedText: {
    fontSize: 15,
    color: Colors.light.textLight,
  },
  timeRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  timeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
  },
  timeButtonText: {
    fontSize: 15,
    fontWeight: '500' as const,
    color: Colors.light.text,
  },
  timeSeparator: {
    fontSize: 15,
    color: Colors.light.textSecondary,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
  timePickerContainer: {
    flex: 1,
    padding: 20,
  },
  timeGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  timeOption: {
    width: '30%',
    backgroundColor: Colors.light.background,
    paddingVertical: 16,
    borderRadius: 16,
    alignItems: 'center',
    borderWidth: 2,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  timeOptionSelected: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  timeOptionText: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: Colors.light.text,
  },
  timeOptionTextSelected: {
    color: '#fff',
  },
});
