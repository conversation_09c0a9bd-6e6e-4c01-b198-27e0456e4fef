import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { User, Mail, Phone, Briefcase, FileText, Save } from 'lucide-react-native';
import Colors from '@/constants/colors';

export default function ProfileScreen() {
  const [profile, setProfile] = useState({
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '',
    specialty: '',
    licenseNumber: '',
    bio: '',
  });

  const handleSave = () => {
    console.log('Saving profile:', profile);
    Alert.alert('Éxito', 'Perfil actualizado correctamente', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Mi Perfil',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.avatarSection}>
          <View style={styles.avatar}>
            <Text style={styles.avatarText}>O</Text>
          </View>
          <TouchableOpacity style={styles.changePhotoButton}>
            <Text style={styles.changePhotoText}>Cambiar Foto</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Personal</Text>
          
          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <User size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Nombre Completo</Text>
            </View>
            <TextInput
              style={styles.input}
              value={profile.name}
              onChangeText={(text) => setProfile({ ...profile, name: text })}
              placeholder="Tu nombre completo"
              placeholderTextColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Mail size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Email</Text>
            </View>
            <TextInput
              style={styles.input}
              value={profile.email}
              onChangeText={(text) => setProfile({ ...profile, email: text })}
              placeholder="<EMAIL>"
              placeholderTextColor={Colors.light.textLight}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Phone size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Teléfono</Text>
            </View>
            <TextInput
              style={styles.input}
              value={profile.phone}
              onChangeText={(text) => setProfile({ ...profile, phone: text })}
              placeholder="+34 600 000 000"
              placeholderTextColor={Colors.light.textLight}
              keyboardType="phone-pad"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información Profesional</Text>
          
          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Briefcase size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Especialidad</Text>
            </View>
            <TextInput
              style={styles.input}
              value={profile.specialty}
              onChangeText={(text) => setProfile({ ...profile, specialty: text })}
              placeholder="Ej: Colorista, Estilista, etc."
              placeholderTextColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <FileText size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Número de Licencia</Text>
            </View>
            <TextInput
              style={styles.input}
              value={profile.licenseNumber}
              onChangeText={(text) => setProfile({ ...profile, licenseNumber: text })}
              placeholder="Número de licencia profesional"
              placeholderTextColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <FileText size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Biografía</Text>
            </View>
            <TextInput
              style={[styles.input, styles.textArea]}
              value={profile.bio}
              onChangeText={(text) => setProfile({ ...profile, bio: text })}
              placeholder="Cuéntanos sobre ti y tu experiencia..."
              placeholderTextColor={Colors.light.textLight}
              multiline
              numberOfLines={4}
              textAlignVertical="top"
            />
          </View>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Guardar Cambios</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  avatarSection: {
    alignItems: 'center',
    gap: 16,
    paddingVertical: 24,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: 40,
    fontWeight: '700' as const,
    color: Colors.light.primary,
  },
  changePhotoButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  changePhotoText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  labelText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 16,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
});
