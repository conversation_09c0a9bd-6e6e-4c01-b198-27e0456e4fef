import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack } from 'expo-router';
import { Users, Plus, Mail, Shield, CheckCircle, XCircle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import type { TeamMember } from '@/types';

const MOCK_TEAM: TeamMember[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: '<EMAIL>',
    role: 'owner',
    specialty: 'Colorista',
    joinedDate: new Date('2024-01-01'),
    isActive: true,
    permissions: {
      manageClients: true,
      createFormulas: true,
      viewReports: true,
      manageTeam: true,
      manageSettings: true,
    },
  },
];

const ROLE_LABELS: Record<string, string> = {
  owner: 'Propietario',
  admin: 'Administrador',
  stylist: '<PERSON><PERSON><PERSON><PERSON>',
  receptionist: '<PERSON>cepcion<PERSON>',
};

export default function TeamScreen() {
  const [team, setTeam] = useState<TeamMember[]>(MOCK_TEAM);

  const handleAddMember = () => {
    Alert.alert(
      'Añadir Miembro',
      'Esta función permitirá invitar a nuevos miembros al equipo',
      [{ text: 'OK' }]
    );
  };

  const handleEditMember = (member: TeamMember) => {
    Alert.alert(
      'Editar Miembro',
      `Editar permisos y rol de ${member.name}`,
      [{ text: 'Cancelar' }, { text: 'Editar' }]
    );
  };

  const toggleMemberStatus = (memberId: string) => {
    setTeam(prev => prev.map(m => 
      m.id === memberId ? { ...m, isActive: !m.isActive } : m
    ));
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Mi Equipo',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.header}>
          <View style={styles.headerInfo}>
            <Users size={24} color={Colors.light.primary} />
            <Text style={styles.headerTitle}>{team.length} Miembros</Text>
          </View>
          <TouchableOpacity 
            style={styles.addButton}
            onPress={handleAddMember}
            activeOpacity={0.7}
          >
            <Plus size={20} color="#fff" />
            <Text style={styles.addButtonText}>Invitar</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.infoCard}>
          <Text style={styles.infoText}>
            Gestiona tu equipo y controla los permisos de cada miembro. 
            Los miembros pueden tener distintos niveles de acceso según su rol.
          </Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Miembros del Equipo</Text>
          
          <View style={styles.teamList}>
            {team.map((member) => (
              <TouchableOpacity
                key={member.id}
                style={styles.memberCard}
                onPress={() => handleEditMember(member)}
                activeOpacity={0.7}
              >
                <View style={styles.memberAvatar}>
                  <Text style={styles.memberAvatarText}>
                    {member.name[0].toUpperCase()}
                  </Text>
                </View>
                
                <View style={styles.memberInfo}>
                  <View style={styles.memberHeader}>
                    <Text style={styles.memberName}>{member.name}</Text>
                    {member.isActive ? (
                      <CheckCircle size={16} color="#10B981" />
                    ) : (
                      <XCircle size={16} color={Colors.light.textLight} />
                    )}
                  </View>
                  
                  <View style={styles.memberDetails}>
                    <Mail size={14} color={Colors.light.textSecondary} />
                    <Text style={styles.memberEmail}>{member.email}</Text>
                  </View>
                  
                  <View style={styles.memberRole}>
                    <Shield size={14} color={Colors.light.primary} />
                    <Text style={styles.memberRoleText}>
                      {ROLE_LABELS[member.role]}
                    </Text>
                    {member.specialty && (
                      <>
                        <Text style={styles.dot}>•</Text>
                        <Text style={styles.memberSpecialty}>{member.specialty}</Text>
                      </>
                    )}
                  </View>
                </View>

                <View style={styles.memberActions}>
                  <TouchableOpacity
                    onPress={(e) => {
                      e.stopPropagation();
                      toggleMemberStatus(member.id);
                    }}
                    style={[
                      styles.statusBadge,
                      member.isActive ? styles.activeBadge : styles.inactiveBadge
                    ]}
                  >
                    <Text style={[
                      styles.statusText,
                      member.isActive ? styles.activeText : styles.inactiveText
                    ]}>
                      {member.isActive ? 'Activo' : 'Inactivo'}
                    </Text>
                  </TouchableOpacity>
                </View>
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Roles y Permisos</Text>
          
          <View style={styles.rolesCard}>
            <View style={styles.roleItem}>
              <Text style={styles.roleName}>👑 Propietario</Text>
              <Text style={styles.roleDescription}>
                Acceso completo a todas las funciones
              </Text>
            </View>
            
            <View style={styles.roleItem}>
              <Text style={styles.roleName}>⚙️ Administrador</Text>
              <Text style={styles.roleDescription}>
                Gestión de clientes, fórmulas y equipo
              </Text>
            </View>
            
            <View style={styles.roleItem}>
              <Text style={styles.roleName}>✂️ Estilista</Text>
              <Text style={styles.roleDescription}>
                Crear fórmulas y gestionar clientes
              </Text>
            </View>
            
            <View style={styles.roleItem}>
              <Text style={styles.roleName}>📋 Recepcionista</Text>
              <Text style={styles.roleDescription}>
                Ver clientes y agendar citas
              </Text>
            </View>
          </View>
        </View>

        <View style={{ height: 40 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 16,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 3,
  },
  addButtonText: {
    fontSize: 15,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
  infoCard: {
    backgroundColor: Colors.light.primary + '10',
    borderRadius: 14,
    padding: 16,
    borderLeftWidth: 4,
    borderLeftColor: Colors.light.primary,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  section: {
    gap: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  teamList: {
    gap: 12,
  },
  memberCard: {
    flexDirection: 'row',
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 18,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    alignItems: 'center',
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  memberAvatar: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  memberAvatarText: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: Colors.light.primary,
  },
  memberInfo: {
    flex: 1,
    gap: 4,
  },
  memberHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  memberName: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  memberDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  memberEmail: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  memberRole: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  memberRoleText: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  dot: {
    fontSize: 13,
    color: Colors.light.textLight,
  },
  memberSpecialty: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  memberActions: {
    gap: 8,
  },
  statusBadge: {
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 8,
  },
  activeBadge: {
    backgroundColor: '#10B981' + '15',
  },
  inactiveBadge: {
    backgroundColor: Colors.light.border,
  },
  statusText: {
    fontSize: 12,
    fontWeight: '600' as const,
  },
  activeText: {
    color: '#10B981',
  },
  inactiveText: {
    color: Colors.light.textLight,
  },
  rolesCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 16,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  roleItem: {
    gap: 4,
  },
  roleName: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  roleDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
});
