import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity, 
  TextInput,
  Alert,
  Platform,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { 
  Briefcase, 
  MapPin, 
  Phone, 
  Mail, 
  Globe, 
  Instagram,
  Clock,
  Save,
} from 'lucide-react-native';
import Colors from '@/constants/colors';

export default function BusinessScreen() {
  const [business, setBusiness] = useState({
    name: '',
    street: '',
    city: '',
    postalCode: '',
    state: '',
    country: '',
    phone: '',
    email: '',
    website: '',
    instagram: '',
  });

  const handleSave = () => {
    console.log('Saving business:', business);
    Alert.alert('Éxito', 'Información del negocio actualizada', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Mi Negocio',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.logoSection}>
          <View style={styles.logoPlaceholder}>
            <Briefcase size={40} color={Colors.light.textLight} />
          </View>
          <TouchableOpacity style={styles.changeLogoButton}>
            <Text style={styles.changeLogoText}>Añadir Logo</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Información del Salón</Text>
          
          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Briefcase size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Nombre del Salón</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.name}
              onChangeText={(text) => setBusiness({ ...business, name: text })}
              placeholder="Nombre de tu salón"
              placeholderTextColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <MapPin size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Dirección (Calle y Número)</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.street}
              onChangeText={(text) => setBusiness({ ...business, street: text })}
              placeholder="Calle Principal 123"
              placeholderTextColor={Colors.light.textLight}
            />
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.labelText}>Ciudad</Text>
              <TextInput
                style={styles.input}
                value={business.city}
                onChangeText={(text) => setBusiness({ ...business, city: text })}
                placeholder="Madrid"
                placeholderTextColor={Colors.light.textLight}
              />
            </View>

            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.labelText}>Código Postal</Text>
              <TextInput
                style={styles.input}
                value={business.postalCode}
                onChangeText={(text) => setBusiness({ ...business, postalCode: text })}
                placeholder="28001"
                placeholderTextColor={Colors.light.textLight}
                keyboardType={Platform.OS === 'ios' ? 'numbers-and-punctuation' : 'default'}
              />
            </View>
          </View>

          <View style={styles.row}>
            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.labelText}>Provincia/Estado</Text>
              <TextInput
                style={styles.input}
                value={business.state}
                onChangeText={(text) => setBusiness({ ...business, state: text })}
                placeholder="Madrid"
                placeholderTextColor={Colors.light.textLight}
              />
            </View>

            <View style={[styles.inputGroup, styles.flex1]}>
              <Text style={styles.labelText}>País</Text>
              <TextInput
                style={styles.input}
                value={business.country}
                onChangeText={(text) => setBusiness({ ...business, country: text })}
                placeholder="España"
                placeholderTextColor={Colors.light.textLight}
              />
            </View>
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Phone size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Teléfono</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.phone}
              onChangeText={(text) => setBusiness({ ...business, phone: text })}
              placeholder="+**************"
              placeholderTextColor={Colors.light.textLight}
              keyboardType="phone-pad"
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Mail size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Email</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.email}
              onChangeText={(text) => setBusiness({ ...business, email: text })}
              placeholder="<EMAIL>"
              placeholderTextColor={Colors.light.textLight}
              keyboardType="email-address"
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Redes y Web</Text>
          
          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Globe size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Sitio Web</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.website}
              onChangeText={(text) => setBusiness({ ...business, website: text })}
              placeholder="www.tusitio.com"
              placeholderTextColor={Colors.light.textLight}
              autoCapitalize="none"
              keyboardType="url"
            />
          </View>

          <View style={styles.inputGroup}>
            <View style={styles.inputLabel}>
              <Instagram size={20} color={Colors.light.textSecondary} />
              <Text style={styles.labelText}>Instagram</Text>
            </View>
            <TextInput
              style={styles.input}
              value={business.instagram}
              onChangeText={(text) => setBusiness({ ...business, instagram: text })}
              placeholder="@tusalon"
              placeholderTextColor={Colors.light.textLight}
              autoCapitalize="none"
            />
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color={Colors.light.text} />
            <Text style={styles.sectionTitle}>Horario de Atención</Text>
          </View>
          <TouchableOpacity 
            style={styles.scheduleCard}
            onPress={() => router.push('/settings/hours')}
          >
            <Text style={styles.scheduleText}>Configurar Horarios</Text>
            <Text style={styles.scheduleSubtext}>
              Define los días y horarios de apertura de tu salón
            </Text>
          </TouchableOpacity>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Guardar Cambios</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  logoSection: {
    alignItems: 'center',
    gap: 16,
    paddingVertical: 24,
  },
  logoPlaceholder: {
    width: 100,
    height: 100,
    borderRadius: 20,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
  },
  changeLogoButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
  },
  changeLogoText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  section: {
    gap: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  labelText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  row: {
    flexDirection: 'row',
    gap: 12,
  },
  flex1: {
    flex: 1,
  },
  input: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 16,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  scheduleCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 20,
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  scheduleText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  scheduleSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
});
