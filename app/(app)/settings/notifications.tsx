import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity,
  Switch,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Smartphone, Mail, Clock, Save } from 'lucide-react-native';
import Colors from '@/constants/colors';

export default function NotificationsScreen() {
  const [pushEnabled, setPushEnabled] = useState(true);
  const [pushAppointments, setPushAppointments] = useState(true);
  const [pushMessages, setPushMessages] = useState(true);
  const [pushTeam, setPushTeam] = useState(false);
  const [pushMarketing, setPushMarketing] = useState(false);
  
  const [emailEnabled, setEmailEnabled] = useState(true);
  const [emailAppointments, setEmailAppointments] = useState(true);
  const [emailWeekly, setEmailWeekly] = useState(true);
  const [emailMarketing, setEmailMarketing] = useState(false);

  const [reminderTime, setReminderTime] = useState(60);

  const handleSave = () => {
    console.log('Saving notification settings');
    Alert.alert('Éxito', 'Configuración de notificaciones actualizada', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Notificaciones',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Smartphone size={20} color={Colors.light.text} />
            <Text style={styles.sectionTitle}>Notificaciones Push</Text>
          </View>
          
          <View style={styles.settingCard}>
            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Activar Push</Text>
                <Text style={styles.settingSubtitle}>
                  Recibe notificaciones en tu dispositivo
                </Text>
              </View>
              <Switch
                value={pushEnabled}
                onValueChange={setPushEnabled}
                trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                thumbColor={pushEnabled ? Colors.light.primary : '#f4f3f4'}
              />
            </View>

            {pushEnabled && (
              <>
                <View style={styles.divider} />
                
                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Citas y Recordatorios</Text>
                  <Switch
                    value={pushAppointments}
                    onValueChange={setPushAppointments}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={pushAppointments ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>

                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Mensajes de Clientes</Text>
                  <Switch
                    value={pushMessages}
                    onValueChange={setPushMessages}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={pushMessages ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>

                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Actualizaciones del Equipo</Text>
                  <Switch
                    value={pushTeam}
                    onValueChange={setPushTeam}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={pushTeam ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>

                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Promociones y Marketing</Text>
                  <Switch
                    value={pushMarketing}
                    onValueChange={setPushMarketing}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={pushMarketing ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>
              </>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Mail size={20} color={Colors.light.text} />
            <Text style={styles.sectionTitle}>Notificaciones por Email</Text>
          </View>
          
          <View style={styles.settingCard}>
            <View style={styles.settingRow}>
              <View style={styles.settingInfo}>
                <Text style={styles.settingTitle}>Activar Email</Text>
                <Text style={styles.settingSubtitle}>
                  Recibe notificaciones por correo electrónico
                </Text>
              </View>
              <Switch
                value={emailEnabled}
                onValueChange={setEmailEnabled}
                trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                thumbColor={emailEnabled ? Colors.light.primary : '#f4f3f4'}
              />
            </View>

            {emailEnabled && (
              <>
                <View style={styles.divider} />
                
                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Confirmaciones de Citas</Text>
                  <Switch
                    value={emailAppointments}
                    onValueChange={setEmailAppointments}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={emailAppointments ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>

                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Reporte Semanal</Text>
                  <Switch
                    value={emailWeekly}
                    onValueChange={setEmailWeekly}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={emailWeekly ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>

                <View style={styles.settingRow}>
                  <Text style={styles.settingLabel}>Novedades y Tips</Text>
                  <Switch
                    value={emailMarketing}
                    onValueChange={setEmailMarketing}
                    trackColor={{ false: Colors.light.border, true: Colors.light.primary + '40' }}
                    thumbColor={emailMarketing ? Colors.light.primary : '#f4f3f4'}
                  />
                </View>
              </>
            )}
          </View>
        </View>

        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Clock size={20} color={Colors.light.text} />
            <Text style={styles.sectionTitle}>Recordatorios</Text>
          </View>
          
          <View style={styles.settingCard}>
            <Text style={styles.settingLabel}>Tiempo de Anticipación</Text>
            <Text style={styles.reminderSubtext}>
              Recordar citas con esta anticipación
            </Text>
            
            <View style={styles.reminderOptions}>
              {[30, 60, 120, 1440].map((minutes) => {
                const label = minutes < 60 
                  ? `${minutes} min` 
                  : minutes === 1440 
                    ? '1 día' 
                    : `${minutes / 60} horas`;
                
                return (
                  <TouchableOpacity
                    key={minutes}
                    style={[
                      styles.reminderOption,
                      reminderTime === minutes && styles.reminderOptionSelected
                    ]}
                    onPress={() => setReminderTime(minutes)}
                    activeOpacity={0.7}
                  >
                    <Text style={[
                      styles.reminderOptionText,
                      reminderTime === minutes && styles.reminderOptionTextSelected
                    ]}>
                      {label}
                    </Text>
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Guardar Cambios</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  section: {
    gap: 12,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  settingCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 18,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  settingRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 12,
  },
  settingInfo: {
    flex: 1,
    gap: 4,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  settingSubtitle: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  settingLabel: {
    fontSize: 15,
    color: Colors.light.text,
    flex: 1,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
  },
  reminderSubtext: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginTop: -8,
  },
  reminderOptions: {
    flexDirection: 'row',
    gap: 8,
    marginTop: 8,
  },
  reminderOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: Colors.light.backgroundSecondary,
    borderWidth: 2,
    borderColor: Colors.light.border,
    alignItems: 'center',
  },
  reminderOptionSelected: {
    backgroundColor: Colors.light.primary + '10',
    borderColor: Colors.light.primary,
  },
  reminderOptionText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  reminderOptionTextSelected: {
    color: Colors.light.primary,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
});
