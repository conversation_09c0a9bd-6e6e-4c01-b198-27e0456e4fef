import React, { useState } from 'react';
import { 
  StyleSheet, 
  Text, 
  View, 
  ScrollView, 
  TouchableOpacity,
  Alert,
} from 'react-native';
import { Stack, router } from 'expo-router';
import { Check, Save } from 'lucide-react-native';
import Colors from '@/constants/colors';

type Language = 'es' | 'en';
type MeasurementSystem = 'metric' | 'imperial';
type Currency = 'USD' | 'EUR' | 'MXN' | 'COP' | 'ARS' | 'CLP';
type DateFormat = 'DD/MM/YYYY' | 'MM/DD/YYYY';

export default function RegionalScreen() {
  const [language, setLanguage] = useState<Language>('es');
  const [measurementSystem, setMeasurementSystem] = useState<MeasurementSystem>('metric');
  const [currency, setCurrency] = useState<Currency>('EUR');
  const [dateFormat, setDateFormat] = useState<DateFormat>('DD/MM/YYYY');

  const handleSave = () => {
    console.log('Saving settings:', { language, measurementSystem, currency, dateFormat });
    Alert.alert('Éxito', 'Configuración regional actualizada', [
      { text: 'OK', onPress: () => router.back() }
    ]);
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{
          title: 'Configuración Regional',
          headerStyle: { backgroundColor: Colors.light.background },
          headerTintColor: Colors.light.text,
        }} 
      />
      
      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Idioma</Text>
          <Text style={styles.sectionDescription}>
            Selecciona el idioma de la aplicación
          </Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.option,
                language === 'es' && styles.optionSelected
              ]}
              onPress={() => setLanguage('es')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionFlag}>🇪🇸</Text>
                <Text style={styles.optionText}>Español</Text>
              </View>
              {language === 'es' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.option,
                language === 'en' && styles.optionSelected
              ]}
              onPress={() => setLanguage('en')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionFlag}>🇺🇸</Text>
                <Text style={styles.optionText}>English</Text>
              </View>
              {language === 'en' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Sistema de Medidas</Text>
          <Text style={styles.sectionDescription}>
            Elige el sistema de medidas para longitudes y cantidades
          </Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.option,
                measurementSystem === 'metric' && styles.optionSelected
              ]}
              onPress={() => setMeasurementSystem('metric')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionText}>Sistema Métrico</Text>
                <Text style={styles.optionSubtext}>cm, ml, gramos</Text>
              </View>
              {measurementSystem === 'metric' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.option,
                measurementSystem === 'imperial' && styles.optionSelected
              ]}
              onPress={() => setMeasurementSystem('imperial')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionText}>Sistema Imperial</Text>
                <Text style={styles.optionSubtext}>pulgadas, oz, libras</Text>
              </View>
              {measurementSystem === 'imperial' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Moneda</Text>
          <Text style={styles.sectionDescription}>
            Selecciona tu moneda local
          </Text>
          
          <View style={styles.optionsContainer}>
            {[
              { code: 'EUR', name: 'Euro', symbol: '€' },
              { code: 'USD', name: 'Dólar Estadounidense', symbol: '$' },
              { code: 'MXN', name: 'Peso Mexicano', symbol: '$' },
              { code: 'COP', name: 'Peso Colombiano', symbol: '$' },
              { code: 'ARS', name: 'Peso Argentino', symbol: '$' },
              { code: 'CLP', name: 'Peso Chileno', symbol: '$' },
            ].map((curr) => (
              <TouchableOpacity
                key={curr.code}
                style={[
                  styles.option,
                  currency === curr.code && styles.optionSelected
                ]}
                onPress={() => setCurrency(curr.code as Currency)}
                activeOpacity={0.7}
              >
                <View style={styles.optionContent}>
                  <Text style={styles.optionText}>{curr.name}</Text>
                  <Text style={styles.optionSubtext}>{curr.symbol} {curr.code}</Text>
                </View>
                {currency === curr.code && <Check size={20} color={Colors.light.primary} />}
              </TouchableOpacity>
            ))}
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.sectionTitle}>Formato de Fecha</Text>
          <Text style={styles.sectionDescription}>
            Elige cómo se mostrarán las fechas
          </Text>
          
          <View style={styles.optionsContainer}>
            <TouchableOpacity
              style={[
                styles.option,
                dateFormat === 'DD/MM/YYYY' && styles.optionSelected
              ]}
              onPress={() => setDateFormat('DD/MM/YYYY')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionText}>DD/MM/YYYY</Text>
                <Text style={styles.optionSubtext}>31/12/2024</Text>
              </View>
              {dateFormat === 'DD/MM/YYYY' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>

            <TouchableOpacity
              style={[
                styles.option,
                dateFormat === 'MM/DD/YYYY' && styles.optionSelected
              ]}
              onPress={() => setDateFormat('MM/DD/YYYY')}
              activeOpacity={0.7}
            >
              <View style={styles.optionContent}>
                <Text style={styles.optionText}>MM/DD/YYYY</Text>
                <Text style={styles.optionSubtext}>12/31/2024</Text>
              </View>
              {dateFormat === 'MM/DD/YYYY' && <Check size={20} color={Colors.light.primary} />}
            </TouchableOpacity>
          </View>
        </View>

        <View style={{ height: 100 }} />
      </ScrollView>

      <View style={styles.footer}>
        <TouchableOpacity 
          style={styles.saveButton}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Save size={20} color="#fff" />
          <Text style={styles.saveButtonText}>Guardar Cambios</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
    gap: 32,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  sectionDescription: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  optionsContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  optionSelected: {
    backgroundColor: Colors.light.primary + '08',
  },
  optionContent: {
    flex: 1,
    gap: 4,
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  optionFlag: {
    fontSize: 24,
    marginRight: 12,
  },
  optionText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  optionSubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginLeft: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 20,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  saveButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.primary,
    borderRadius: 16,
    paddingVertical: 18,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  saveButtonText: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.background,
    letterSpacing: -0.2,
  },
});
