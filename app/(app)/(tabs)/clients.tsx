import React, { useState } from 'react';
import { StyleSheet, Text, View, FlatList, TouchableOpacity, TextInput, Alert, Modal, Pressable, ActivityIndicator } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Search, User, Plus, Sparkles, MoreVertical, Edit2, Trash2 } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClients } from '@/contexts/ClientContext';
import { useFormula } from '@/contexts/FormulaContext';

export default function ClientsScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clients, isLoading, deleteClient } = useClients();
  const { selectClient } = useFormula();
  const [search, setSearch] = useState('');
  const [menuVisible, setMenuVisible] = useState<string | null>(null);

  const filteredClients = clients.filter(client =>
    client.name.toLowerCase().includes(search.toLowerCase())
  );

  const formatDate = (date?: Date) => {
    if (!date) return 'Nueva';
    return date.toLocaleDateString('es-ES', { day: 'numeric', month: 'short' });
  };

  const handleViewClient = (clientId: string) => {
    router.push(`/clients/${clientId}`);
  };

  const handleStartFormula = (client: typeof clients[0], e: any) => {
    e.stopPropagation(); // Prevent card tap
    selectClient(client);
    router.push(`/formula/step1?clientId=${client.id}`);
  };

  const handleEdit = (clientId: string) => {
    setMenuVisible(null);
    router.push(`/clients/edit?id=${clientId}`);
  };

  const handleDelete = async (clientId: string, clientName: string) => {
    setMenuVisible(null);
    Alert.alert(
      'Eliminar Cliente',
      `¿Estás seguro de que quieres eliminar a ${clientName}?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteClient(clientId);
            } catch {
              Alert.alert('Error', 'No se pudo eliminar el cliente');
            }
          },
        },
      ]
    );
  };

  if (isLoading) {
    return (
      <View style={[styles.container, styles.centered]}>
        <ActivityIndicator size="large" color={Colors.light.primary} />
        <Text style={styles.loadingText}>Cargando clientes...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top + 20 }]}>
        <View style={styles.titleRow}>
          <Text style={styles.title}>Clientes</Text>
          <TouchableOpacity
            style={styles.addButton}
            onPress={() => router.push('/clients/new')}
          >
            <Plus color={Colors.light.background} size={20} />
          </TouchableOpacity>
        </View>
        <View style={styles.searchContainer}>
          <Search color={Colors.light.textLight} size={20} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar cliente..."
            placeholderTextColor={Colors.light.textLight}
            value={search}
            onChangeText={setSearch}
          />
        </View>
      </View>

      <FlatList
        data={filteredClients}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.list}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={styles.clientCard}
            onPress={() => handleViewClient(item.id)}
            activeOpacity={0.7}
          >
            <View style={styles.avatar}>
              <User color={Colors.light.primary} size={24} />
            </View>
            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>{item.name}</Text>
              <Text style={styles.clientNotes}>{item.notes}</Text>
            </View>
            <View style={styles.clientActions}>
              <Text style={styles.clientDate}>{formatDate(item.lastVisit)}</Text>
              <View style={styles.actionButtons}>
                <TouchableOpacity
                  style={styles.menuButton}
                  onPress={(e) => {
                    e.stopPropagation();
                    setMenuVisible(item.id);
                  }}
                >
                  <MoreVertical color={Colors.light.text} size={20} />
                </TouchableOpacity>
                <TouchableOpacity
                  style={styles.formulaButton}
                  onPress={(e) => handleStartFormula(item, e)}
                >
                  <Sparkles color={Colors.light.background} size={18} />
                </TouchableOpacity>
              </View>
            </View>

            <Modal
              visible={menuVisible === item.id}
              transparent
              animationType="fade"
              onRequestClose={() => setMenuVisible(null)}
            >
              <Pressable
                style={styles.modalOverlay}
                onPress={() => setMenuVisible(null)}
              >
                <View style={styles.menuContainer}>
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => handleEdit(item.id)}
                  >
                    <Edit2 color={Colors.light.text} size={20} />
                    <Text style={styles.menuItemText}>Editar</Text>
                  </TouchableOpacity>
                  <View style={styles.menuDivider} />
                  <TouchableOpacity
                    style={styles.menuItem}
                    onPress={() => handleDelete(item.id, item.name)}
                  >
                    <Trash2 color="#EF4444" size={20} />
                    <Text style={[styles.menuItemText, styles.menuItemTextDanger]}>Eliminar</Text>
                  </TouchableOpacity>
                </View>
              </Pressable>
            </Modal>
          </TouchableOpacity>
        )}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    backgroundColor: Colors.light.background,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  titleRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 20,
  },
  title: {
    fontSize: 36,
    fontWeight: '800' as const,
    color: Colors.light.text,
    letterSpacing: -0.5,
  },
  addButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 14,
    gap: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
  },
  list: {
    padding: 20,
    gap: 16,
  },
  clientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 18,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clientInfo: {
    flex: 1,
    gap: 6,
  },
  clientName: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.text,
    letterSpacing: -0.2,
  },
  clientNotes: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  clientDate: {
    fontSize: 13,
    color: Colors.light.textLight,
    fontWeight: '600' as const,
    marginBottom: 10,
  },
  clientActions: {
    alignItems: 'flex-end',
    gap: 10,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  menuButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  formulaButton: {
    width: 44,
    height: 44,
    borderRadius: 22,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: Colors.light.overlay,
    justifyContent: 'center',
    alignItems: 'center',
  },
  menuContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    minWidth: 220,
    paddingVertical: 12,
    marginHorizontal: 20,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.15,
    shadowRadius: 24,
    elevation: 12,
  },
  menuItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  menuItemText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  menuItemTextDanger: {
    color: Colors.light.error,
  },
  menuDivider: {
    height: 1,
    backgroundColor: Colors.light.divider,
    marginVertical: 6,
    marginHorizontal: 12,
  },
  centered: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 16,
    fontSize: 16,
    color: Colors.light.textSecondary,
    fontWeight: '500' as const,
  },
});
