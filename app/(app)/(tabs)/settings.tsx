import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';
import { router } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { 
  User, 
  Briefcase, 
  Users, 
  Globe, 
  Palette, 
  Bell, 
  Lock, 
  CreditCard,
  HelpCircle, 
  Info,
  ChevronRight,
  LogOut
} from 'lucide-react-native';
import Colors from '@/constants/colors';

type SettingOption = {
  id: string;
  title: string;
  subtitle: string;
  icon: typeof User;
  route?: string;
  color: string;
};

const settingsGroups = [
  {
    title: 'Cuenta',
    options: [
      {
        id: 'profile',
        title: 'Mi Perfil',
        subtitle: 'Información personal y profesional',
        icon: User,
        route: '/settings/profile',
        color: Colors.light.primary,
      },
      {
        id: 'business',
        title: 'Mi Negocio',
        subtitle: 'Datos del salón y ubicación',
        icon: Briefcase,
        route: '/settings/business',
        color: '#E67E22',
      },
      {
        id: 'team',
        title: 'Mi Equipo',
        subtitle: '<PERSON><PERSON><PERSON> empleados a tu salón',
        icon: Users,
        route: '/settings/team',
        color: '#9B59B6',
      },
    ] as SettingOption[],
  },
  {
    title: 'Preferencias',
    options: [
      {
        id: 'regional',
        title: 'Configuración Regional',
        subtitle: 'Idioma, moneda y medidas',
        icon: Globe,
        route: '/settings/regional',
        color: '#3498DB',
      },
      {
        id: 'brands',
        title: 'Marcas Preferidas',
        subtitle: 'Marcas de coloración que usas',
        icon: Palette,
        route: '/settings/brands',
        color: '#E91E63',
      },
      {
        id: 'notifications',
        title: 'Notificaciones',
        subtitle: 'Alertas y recordatorios',
        icon: Bell,
        route: '/settings/notifications',
        color: '#FF9800',
      },
    ] as SettingOption[],
  },
  {
    title: 'App',
    options: [
      {
        id: 'privacy',
        title: 'Privacidad y Seguridad',
        subtitle: 'Permisos y respaldo de datos',
        icon: Lock,
        route: '/settings/privacy',
        color: '#607D8B',
      },
      {
        id: 'subscription',
        title: 'Suscripción',
        subtitle: 'Plan actual y facturación',
        icon: CreditCard,
        route: '/settings/subscription',
        color: '#4CAF50',
      },
      {
        id: 'help',
        title: 'Ayuda y Soporte',
        subtitle: 'Centro de ayuda y tutoriales',
        icon: HelpCircle,
        route: '/settings/help',
        color: '#00BCD4',
      },
      {
        id: 'about',
        title: 'Acerca de',
        subtitle: 'Versión 1.0.0',
        icon: Info,
        route: '/settings/about',
        color: '#795548',
      },
    ] as SettingOption[],
  },
];

export default function SettingsScreen() {
  const insets = useSafeAreaInsets();

  const handleOptionPress = (route?: string) => {
    if (route) {
      console.log('Navigate to:', route);
      router.push(route as any);
    }
  };

  const handleLogout = () => {
    console.log('Logout pressed');
    // TODO: Implement logout logic
  };

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top + 20 }]}>
        <Text style={styles.title}>Configuración</Text>
      </View>

      <ScrollView 
        contentContainerStyle={styles.content}
        showsVerticalScrollIndicator={false}
      >
        <View style={styles.profileCard}>
          <View style={styles.avatarContainer}>
            <View style={styles.avatar}>
              <Text style={styles.avatarText}>O</Text>
            </View>
            <View style={styles.onlineBadge} />
          </View>
          <View style={styles.profileInfo}>
            <Text style={styles.profileName}>Oscar</Text>
            <Text style={styles.profileEmail}><EMAIL></Text>
          </View>
          <TouchableOpacity 
            style={styles.editButton}
            onPress={() => handleOptionPress('/settings/profile')}
          >
            <Text style={styles.editButtonText}>Editar Perfil</Text>
          </TouchableOpacity>
        </View>

        {settingsGroups.map((group) => (
          <View key={group.title} style={styles.section}>
            <Text style={styles.sectionTitle}>{group.title}</Text>
            <View style={styles.optionsContainer}>
              {group.options.map((option) => {
                const IconComponent = option.icon;
                return (
                  <TouchableOpacity 
                    key={option.id} 
                    style={styles.option}
                    onPress={() => handleOptionPress(option.route)}
                    activeOpacity={0.7}
                  >
                    <View style={[styles.iconContainer, { backgroundColor: option.color + '15' }]}>
                      <IconComponent color={option.color} size={22} strokeWidth={2} />
                    </View>
                    <View style={styles.optionContent}>
                      <Text style={styles.optionTitle}>{option.title}</Text>
                      <Text style={styles.optionSubtitle}>{option.subtitle}</Text>
                    </View>
                    <ChevronRight color={Colors.light.textLight} size={20} />
                  </TouchableOpacity>
                );
              })}
            </View>
          </View>
        ))}

        <TouchableOpacity 
          style={styles.logoutButton}
          onPress={handleLogout}
          activeOpacity={0.7}
        >
          <LogOut color="#EF4444" size={20} strokeWidth={2} />
          <Text style={styles.logoutText}>Cerrar Sesión</Text>
        </TouchableOpacity>

        <Text style={styles.versionText}>Salonier AI • Versión 1.0.0</Text>
        
        <View style={{ height: 40 }} />
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: 32,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  content: {
    padding: 20,
    gap: 24,
  },
  profileCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 24,
    alignItems: 'center',
    gap: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  avatarContainer: {
    position: 'relative',
  },
  avatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary + '20',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarText: {
    fontSize: 32,
    fontWeight: '700' as const,
    color: Colors.light.primary,
  },
  onlineBadge: {
    position: 'absolute',
    bottom: 2,
    right: 2,
    width: 18,
    height: 18,
    borderRadius: 9,
    backgroundColor: '#10B981',
    borderWidth: 3,
    borderColor: Colors.light.background,
  },
  profileInfo: {
    alignItems: 'center',
    gap: 4,
  },
  profileName: {
    fontSize: 24,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  profileEmail: {
    fontSize: 15,
    color: Colors.light.textSecondary,
  },
  editButton: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    paddingVertical: 12,
    paddingHorizontal: 24,
    marginTop: 8,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  editButtonText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  section: {
    gap: 12,
  },
  sectionTitle: {
    fontSize: 13,
    fontWeight: '700' as const,
    color: Colors.light.textLight,
    textTransform: 'uppercase',
    letterSpacing: 1,
    marginLeft: 4,
  },
  optionsContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  option: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    gap: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  iconContainer: {
    width: 44,
    height: 44,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  optionContent: {
    flex: 1,
    gap: 2,
  },
  optionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  optionSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 18,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 14,
    paddingVertical: 16,
    marginTop: 8,
    borderWidth: 1,
    borderColor: '#FEE2E2',
  },
  logoutText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: '#EF4444',
  },
  versionText: {
    fontSize: 13,
    color: Colors.light.textLight,
    textAlign: 'center',
    marginTop: 8,
  },
});
