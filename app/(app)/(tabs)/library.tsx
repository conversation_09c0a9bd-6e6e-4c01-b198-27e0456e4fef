import React from 'react';
import { StyleSheet, Text, View, ScrollView, TouchableOpacity } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { Palette, FileText, Lightbulb } from 'lucide-react-native';
import Colors from '@/constants/colors';

const libraryCategories = [
  {
    id: '1',
    title: 'Teoría del Color',
    icon: Palette,
    count: 12,
    color: Colors.light.primary,
  },
  {
    id: '2',
    title: 'Fórmulas Guardadas',
    icon: FileText,
    count: 24,
    color: Colors.light.accent,
  },
  {
    id: '3',
    title: 'Tips & Técnicas',
    icon: Lightbulb,
    count: 8,
    color: Colors.light.warning,
  },
];

export default function LibraryScreen() {
  const insets = useSafeAreaInsets();

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top + 20 }]}>
        <Text style={styles.title}>Biblioteca</Text>
        <Text style={styles.subtitle}>Recursos y conocimiento profesional</Text>
      </View>

      <ScrollView contentContainerStyle={styles.content}>
        {libraryCategories.map((category) => {
          const IconComponent = category.icon;
          return (
            <TouchableOpacity key={category.id} style={styles.card}>
              <View style={[styles.iconContainer, { backgroundColor: category.color + '15' }]}>
                <IconComponent color={category.color} size={28} />
              </View>
              <View style={styles.cardContent}>
                <Text style={styles.cardTitle}>{category.title}</Text>
                <Text style={styles.cardCount}>{category.count} recursos</Text>
              </View>
            </TouchableOpacity>
          );
        })}

        <View style={styles.infoCard}>
          <Text style={styles.infoTitle}>💡 Próximamente</Text>
          <Text style={styles.infoText}>
            Accede a tutoriales, guías de marcas, tablas de colorimetría y mucho más.
          </Text>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  header: {
    paddingHorizontal: 20,
    paddingBottom: 20,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  title: {
    fontSize: 32,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
  },
  content: {
    padding: 20,
    gap: 16,
  },
  card: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  iconContainer: {
    width: 56,
    height: 56,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardContent: {
    flex: 1,
    gap: 4,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  cardCount: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  infoCard: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
    padding: 20,
    gap: 8,
    marginTop: 16,
  },
  infoTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  infoText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
});
