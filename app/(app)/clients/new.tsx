import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TextInput,
  TouchableOpacity,
  Switch,
  Alert,
  KeyboardAvoidingView,
  Platform,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Shield, AlertCircle, Bell } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClients } from '@/contexts/ClientContext';
import type { Client } from '@/types';

export default function NewClientScreen() {
  const router = useRouter();
  const { addClient } = useClients();
  const insets = useSafeAreaInsets();

  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    knownAllergies: '',
    isPregnantOrBreastfeeding: false,
    hasSensitiveScalp: false,
    chemicalTreatments: {
      henna: false,
      chemicalStraightening: false,
      keratin: false,
    },
    communicationPreferences: {
      acceptsReminders: true,
      preferredMethod: 'whatsapp' as 'whatsapp' | 'sms',
    },
    additionalNotes: '',
  });

  const updateField = (field: string, value: string | boolean) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const updateChemicalTreatment = (treatment: keyof typeof formData.chemicalTreatments, value: boolean) => {
    setFormData(prev => ({
      ...prev,
      chemicalTreatments: {
        ...prev.chemicalTreatments,
        [treatment]: value,
      },
    }));
  };

  const updateCommunicationPref = (field: string, value: 'whatsapp' | 'sms' | boolean) => {
    setFormData(prev => ({
      ...prev,
      communicationPreferences: {
        ...prev.communicationPreferences,
        [field]: value,
      },
    }));
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      Alert.alert('Error', 'El nombre completo es obligatorio');
      return;
    }

    try {
      const newClient: Omit<Client, 'id'> = {
        name: formData.name.trim(),
        email: formData.email.trim() || undefined,
        phone: formData.phone.trim() || undefined,
        knownAllergies: formData.knownAllergies.trim() || undefined,
        isPregnantOrBreastfeeding: formData.isPregnantOrBreastfeeding,
        hasSensitiveScalp: formData.hasSensitiveScalp,
        chemicalTreatments: formData.chemicalTreatments,
        communicationPreferences: formData.communicationPreferences,
        additionalNotes: formData.additionalNotes.trim() || undefined,
      };

      await addClient(newClient);
      router.back();
    } catch (error) {
      console.error('Error saving client:', error);
      Alert.alert('Error', 'No se pudo guardar el cliente');
    }
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
    >
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        <View style={styles.section}>
          <Text style={styles.label}>Nombre completo *</Text>
          <TextInput
            style={styles.input}
            placeholder="Nombre y apellido"
            placeholderTextColor={Colors.light.textLight}
            value={formData.name}
            onChangeText={(value) => updateField('name', value)}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Email</Text>
          <TextInput
            style={styles.input}
            placeholder="<EMAIL>"
            placeholderTextColor={Colors.light.textLight}
            value={formData.email}
            onChangeText={(value) => updateField('email', value)}
            keyboardType="email-address"
            autoCapitalize="none"
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Teléfono</Text>
          <TextInput
            style={styles.input}
            placeholder="+34 600 000 000"
            placeholderTextColor={Colors.light.textLight}
            value={formData.phone}
            onChangeText={(value) => updateField('phone', value)}
            keyboardType="phone-pad"
          />
        </View>

        <View style={styles.sectionHeader}>
          <Shield color={Colors.light.primary} size={20} />
          <Text style={styles.sectionTitle}>Información de Seguridad</Text>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Alergias conocidas</Text>
          <TextInput
            style={styles.input}
            placeholder="Ej: PPD, amoníaco, níquel..."
            placeholderTextColor={Colors.light.textLight}
            value={formData.knownAllergies}
            onChangeText={(value) => updateField('knownAllergies', value)}
          />
          <Text style={styles.hint}>Esta información es importante para tu seguridad</Text>
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Embarazo o lactancia</Text>
          <Switch
            value={formData.isPregnantOrBreastfeeding}
            onValueChange={(value) => updateField('isPregnantOrBreastfeeding', value)}
            trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
            thumbColor={Colors.light.background}
          />
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Cuero cabelludo sensible</Text>
          <Switch
            value={formData.hasSensitiveScalp}
            onValueChange={(value) => updateField('hasSensitiveScalp', value)}
            trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
            thumbColor={Colors.light.background}
          />
        </View>

        <View style={styles.sectionHeader}>
          <AlertCircle color="#F59E0B" size={20} />
          <Text style={styles.sectionTitle}>Tratamientos Químicos Activos</Text>
        </View>

        <View style={styles.checkboxContainer}>
          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => updateChemicalTreatment('henna', !formData.chemicalTreatments.henna)}
          >
            <View style={[styles.checkboxBox, formData.chemicalTreatments.henna && styles.checkboxBoxChecked]}>
              {formData.chemicalTreatments.henna && <Text style={styles.checkmark}>✓</Text>}
            </View>
            <Text style={styles.checkboxLabel}>Henna</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => updateChemicalTreatment('chemicalStraightening', !formData.chemicalTreatments.chemicalStraightening)}
          >
            <View style={[styles.checkboxBox, formData.chemicalTreatments.chemicalStraightening && styles.checkboxBoxChecked]}>
              {formData.chemicalTreatments.chemicalStraightening && <Text style={styles.checkmark}>✓</Text>}
            </View>
            <Text style={styles.checkboxLabel}>Alisado químico</Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={styles.checkbox}
            onPress={() => updateChemicalTreatment('keratin', !formData.chemicalTreatments.keratin)}
          >
            <View style={[styles.checkboxBox, formData.chemicalTreatments.keratin && styles.checkboxBoxChecked]}>
              {formData.chemicalTreatments.keratin && <Text style={styles.checkmark}>✓</Text>}
            </View>
            <Text style={styles.checkboxLabel}>Keratina o Botox capilar</Text>
          </TouchableOpacity>
        </View>

        <View style={styles.sectionHeader}>
          <Bell color={Colors.light.primary} size={20} />
          <Text style={styles.sectionTitle}>Preferencias de Comunicación</Text>
        </View>

        <View style={styles.switchRow}>
          <Text style={styles.switchLabel}>Acepta recordatorios de mantenimiento</Text>
          <Switch
            value={formData.communicationPreferences.acceptsReminders}
            onValueChange={(value) => updateCommunicationPref('acceptsReminders', value)}
            trackColor={{ false: Colors.light.border, true: Colors.light.primary }}
            thumbColor={Colors.light.background}
          />
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Método preferido:</Text>
          <View style={styles.radioGroup}>
            <TouchableOpacity
              style={styles.radioOption}
              onPress={() => updateCommunicationPref('preferredMethod', 'whatsapp')}
            >
              <View style={styles.radio}>
                {formData.communicationPreferences.preferredMethod === 'whatsapp' && (
                  <View style={styles.radioSelected} />
                )}
              </View>
              <Text style={styles.radioLabel}>WhatsApp</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={styles.radioOption}
              onPress={() => updateCommunicationPref('preferredMethod', 'sms')}
            >
              <View style={styles.radio}>
                {formData.communicationPreferences.preferredMethod === 'sms' && (
                  <View style={styles.radioSelected} />
                )}
              </View>
              <Text style={styles.radioLabel}>SMS</Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.section}>
          <Text style={styles.label}>Notas adicionales</Text>
          <TextInput
            style={[styles.input, styles.textArea]}
            placeholder="Cualquier otra información relevante..."
            placeholderTextColor={Colors.light.textLight}
            value={formData.additionalNotes}
            onChangeText={(value) => updateField('additionalNotes', value)}
            multiline
            numberOfLines={4}
            textAlignVertical="top"
          />
        </View>
      </ScrollView>

      <View style={[styles.footer, { paddingBottom: insets.bottom + 20 }]}>
        <TouchableOpacity
          style={styles.cancelButton}
          onPress={() => router.back()}
        >
          <Text style={styles.cancelButtonText}>Cancelar</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.saveButton}
          onPress={handleSave}
        >
          <Text style={styles.saveButtonText}>Guardar Cliente</Text>
        </TouchableOpacity>
      </View>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 20,
    paddingBottom: 100,
  },
  section: {
    marginBottom: 24,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
    marginTop: 8,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  label: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 8,
  },
  input: {
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 14,
    fontSize: 16,
    color: Colors.light.text,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  textArea: {
    minHeight: 100,
    paddingTop: 14,
  },
  hint: {
    fontSize: 12,
    color: Colors.light.textLight,
    marginTop: 6,
  },
  switchRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  switchLabel: {
    fontSize: 16,
    color: Colors.light.text,
    flex: 1,
  },
  checkboxContainer: {
    gap: 16,
    marginBottom: 24,
  },
  checkbox: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  checkboxBox: {
    width: 24,
    height: 24,
    borderRadius: 6,
    borderWidth: 2,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkboxBoxChecked: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  checkmark: {
    color: Colors.light.background,
    fontSize: 16,
    fontWeight: '700' as const,
  },
  checkboxLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  radioGroup: {
    gap: 16,
  },
  radioOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  radio: {
    width: 24,
    height: 24,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  radioSelected: {
    width: 12,
    height: 12,
    borderRadius: 6,
    backgroundColor: Colors.light.primary,
  },
  radioLabel: {
    fontSize: 16,
    color: Colors.light.text,
  },
  footer: {
    flexDirection: 'row',
    padding: 20,
    gap: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  cancelButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  cancelButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  saveButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.primary,
  },
  saveButtonText: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.background,
  },
});
