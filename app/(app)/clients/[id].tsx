/**
 * Client Detail Screen
 * Shows client info and formula history
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  Modal,
  TextInput,
  Alert,
  Share,
  Clipboard,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import {
  ArrowLeft,
  Edit2,
  Sparkles,
  Phone,
  Mail,
  Calendar,
  Plus,
  Copy,
  Share2,
  X,
  RefreshCw,
} from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClients } from '@/contexts/ClientContext';
import { useFormula } from '@/contexts/FormulaContext';
import { getClientFormulas, addFormulaNote, getFormulaNotes } from '@/lib/supabase-formulas';
import type { Formula, FormulaNotes } from '@/types';
import FormattedMessageContent from '@/components/chat/FormattedMessageContent';

export default function ClientDetailScreen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { clients } = useClients();
  const { selectClient } = useFormula();

  const client = clients.find((c) => c.id === id);

  const [formulas, setFormulas] = useState<Formula[]>([]);
  const [isLoadingFormulas, setIsLoadingFormulas] = useState(true);
  const [selectedFormula, setSelectedFormula] = useState<Formula | null>(null);
  const [showFormulaModal, setShowFormulaModal] = useState(false);
  const [noteText, setNoteText] = useState('');
  const [formulaNotes, setFormulaNotes] = useState<FormulaNotes[]>([]);
  const [isAddingNote, setIsAddingNote] = useState(false);

  const loadFormulas = useCallback(async () => {
    if (!client) return;

    setIsLoadingFormulas(true);
    try {
      const clientFormulas = await getClientFormulas(client.id);
      setFormulas(clientFormulas);
    } catch (error) {
      console.error('[ClientDetail] Error loading formulas:', error);
      Alert.alert('Error', 'No se pudieron cargar las fórmulas');
    } finally {
      setIsLoadingFormulas(false);
    }
  }, [client]);

  useEffect(() => {
    loadFormulas();
  }, [loadFormulas]);

  const loadNotes = async (formulaId: string) => {
    try {
      const notes = await getFormulaNotes(formulaId);
      setFormulaNotes(notes);
    } catch (error) {
      console.error('[ClientDetail] Error loading notes:', error);
    }
  };

  const handleViewFormula = async (formula: Formula) => {
    setSelectedFormula(formula);
    setShowFormulaModal(true);
    await loadNotes(formula.id);
  };

  const handleStartNewFormula = () => {
    if (!client) return;
    selectClient(client);
    router.push('/formula/step1');
  };

  const handleRepeatFormula = () => {
    if (!client || !selectedFormula) return;

    // Navigate to formula creation with this formula as reference
    selectClient(client);
    // TODO: Pre-fill step4 with same brand/product line
    router.push('/formula/step1');
    setShowFormulaModal(false);
  };

  const handleCopyFormula = () => {
    if (!selectedFormula) return;

    Clipboard.setString(selectedFormula.formulaText);
    Alert.alert('Copiado', 'Fórmula copiada al portapapeles');
  };

  const handleShareFormula = async () => {
    if (!selectedFormula) return;

    try {
      await Share.share({
        message: `📋 Fórmula para ${client?.name}\n\n${selectedFormula.formulaText}`,
      });
    } catch (error) {
      console.error('[ClientDetail] Error sharing:', error);
    }
  };

  const handleAddNote = async () => {
    if (!selectedFormula || !noteText.trim()) return;

    setIsAddingNote(true);
    try {
      const { supabase } = await import('@/lib/supabase');
      const { data: userData } = await supabase.auth.getUser();

      if (!userData.user) {
        Alert.alert('Error', 'Debes iniciar sesión para agregar notas');
        return;
      }

      await addFormulaNote({
        formulaId: selectedFormula.id,
        userId: userData.user.id,
        noteText: noteText.trim(),
        noteType: 'observation',
      });

      setNoteText('');
      await loadNotes(selectedFormula.id);
      Alert.alert('Nota agregada', 'La nota se guardó correctamente');
    } catch (error) {
      console.error('[ClientDetail] Error adding note:', error);
      Alert.alert('Error', 'No se pudo guardar la nota');
    } finally {
      setIsAddingNote(false);
    }
  };

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('es-ES', {
      day: 'numeric',
      month: 'short',
      year: 'numeric'
    });
  };

  const getServiceTypeLabel = (type: string): string => {
    const labels: Record<string, string> = {
      color: '🎨 Color',
      highlights: '✨ Mechas',
      balayage: '🌊 Balayage',
      toner: '💜 Toner',
      correction: '🔧 Corrección',
    };
    return labels[type] || type;
  };

  if (!client) {
    return (
      <View style={[styles.container, styles.centerContent]}>
        <Text style={styles.errorText}>Cliente no encontrado</Text>
        <TouchableOpacity
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Volver</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={[styles.header, { paddingTop: insets.top + 12 }]}>
        <View style={styles.headerRow}>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.back()}
          >
            <ArrowLeft size={24} color={Colors.light.text} strokeWidth={2} />
          </TouchableOpacity>
          <Text style={styles.headerTitle}>Detalle del Cliente</Text>
          <TouchableOpacity
            style={styles.iconButton}
            onPress={() => router.push(`/clients/edit?id=${client.id}`)}
          >
            <Edit2 size={20} color={Colors.light.primary} strokeWidth={2} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView
        style={styles.scrollView}
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        {/* Client Info Card */}
        <View style={styles.clientCard}>
          <View style={styles.clientAvatar}>
            <Text style={styles.clientInitial}>{client.name.charAt(0).toUpperCase()}</Text>
          </View>
          <Text style={styles.clientName}>{client.name}</Text>

          {client.phone && (
            <View style={styles.infoRow}>
              <Phone size={16} color={Colors.light.textSecondary} strokeWidth={2} />
              <Text style={styles.infoText}>{client.phone}</Text>
            </View>
          )}

          {client.email && (
            <View style={styles.infoRow}>
              <Mail size={16} color={Colors.light.textSecondary} strokeWidth={2} />
              <Text style={styles.infoText}>{client.email}</Text>
            </View>
          )}

          {client.lastVisit && (
            <View style={styles.infoRow}>
              <Calendar size={16} color={Colors.light.textSecondary} strokeWidth={2} />
              <Text style={styles.infoText}>Última visita: {formatDate(client.lastVisit)}</Text>
            </View>
          )}

          {client.notes && (
            <View style={styles.notesContainer}>
              <Text style={styles.notesText}>{client.notes}</Text>
            </View>
          )}

          <TouchableOpacity
            style={styles.newFormulaButton}
            onPress={handleStartNewFormula}
          >
            <Sparkles size={20} color={Colors.light.background} strokeWidth={2} />
            <Text style={styles.newFormulaButtonText}>Nueva Fórmula</Text>
          </TouchableOpacity>
        </View>

        {/* Formula History */}
        <View style={styles.historySection}>
          <Text style={styles.sectionTitle}>
            📋 Historial de Fórmulas {formulas.length > 0 && `(${formulas.length})`}
          </Text>

          {isLoadingFormulas ? (
            <View style={styles.loadingContainer}>
              <ActivityIndicator size="large" color={Colors.light.primary} />
              <Text style={styles.loadingText}>Cargando fórmulas...</Text>
            </View>
          ) : formulas.length === 0 ? (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>Aún no hay fórmulas para este cliente</Text>
              <Text style={styles.emptySubtext}>
                Crea la primera fórmula tocando &quot;Nueva Fórmula&quot;
              </Text>
            </View>
          ) : (
            formulas.map((formula) => (
              <TouchableOpacity
                key={formula.id}
                style={styles.formulaCard}
                onPress={() => handleViewFormula(formula)}
              >
                <View style={styles.formulaHeader}>
                  <Text style={styles.formulaType}>
                    {getServiceTypeLabel(formula.serviceType)}
                  </Text>
                  <Text style={styles.formulaDate}>{formatDate(formula.createdAt)}</Text>
                </View>

                <Text style={styles.formulaBrand}>
                  {formula.brand} {formula.productLine && `• ${formula.productLine}`}
                </Text>

                <Text style={styles.formulaSession}>Sesión {formula.sessionNumber}</Text>

                {formula.currentLevel && formula.targetLevel && (
                  <Text style={styles.formulaLevels}>
                    Nivel {formula.currentLevel} → {formula.targetLevel}
                  </Text>
                )}

                <View style={styles.formulaFooter}>
                  <Text style={styles.viewFormulaText}>Ver fórmula completa →</Text>
                </View>
              </TouchableOpacity>
            ))
          )}
        </View>
      </ScrollView>

      {/* Formula View Modal */}
      <Modal visible={showFormulaModal} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { paddingTop: insets.top }]}>
          <View style={styles.modalHeader}>
            <Text style={styles.modalTitle}>Fórmula Completa</Text>
            <TouchableOpacity onPress={() => setShowFormulaModal(false)}>
              <X size={24} color={Colors.light.text} strokeWidth={2} />
            </TouchableOpacity>
          </View>

          {selectedFormula && (
            <>
              <ScrollView
                style={styles.modalContent}
                contentContainerStyle={styles.modalScrollContent}
              >
                <View style={styles.formulaMetadata}>
                  <Text style={styles.metadataItem}>
                    {getServiceTypeLabel(selectedFormula.serviceType)} • {selectedFormula.brand}
                  </Text>
                  <Text style={styles.metadataItem}>
                    {formatDate(selectedFormula.createdAt)}
                  </Text>
                </View>

                <View style={styles.formulaContent}>
                  <FormattedMessageContent
                    content={selectedFormula.formulaText}
                    tone="assistant"
                  />
                </View>

                {/* Notes Section */}
                {formulaNotes.length > 0 && (
                  <View style={styles.notesSection}>
                    <Text style={styles.notesSectionTitle}>📝 Notas personales</Text>
                    {formulaNotes.map((note) => (
                      <View key={note.id} style={styles.noteCard}>
                        <Text style={styles.noteText}>{note.noteText}</Text>
                        <Text style={styles.noteDate}>
                          {formatDate(note.createdAt)}
                        </Text>
                      </View>
                    ))}
                  </View>
                )}

                {/* Add Note Input */}
                <View style={styles.addNoteSection}>
                  <Text style={styles.addNoteTitle}>Agregar nota:</Text>
                  <TextInput
                    style={styles.noteInput}
                    placeholder="Ej: La cliente quedó muy satisfecha. Próxima cita en 6 semanas."
                    placeholderTextColor={Colors.light.textLight}
                    value={noteText}
                    onChangeText={setNoteText}
                    multiline
                    maxLength={500}
                  />
                  <TouchableOpacity
                    style={[styles.addNoteButton, (!noteText.trim() || isAddingNote) && styles.addNoteButtonDisabled]}
                    onPress={handleAddNote}
                    disabled={!noteText.trim() || isAddingNote}
                  >
                    {isAddingNote ? (
                      <ActivityIndicator size="small" color={Colors.light.background} />
                    ) : (
                      <>
                        <Plus size={18} color={Colors.light.background} strokeWidth={2} />
                        <Text style={styles.addNoteButtonText}>Agregar Nota</Text>
                      </>
                    )}
                  </TouchableOpacity>
                </View>
              </ScrollView>

              {/* Action Buttons */}
              <View style={[styles.modalActions, { paddingBottom: insets.bottom || 16 }]}>
                <TouchableOpacity style={styles.actionButton} onPress={handleCopyFormula}>
                  <Copy size={18} color={Colors.light.primary} strokeWidth={2} />
                  <Text style={styles.actionButtonText}>Copiar</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.actionButton} onPress={handleShareFormula}>
                  <Share2 size={18} color={Colors.light.primary} strokeWidth={2} />
                  <Text style={styles.actionButtonText}>Compartir</Text>
                </TouchableOpacity>

                <TouchableOpacity style={styles.actionButton} onPress={handleRepeatFormula}>
                  <RefreshCw size={18} color={Colors.light.primary} strokeWidth={2} />
                  <Text style={styles.actionButtonText}>Repetir</Text>
                </TouchableOpacity>
              </View>
            </>
          )}
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  centerContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },

  // Header
  header: {
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingHorizontal: 16,
    paddingBottom: 12,
  },
  headerRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },

  // ScrollView
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
  },

  // Client Info Card
  clientCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 20,
    padding: 20,
    alignItems: 'center',
    marginBottom: 24,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
  },
  clientAvatar: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: Colors.light.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 12,
  },
  clientInitial: {
    fontSize: 32,
    fontWeight: '700',
    color: Colors.light.background,
  },
  clientName: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 16,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 8,
  },
  infoText: {
    fontSize: 15,
    color: Colors.light.textSecondary,
  },
  notesContainer: {
    marginTop: 12,
    padding: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    width: '100%',
  },
  notesText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
  },
  newFormulaButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: Colors.light.primary,
    paddingVertical: 14,
    paddingHorizontal: 24,
    borderRadius: 12,
    marginTop: 16,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 3,
  },
  newFormulaButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.background,
  },

  // History Section
  historySection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 16,
  },
  loadingContainer: {
    padding: 40,
    alignItems: 'center',
  },
  loadingText: {
    marginTop: 12,
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  emptyContainer: {
    padding: 40,
    alignItems: 'center',
    backgroundColor: Colors.light.card,
    borderRadius: 16,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },

  // Formula Cards
  formulaCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 16,
    padding: 16,
    marginBottom: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  formulaHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  formulaType: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
  },
  formulaDate: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  formulaBrand: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  formulaSession: {
    fontSize: 14,
    color: Colors.light.primary,
    fontWeight: '600',
    marginBottom: 4,
  },
  formulaLevels: {
    fontSize: 14,
    color: Colors.light.text,
    marginBottom: 8,
  },
  formulaFooter: {
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingTop: 12,
    marginTop: 8,
  },
  viewFormulaText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },

  // Modal
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.light.text,
  },
  modalContent: {
    flex: 1,
  },
  modalScrollContent: {
    padding: 20,
  },
  formulaMetadata: {
    marginBottom: 16,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  metadataItem: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    marginBottom: 4,
  },
  formulaContent: {
    marginBottom: 24,
  },

  // Notes
  notesSection: {
    marginBottom: 24,
  },
  notesSectionTitle: {
    fontSize: 16,
    fontWeight: '700',
    color: Colors.light.text,
    marginBottom: 12,
  },
  noteCard: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
  },
  noteText: {
    fontSize: 14,
    color: Colors.light.text,
    lineHeight: 20,
    marginBottom: 6,
  },
  noteDate: {
    fontSize: 12,
    color: Colors.light.textSecondary,
  },

  // Add Note
  addNoteSection: {
    marginBottom: 24,
  },
  addNoteTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.text,
    marginBottom: 8,
  },
  noteInput: {
    backgroundColor: Colors.light.card,
    borderRadius: 12,
    padding: 12,
    fontSize: 14,
    color: Colors.light.text,
    minHeight: 80,
    textAlignVertical: 'top',
    marginBottom: 12,
  },
  addNoteButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    borderRadius: 12,
  },
  addNoteButtonDisabled: {
    opacity: 0.5,
  },
  addNoteButtonText: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.light.background,
  },

  // Modal Actions
  modalActions: {
    flexDirection: 'row',
    gap: 12,
    paddingHorizontal: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 6,
    backgroundColor: Colors.light.card,
    paddingVertical: 12,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  actionButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.light.primary,
  },

  // Error
  errorText: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 16,
  },
  backButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 12,
  },
  backButtonText: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.light.background,
  },
});
