/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useState, useEffect, useRef, useMemo } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  ActivityIndicator,
  KeyboardAvoidingView,
  Platform,
  TextInput,
  FlatList,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Download, Share2, Send, MessageCircle, User, Clock, Droplet, AlertCircle, CheckCircle2, PackageOpen, ArrowRight, Target, BookOpenCheck, Sparkles, ShieldAlert, ShieldCheck, ChevronDown, ChevronUp, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useFormula } from '@/contexts/FormulaContext';
import { generateTextSafe, getErrorMessage } from '@/lib/ai-client';
import { uploadFormulaPhoto } from '@/lib/storage';
import type { Message } from '@/types';
import FormattedMessageContent from '@/components/chat/FormattedMessageContent';
import {
  getFormulaSystemPrompt,
  getFormulaUserPrompt,
  getChatSystemPrompt,
  getQuickQuestions,
  type FormulaPromptContext
} from '@/lib/formula-prompts';
import {
  saveFormula,
  extractProductsFromText,
  getLatestSessionNumber
} from '@/lib/supabase-formulas';

type GenerationStage = 'analysis' | 'validation' | 'references' | 'drafting' | 'done';
type StageId = Exclude<GenerationStage, 'done'>;

type StepContentItem = {
  type: 'text' | 'product';
  text: string;
  explanation?: string;
};

type ParsedSessionStep = {
  title: string;
  content: StepContentItem[];
};

type ParsedSession = {
  number: number;
  title: string;
  steps: ParsedSessionStep[];
};

type ParsedFormulaStructure = {
  preliminarySections: ParsedSessionStep[];
  sessions: ParsedSession[];
};

type ProductSummaryItem = {
  name: string;
  reasons: string[];
  sessions: string[];
  stepHints: string[];
};

type CollapsibleSectionKey =
  | 'preflight'
  | 'kit'
  | 'journey'
  | 'overview'
  | 'plan'
  | 'insights'
  | 'checkpoints';

type IconRenderer = React.ComponentType<{ size: number; color: string; strokeWidth: number }>;

const formatLineWithExplanation = (item: StepContentItem) => {
  if (!item.text) return '';
  return item.explanation
    ? `${item.text} — ${item.explanation}`
    : item.text;
};

const parseFormulaStructure = (text: string): ParsedFormulaStructure => {
  if (!text || text.trim().length === 0) {
    return {
      preliminarySections: [],
      sessions: [],
    };
  }

  const lines = text.split('\n');

  const preliminarySections: ParsedSessionStep[] = [];
  const sessions: ParsedSession[] = [];

  let currentSession: ParsedSession | null = null;
  let currentStep: ParsedSessionStep | null = null;
  let pendingProductExplanation = '';

  const splitLineWithExplanation = (line: string) => {
    const segments = line
      .split(/\s[—–-]\s/)
      .map(part => part.trim())
      .filter(Boolean);

    if (segments.length === 0) {
      return { primary: '', secondary: undefined as string | undefined };
    }

    if (segments.length === 1) {
      return { primary: segments[0], secondary: undefined as string | undefined };
    }

    if (segments.length === 2) {
      return { primary: segments[0], secondary: segments[1] };
    }

    return {
      primary: `${segments[0]} · ${segments[1]}`,
      secondary: segments.slice(2).join(' — '),
    };
  };

  const flushStep = () => {
    if (!currentStep || currentStep.content.length === 0) return;

    if (currentSession) {
      currentSession.steps.push(currentStep);
    } else {
      preliminarySections.push(currentStep);
    }

    currentStep = null;
    pendingProductExplanation = '';
  };

  const flushSession = () => {
    flushStep();
    if (currentSession && currentSession.steps.length > 0) {
      sessions.push(currentSession);
      currentSession = null;
    }
  };

  for (let i = 0; i < lines.length; i++) {
    const trimmed = lines[i].trim();

    if (!trimmed) {
      if (pendingProductExplanation) {
        pendingProductExplanation = '';
      }
      continue;
    }

    const sessionMatch = trimmed.match(/^#{2,3}\s*SESI[ÓO]N\s+(\d+)/i);
    if (sessionMatch) {
      flushSession();

      const sessionNumber = parseInt(sessionMatch[1], 10);
      const restOfTitle = trimmed.replace(/^#{2,3}\s*SESI[ÓO]N\s+\d+:?\s*/i, '').trim();

      currentSession = {
        number: sessionNumber,
        title: restOfTitle || `Sesión ${sessionNumber}`,
        steps: [],
      };
      continue;
    }

    const headerMatch = trimmed.match(/^#{1,3}\s+(.+)|^([A-ZÁÉÍÓÚÑ][A-ZÁÉÍÓÚ\s]+):$/);
    if (headerMatch) {
      flushStep();

      const stepTitle = (headerMatch[1] || headerMatch[2])
        .replace(/^\d+\.\s*/, '')
        .replace(/:$/, '')
        .trim();

      currentStep = {
        title: stepTitle,
        content: [],
      };
      continue;
    }

    if (!currentStep) continue;

    const isBullet = /^[-•*]\s/.test(trimmed);
    const cleanLine = trimmed
      .replace(/^[-•*]\s*/, '')
      .replace(/^\d+\.\s*/, '')
      .replace(/\*\*/g, '')
      .replace(/__/g, '')
      .trim();

    if (!cleanLine) continue;

    const isProductSection = currentStep.title.toUpperCase().includes('PRODUCTO');

    if (isBullet && isProductSection) {
      const hasParenthesis = cleanLine.includes('(');
      const hasDash = cleanLine.includes('–') || cleanLine.includes('-');
      const startsWithCapital = /^[A-ZÁÉÍÓÚÑ]/.test(cleanLine);
      const hasUnits = cleanLine.includes('ml') || cleanLine.includes('g') || cleanLine.includes('oz');

      if (startsWithCapital && (hasParenthesis || hasDash || hasUnits)) {
        if (pendingProductExplanation && currentStep.content.length > 0) {
          const lastItem = currentStep.content[currentStep.content.length - 1];
          if (lastItem.type === 'product') {
            lastItem.explanation = lastItem.explanation
              ? `${lastItem.explanation} ${pendingProductExplanation}`.trim()
              : pendingProductExplanation;
          }
        }

        const { primary, secondary } = splitLineWithExplanation(cleanLine);

        currentStep.content.push({
          type: 'product',
          text: primary || cleanLine,
          explanation: secondary,
        });
        pendingProductExplanation = '';
      } else {
        if (pendingProductExplanation) {
          pendingProductExplanation += ' ' + cleanLine;
        } else {
          pendingProductExplanation = cleanLine;
        }
      }
    } else {
      const { primary, secondary } = splitLineWithExplanation(cleanLine);

      currentStep.content.push({
        type: 'text',
        text: primary || cleanLine,
        explanation: secondary,
      });
    }
  }

  if (pendingProductExplanation && currentStep && currentStep.content.length > 0) {
    const lastItem = currentStep.content[currentStep.content.length - 1];
    if (lastItem.type === 'product') {
      lastItem.explanation = lastItem.explanation
        ? `${lastItem.explanation} ${pendingProductExplanation}`.trim()
        : pendingProductExplanation;
    }
  }

  flushSession();

  return {
    preliminarySections,
    sessions,
  };
};

// AI Configuration Constants
const MAX_CONVERSATION_CONTEXT_MESSAGES = 8; // ~4 conversation turns (user + assistant pairs)
const FORMULA_GENERATION_TIMEOUT = 60000; // 60s (GPT-4o typically responds in <40s)
const CHAT_TIMEOUT = 45000; // 45s for Q&A chat responses

export default function Step5Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { formulaData, selectedClient, resetFormula } = useFormula();
  const chatListRef = useRef<FlatList>(null);
  const mixingRatioCacheRef = useRef(new Map<string, string>());

  const currentAnalysis = formulaData.currentColorAnalysis;
  const desiredAnalysis = formulaData.desiredColorAnalysis;
  const brand = formulaData.brand;
  const productLine = formulaData.productLine;
  const clientName = formulaData.clientName;
  const technique = formulaData.technique;

  const [formula, setFormula] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(true);
  const [showChat, setShowChat] = useState(false);
  const [chatMessages, setChatMessages] = useState<Message[]>([]);
  const [inputText, setInputText] = useState('');
  const [isSending, setIsSending] = useState(false);
  const [isUploadingPhotos, setIsUploadingPhotos] = useState(false);
  const [generationStage, setGenerationStage] = useState<GenerationStage>('analysis');
  const [mixingRatioDetails, setMixingRatioDetails] = useState<string | null>(null);
  const [validationChecklist, setValidationChecklist] = useState<string[]>([]);
  const [collapsedSections, setCollapsedSections] = useState<Record<CollapsibleSectionKey, boolean>>({
    preflight: false,
    kit: false,
    journey: false,
    overview: false,
    plan: false,
    insights: false,
    checkpoints: false,
  });

  const parsedFormula = useMemo(() => parseFormulaStructure(formula), [formula]);

  const isSectionCollapsed = (key: CollapsibleSectionKey) => collapsedSections[key];
  const toggleSection = (key: CollapsibleSectionKey) => {
    setCollapsedSections(prev => ({
      ...prev,
      [key]: !prev[key],
    }));
  };

  const hasStructuredFormula = useMemo(() => {
    if (!formula || formula.trim().length === 0) {
      return false;
    }

    if (parsedFormula.preliminarySections.length > 0) {
      return true;
    }

    return parsedFormula.sessions.some(session => session.steps.length > 0);
  }, [formula, parsedFormula]);

  const executiveSummarySection = useMemo(() => {
    return parsedFormula.preliminarySections.find(section =>
      section.title.toUpperCase().includes('RESUMEN')
    );
  }, [parsedFormula]);

  const diagnosticSection = useMemo(() => {
    return parsedFormula.preliminarySections.find(section =>
      section.title.toUpperCase().includes('DIAGNÓSTICO') ||
      section.title.toUpperCase().includes('DIAGNOSTICO')
    );
  }, [parsedFormula]);

  const strategySection = useMemo(() => {
    return parsedFormula.preliminarySections.find(section => {
      const upper = section.title.toUpperCase();
      return upper.includes('ESTRATEGIA') || upper.includes('TRANSFORMACIÓN') || upper.includes('TRANSFORMACION');
    });
  }, [parsedFormula]);

  const strategyHighlights = useMemo(() => {
    if (!strategySection) return [] as string[];

    return strategySection.content
      .filter(item => item.type === 'text' && item.text)
      .map(item => formatLineWithExplanation(item))
      .slice(0, 3);
  }, [strategySection]);

  const diagnosticHighlights = useMemo(() => {
    if (!diagnosticSection) return [] as string[];

    return diagnosticSection.content
      .filter(item => item.type === 'text' && item.text)
      .map(item => formatLineWithExplanation(item))
      .slice(0, 4);
  }, [diagnosticSection]);

  const executiveSummaryDetails = useMemo(() => {
    if (!executiveSummarySection) {
      return {
        objective: null as string | null,
        sessions: null as string | null,
        riskNotes: [] as string[],
        guidelines: [] as string[],
      };
    }

    const details = {
      objective: null as string | null,
      sessions: null as string | null,
      riskNotes: [] as string[],
      guidelines: [] as string[],
    };

    executiveSummarySection.content
      .filter(item => item.type === 'text' && item.text)
      .forEach(item => {
        const line = formatLineWithExplanation(item).trim();
        if (!line) return;

        const colonIndex = line.indexOf(':');
        const label = colonIndex !== -1 ? line.slice(0, colonIndex).trim().toLowerCase() : '';
        const value = colonIndex !== -1 ? line.slice(colonIndex + 1).trim() : line;

        if (label.includes('objetivo')) {
          details.objective = value || line;
        } else if (label.includes('sesiones') || label.includes('ritmo')) {
          details.sessions = value || line;
        } else if (label.includes('riesgo') || label.includes('alerta')) {
          details.riskNotes.push(value || line);
        } else {
          details.guidelines.push(value || line);
        }
      });

    return details;
  }, [executiveSummarySection]);

  const totalSessions = parsedFormula.sessions.length;

  const sessionHighlights = useMemo(() => parsedFormula.sessions.map(session => {
    const objectiveStep = session.steps.find(step => {
      const upper = step.title.toUpperCase();
      return upper.includes('OBJETIVO');
    });

    const highlightItem = objectiveStep?.content.find(item => item.type === 'text');
    const highlightText = highlightItem ? formatLineWithExplanation(highlightItem) : undefined;

    return {
      number: session.number,
      title: session.title,
      highlight: highlightText,
    };
  }), [parsedFormula]);

  const preliminarySectionsToRender = useMemo(() => (
    parsedFormula.preliminarySections.filter(section => section !== executiveSummarySection)
  ), [parsedFormula, executiveSummarySection]);

  const productSummaryItems = useMemo(() => {
    const productMap = new Map<string, ProductSummaryItem>();

    const normalizeKey = (value: string) => value.trim().toLowerCase();
    const registerProduct = (
      name: string,
      explanation?: string,
      sessionLabel?: string,
      stepTitle?: string,
    ) => {
      const key = normalizeKey(name);
      if (!productMap.has(key)) {
        productMap.set(key, {
          name: name.trim(),
          reasons: [],
          sessions: [],
          stepHints: [],
        });
      }
      const entry = productMap.get(key) as ProductSummaryItem;

      if (explanation && !entry.reasons.includes(explanation)) {
        entry.reasons.push(explanation);
      }

      if (sessionLabel && !entry.sessions.includes(sessionLabel)) {
        entry.sessions.push(sessionLabel);
      }

      if (stepTitle && !entry.stepHints.includes(stepTitle)) {
        entry.stepHints.push(stepTitle);
      }
    };

    const processStep = (step: ParsedSessionStep, sessionLabel?: string) => {
      step.content.forEach(item => {
        if (item.type === 'product') {
          registerProduct(item.text, item.explanation, sessionLabel, step.title);
        }
      });
    };

    preliminarySectionsToRender.forEach(section => processStep(section));

    parsedFormula.sessions.forEach(session => {
      const sessionLabel = session.title
        ? `${session.title} (${session.number > 0 ? `Sesión ${session.number}` : 'Sesión'})`
        : `Sesión ${session.number}`;

      session.steps.forEach(step => processStep(step, sessionLabel));
    });

    return Array.from(productMap.values()).map(item => ({
      ...item,
      reasons: item.reasons.slice(0, 2),
      sessions: item.sessions.slice(0, 3),
      stepHints: item.stepHints.slice(0, 2),
    }));
  }, [parsedFormula, preliminarySectionsToRender]);

  const learningShortcuts = useMemo(() => {
    const brandContext = brand
      ? productLine
        ? `${brand} ${productLine}`
        : brand
      : 'la marca seleccionada';

    return [
      {
        id: 'bands',
        title: 'Bandas impecables',
        subtitle: 'Guía para equilibrarlas sin sobreprocesar.',
        prompt: `Necesito un checklist accionable para suavizar bandas de demarcación usando ${brandContext}. Incluye preparación, mezcla sugerida, tiempos y señales para detenerse.`,
      },
      {
        id: 'saturation',
        title: 'Control de saturación',
        subtitle: 'Qué hacer si el color se calienta demasiado rápido.',
        prompt: `Dame recomendaciones avanzadas para manejar saturación rápida durante la aplicación con ${brandContext}. Incluye ajustes de sección, mezcla y tiempos de control.`,
      },
      {
        id: 'sealing',
        title: 'Sellado y aprendizaje',
        subtitle: 'Cómo cerrar color y enseñar al cliente.',
        prompt: `Explica paso a paso cómo sellar la cutícula y educar al cliente después de esta formulación con ${brandContext}. Añade tips para próximos servicios y aprendizaje clave.`,
      },
    ];
  }, [brand, productLine]);

  const sessionCheckpoints = useMemo(() => {
    const checkpoints: { id: string; title: string; items: string[] }[] = [];

    const globalItems = [...executiveSummaryDetails.riskNotes, ...executiveSummaryDetails.guidelines]
      .map(line => line.trim())
      .filter(Boolean)
      .slice(0, 4);

    if (globalItems.length > 0) {
      checkpoints.push({
        id: 'global',
        title: 'Antes de empezar',
        items: globalItems,
      });
    }

    parsedFormula.sessions.forEach(session => {
      const collected: string[] = [];

      session.steps.forEach(step => {
        const upper = step.title.toUpperCase();
        const isControlStep =
          upper.includes('CONTROL') ||
          upper.includes('TIEMPO') ||
          upper.includes('CHECKPOINT') ||
          upper.includes('VIGILAR') ||
          upper.includes('SEÑAL') ||
          upper.includes('SIGNO') ||
          upper.includes('RIESGO');

        if (isControlStep) {
          step.content.forEach(item => {
            const text = formatLineWithExplanation(item);
            if (text && !collected.includes(text)) {
              collected.push(text);
            }
          });
        }
      });

      if (collected.length === 0) {
        const fallbackStep = session.steps.find(step => {
          const upper = step.title.toUpperCase();
          return upper.includes('OBJETIVO') || upper.includes('TÉCNICA') || upper.includes('TECNICA');
        });

        fallbackStep?.content.forEach(item => {
          if (collected.length >= 3) return;
          const text = formatLineWithExplanation(item);
          if (text && !collected.includes(text)) {
            collected.push(text);
          }
        });
      }

      const limited = collected.slice(0, 4);
      if (limited.length > 0) {
        checkpoints.push({
          id: `session-${session.number}`,
          title: session.title || `Sesión ${session.number}`,
          items: limited,
        });
      }
    });

    return checkpoints;
  }, [parsedFormula, executiveSummaryDetails]);

  const stageOrder: StageId[] = ['analysis', 'validation', 'references', 'drafting'];
  const generationSteps = [
    {
      id: 'analysis' as const,
      label: 'Analizando diagnóstico actual',
      hint: 'Evaluando estado de fibra, porosidad y antecedentes',
    },
    {
      id: 'validation' as const,
      label: 'Validando viabilidad y seguridad',
      hint: 'Cruce de diagnóstico vs. objetivo y riesgos detectados',
    },
    {
      id: 'references' as const,
      label: 'Confirmando manual y catálogo oficial',
      hint: `Proporciones y disponibilidad en ${brand || 'la marca seleccionada'}`,
    },
    {
      id: 'drafting' as const,
      label: 'Redactando plan operativo',
      hint: 'Estructurando sesiones, técnicas y fundamentos',
    },
  ];

  const getStepStatus = (id: StageId): 'done' | 'active' | 'pending' => {
    const activeIndex = generationStage === 'done'
      ? stageOrder.length
      : stageOrder.indexOf(generationStage as StageId);
    const stepIndex = stageOrder.indexOf(id);

    if (activeIndex > stepIndex) return 'done';
    if (activeIndex === stepIndex) return 'active';
    return 'pending';
  };

  useEffect(() => {
    generateFormula();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const verifyMixingRatio = async (brand: string, productLine?: string): Promise<string> => {
    const normalizedBrand = brand.trim().toLowerCase();
    const normalizedLine = (productLine && productLine.trim().length > 0 ? productLine.trim().toLowerCase() : 'general');
    const cacheKey = `${normalizedBrand}::${normalizedLine}`;

    if (mixingRatioCacheRef.current.has(cacheKey)) {
      return mixingRatioCacheRef.current.get(cacheKey) as string;
    }

    try {
      const query = `Proporción de mezcla EXACTA para ${brand}${productLine ? ` ${productLine}` : ''} según manual técnico profesional oficial 2025.

IMPORTANTE: Responde en formato estructurado:
1. Proporción estándar (ej: 1:1, 1:2, o "NO REQUIERE OXIDANTE")
2. Tipo de producto (permanente/semi-permanente/demi-permanente/decolorador/toner)
3. Volúmenes de oxidante compatibles (si aplica)
4. Tiempo de procesamiento típico
5. Instrucciones críticas o advertencias especiales

Solo información verificada de fuentes oficiales del fabricante.`;

      const verification = await generateTextSafe({
        messages: [{ role: 'user', content: query }],
        useCase: 'product_search', // Usa Perplexity para info actualizada
        maxRetries: 1,
      });

      console.log(`[Step5] Mixing ratio verified for ${brand} ${productLine || ''}`);
      mixingRatioCacheRef.current.set(cacheKey, verification);
      return verification;
    } catch (error) {
      console.warn('[Step5] Mixing ratio verification failed, using fallback:', error);
      // Fallback: reglas generales básicas
      const fallback = `INFORMACIÓN GENERAL (no se pudo verificar marca específica):
- Tintes permanentes típicamente usan proporción 1:1
- Decoloradores típicamente usan proporción 1:2
- Semi-permanentes pueden NO requerir oxidante - VERIFICAR
- IMPORTANTE: Consulta el manual técnico del fabricante para esta marca específica`;
      mixingRatioCacheRef.current.set(cacheKey, fallback);
      return fallback;
    }
  };

  const generateFormula = async () => {
    setIsGenerating(true);
    setGenerationStage('analysis');
    setMixingRatioDetails(null);
    setValidationChecklist([]);
    try {
      const currentAnalysis = formulaData.currentColorAnalysis;
      const desiredAnalysis = formulaData.desiredColorAnalysis;
      const brand = formulaData.brand;
      const productLine = formulaData.productLine;
      const clientName = formulaData.clientName;
      const technique = formulaData.technique;

      if (!currentAnalysis || !desiredAnalysis || !brand) {
        throw new Error('Faltan datos necesarios para generar la fórmula');
      }

      setGenerationStage('validation');

      try {
        const validationPrompt = `Antes de escribir la fórmula final realiza una VALIDACIÓN INTERNA completa de la información disponible.

Datos que debes revisar:
- Diagnóstico actual: ${currentAnalysis.roots.level}/10 en raíces (${currentAnalysis.roots.state}), medios ${currentAnalysis.mids.level}/10 (${currentAnalysis.mids.state}), puntas ${currentAnalysis.ends.level}/10 (${currentAnalysis.ends.state}). Porosidad: ${currentAnalysis.roots.porosity}. Elasticidad: ${currentAnalysis.roots.elasticity}.
- Color objetivo: nivel ${desiredAnalysis.level}/10, tono ${desiredAnalysis.tone}, reflejo ${desiredAnalysis.reflection || 'N/A'}, tipo ${desiredAnalysis.resultType}.
- Marca/línea seleccionada: ${brand}${productLine ? ` · ${productLine}` : ''}.
- Historial químico relevante: ${currentAnalysis.chemicalHistory.lastProcessType}.

Instrucciones para la validación:
1. Confirma que el diagnóstico actual es coherente (estado de fibra, porosidad, elasticidad, zonas).
2. Evalúa si el objetivo deseado es viable en función del diagnóstico (niveles, reflejos, canas).
3. Determina el número de sesiones necesario y justifícalo.
4. Declara el enfoque técnico para ${brand}${productLine ? ` (${productLine})` : ''} (familias de producto, oxidantes permitidos, restricciones).
5. Verifica que existen productos dentro de la marca para lograrlo y si requieren mezclas específicas.
6. Confirma las proporciones de mezcla (referenciando el manual) y los tiempos de proceso estándar.
7. Declara explícitamente que estás listo para formular y que no hay inconsistencias pendientes.

Formato de salida:
- Devuelve 7 pasos numerados ("1. ...") con frases cortas y accionables. Cada paso debe cerrar con una justificación después de "—" indicando el fundamento técnico.
- No repitas información redundante ni menciones que es una validación; simplemente describe las conclusiones.
- Nada de emojis ni markdown adicional.
`;

        const validationResult = await generateTextSafe({
          messages: [
            { role: 'system', content: 'Actúas como colorista senior responsable de validar datos antes de formular.' },
            { role: 'user', content: validationPrompt },
          ],
          maxRetries: 1,
          requestTimeout: 20000,
          useCase: 'formula_generation',
        });

        console.log('[Step5] Validation steps completed:', validationResult);
        const parsedChecklist = validationResult
          .split('\n')
          .map(line => line.trim())
          .filter(line => /^\d+\./.test(line) || /^[-•*]\s/.test(line))
          .map(line =>
            line
              .replace(/^\d+\.\s*/, '')
              .replace(/^[-•*]\s*/, '')
              .trim()
          )
          .filter(Boolean);

        if (parsedChecklist.length > 0) {
          setValidationChecklist(parsedChecklist);
        }
      } catch (validationError) {
        console.warn('[Step5] Validation checklist failed', validationError);
        setValidationChecklist([]);
      }

      setGenerationStage('references');

      let mixingRatioInfo = '';

      if (brand && brand !== 'Sin marca específica') {
        console.log('[Step5] Verifying mixing ratios with manufacturer manual...');
        try {
          mixingRatioInfo = await verifyMixingRatio(brand, productLine);
          setMixingRatioDetails(mixingRatioInfo);
        } catch (ratioError) {
          console.warn('[Step5] Mixing ratio verification failed, using fallback:', ratioError);
          setMixingRatioDetails(null);
        }
      }

      setGenerationStage('drafting');
      setFormula('');

      // Calculate context for prompt generation
      const levelDifference = Math.abs(desiredAnalysis.level - currentAnalysis.roots.level);
      const needsMultipleSessions = levelDifference > 3 ||
        currentAnalysis.roots.state === 'dañado' ||
        currentAnalysis.roots.state === 'muy dañado' ||
        (currentAnalysis.chemicalHistory.lastProcessType !== 'ninguno' && levelDifference > 2);

      const promptContext: FormulaPromptContext = {
        brand: brand || 'Sin marca específica',
        productLine,
        currentAnalysis,
        desiredAnalysis,
        clientName,
        technique,
        mixingRatioInfo,
        levelDifference,
        needsMultipleSessions,
      };

      // Use improved conversational prompts
      const systemPrompt = getFormulaSystemPrompt(promptContext);
      const userPrompt = getFormulaUserPrompt(promptContext);

      console.log('[Step5] Generating base formula...');

      let streamStarted = false;
      let streamedFormula = '';

      const baseFormula = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: userPrompt },
        ],
        maxRetries: 2,
        retryDelay: 1500,
        useCase: 'formula_generation',
        stream: true,
        requestTimeout: FORMULA_GENERATION_TIMEOUT,
        onStreamResult: (event) => {
          if ((event.type === 'token' || event.type === 'done') && typeof event.text === 'string') {
            streamedFormula = event.text;
            setFormula(event.text);
            if (!streamStarted) {
              streamStarted = true;
              setIsGenerating(false);
            }
          } else if (event.type === 'error' && event.error) {
            setFormula(event.error);
          }
        },
      });

      console.log('[Step5] Base formula ready, revealing to user');
      console.log('[Step5] 🔍 Formula length:', (streamedFormula || baseFormula).length);
      console.log('[Step5] 🔍 First 500 chars:', (streamedFormula || baseFormula).substring(0, 500));

      const finalFormula = streamedFormula || baseFormula;
      setFormula(finalFormula);
      if (!streamStarted) {
        setIsGenerating(false);
      }

      setGenerationStage('drafting');

      const initialMessage: Message = {
        id: Date.now().toString(),
        role: 'assistant',
        content: '¡Fórmula generada con éxito! ¿Tienes alguna pregunta o necesitas alguna aclaración sobre la fórmula?',
        timestamp: new Date(),
      };
      setChatMessages([initialMessage]);
    } catch (error) {
      console.error('Error al generar fórmula:', error);

      const errorMessage = getErrorMessage(error);

      setFormula(errorMessage);
    } finally {
      setGenerationStage('done');
      setIsGenerating(false);
    }
  };

  const handleFinish = async () => {
    if (!selectedClient?.id) {
      // Si no hay cliente, solo resetear
      resetFormula();
      router.push('/(app)/(tabs)/chat');
      return;
    }

    setIsUploadingPhotos(true);

    try {
      // Recolectar TODAS las fotos del workflow (current + desired)
      const allPhotos = [
        ...(formulaData.currentColorImages || []),
        ...(formulaData.desiredColorImages || []),
      ].filter(uri => uri && uri.startsWith('file://')); // Solo URIs locales

      if (allPhotos.length > 0) {
        console.log(`[Step5] Subiendo ${allPhotos.length} fotos a Storage...`);

        // Subir todas las fotos en paralelo
        await Promise.all(
          allPhotos.map(uri => uploadFormulaPhoto(uri, selectedClient.id))
        );

        console.log('[Step5] Todas las fotos subidas exitosamente');
      }

      // Finalizar y volver al chat
      resetFormula();
      router.push('/(app)/(tabs)/chat');
    } catch (error) {
      console.error('[Step5] Error al subir fotos:', error);
      // Preguntar si quiere continuar sin subir
      alert('Error al guardar las fotos. ¿Deseas finalizar de todas formas?');
      // Por ahora, finalizar igualmente
      resetFormula();
      router.push('/(app)/(tabs)/chat');
    } finally {
      setIsUploadingPhotos(false);
    }
  };

  const sendMessageToAssistant = async (
    content: string,
    options: { skipInputClear?: boolean } = {},
  ) => {
    const trimmed = content.trim();
    if (!trimmed || isSending) return;

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content: trimmed,
      timestamp: new Date(),
    };

    const conversationHistory = chatMessages
      .slice(-MAX_CONVERSATION_CONTEXT_MESSAGES)
      .filter(msg => msg.content.trim().length > 0)
      .map((msg) => ({
        role: msg.role,
        content: msg.content,
      }));

    setChatMessages((prev) => [...prev, userMessage]);
    if (!options.skipInputClear) {
      setInputText('');
    }
    setIsSending(true);

    try {
      const systemPrompt = `Eres un experto en coloración capilar que actúa como mentor técnico.

Contexto:
- Fórmula base generada (no la repitas completa si no es necesario):

${formula}

Tu objetivo:
- Responder preguntas sobre esta fórmula
- Sugerir ajustes cuando aporten valor
- Explicar el porqué de cada recomendación
- Señalar riesgos o precauciones antes de cada cambio`;

      const aiResponseContent = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          { role: 'user', content: trimmed },
        ],
        maxRetries: 2,
        retryDelay: 1500,
        useCase: 'chat',
        conversationHistory: conversationHistory.length > 0 ? conversationHistory : undefined,
        requestTimeout: CHAT_TIMEOUT,
      });

      const aiResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: aiResponseContent,
        timestamp: new Date(),
      };

      setChatMessages((prev) => [...prev, aiResponse]);

      setTimeout(() => {
        chatListRef.current?.scrollToEnd({ animated: true });
      }, 100);
    } catch (error) {
      console.error('Error al enviar mensaje:', error);

      const errorMessage = getErrorMessage(error);

      const errorResponse: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: errorMessage,
        timestamp: new Date(),
      };

      setChatMessages((prev) => [...prev, errorResponse]);
    } finally {
      setIsSending(false);
    }
  };

  const sendChatMessage = async () => {
    await sendMessageToAssistant(inputText);
  };

  const handleLearningShortcut = (prompt: string) => {
    if (isSending) return;
    setShowChat(true);
    void sendMessageToAssistant(prompt, { skipInputClear: true });
  };


  const getSectionIcon = (title: string) => {
    const upperTitle = title.toUpperCase();
    if (upperTitle.includes('SESIÓN') || upperTitle.includes('SESION')) return User;
    if (upperTitle.includes('DIAGNÓSTICO') || upperTitle.includes('DIAGNOSTICO')) return AlertCircle;
    if (upperTitle.includes('TRANSFORMACIÓN') || upperTitle.includes('TRANSFORMACION') || upperTitle.includes('ESTRATEGIA')) return AlertCircle;
    if (upperTitle.includes('PRODUCTO')) return PackageOpen;
    if (upperTitle.includes('PREPARACIÓN') || upperTitle.includes('PREPARACION')) return Droplet;
    if (upperTitle.includes('APLICACIÓN') || upperTitle.includes('APLICACION')) return CheckCircle2;
    if (upperTitle.includes('TÉCNICA') || upperTitle.includes('TECNICA')) return CheckCircle2;
    if (upperTitle.includes('TIEMPO')) return Clock;
    if (upperTitle.includes('ENJUAGUE')) return Droplet;
    if (upperTitle.includes('OBJETIVO')) return CheckCircle2;
    if (upperTitle.includes('CUIDADO') || upperTitle.includes('MANTENIMIENTO')) return AlertCircle;
    return CheckCircle2;
  };

  const getSectionColor = (title: string) => {
    const upperTitle = title.toUpperCase();
    if (upperTitle.includes('SESIÓN') || upperTitle.includes('SESION')) return '#1E40AF';
    if (upperTitle.includes('DIAGNÓSTICO') || upperTitle.includes('DIAGNOSTICO')) return '#7C3AED';
    if (upperTitle.includes('TRANSFORMACIÓN') || upperTitle.includes('TRANSFORMACION') || upperTitle.includes('ESTRATEGIA')) return '#7C3AED';
    if (upperTitle.includes('PRODUCTO')) return '#2563EB';
    if (upperTitle.includes('PREPARACIÓN') || upperTitle.includes('PREPARACION')) return '#059669';
    if (upperTitle.includes('APLICACIÓN') || upperTitle.includes('APLICACION') || upperTitle.includes('TÉCNICA') || upperTitle.includes('TECNICA')) return '#D97706';
    if (upperTitle.includes('TIEMPO')) return '#DC2626';
    if (upperTitle.includes('ENJUAGUE')) return '#0891B2';
    if (upperTitle.includes('OBJETIVO')) return '#16A34A';
    if (upperTitle.includes('CUIDADO') || upperTitle.includes('MANTENIMIENTO')) return '#4F46E5';
    return '#6B7280';
  };

  const renderStepCard = (step: ParsedSessionStep, stepKey: string) => {
    const Icon = getSectionIcon(step.title);
    const color = getSectionColor(step.title);
    const isProductSection = step.title.toUpperCase().includes('PRODUCTO');

    return (
      <View key={stepKey} style={styles.stepCard}>
        <View style={styles.stepHeaderStatic}>
          <View style={styles.stepHeaderLeft}>
            <View style={[styles.stepIconContainer, { backgroundColor: `${color}20` }]}
            >
              <Icon size={16} color={color} strokeWidth={2.5} />
            </View>
            <Text style={styles.stepTitle}>{step.title}</Text>
          </View>
        </View>

        <View style={styles.stepContent}>
          {step.content.map((item, idx) => {
            const itemKey = `${stepKey}-item-${idx}`;
            if (item.type === 'product' && isProductSection) {
              return (
                <View key={itemKey} style={styles.productCard}>
                  <View style={styles.productHeader}>
                    <View style={[styles.productBullet, { backgroundColor: color }]} />
                    <Text style={styles.productName}>{item.text}</Text>
                  </View>
                  {item.explanation && (
                    <View style={styles.productExplanation}>
                      <Text style={styles.productExplanationLabel}>Fundamento técnico</Text>
                      <Text style={styles.productExplanationText}>{item.explanation}</Text>
                    </View>
                  )}
                </View>
              );
            }

            return (
              <View key={itemKey} style={styles.contentItem}>
                <View style={[styles.contentBullet, { backgroundColor: color }]} />
                <View style={styles.contentBody}>
                  <Text style={styles.contentText}>{item.text}</Text>
                  {item.explanation && (
                    <View style={styles.contentExplanationBox}>
                      <Text style={styles.contentExplanationLabel}>Fundamento técnico</Text>
                      <Text style={styles.contentExplanation}>{item.explanation}</Text>
                    </View>
                  )}
                </View>
              </View>
            );
          })}
        </View>
      </View>
    );
  };

  const renderSessionCard = (session: ParsedSession, isUniqueSession: boolean) => {
    return (
      <View key={session.number} style={styles.sessionCard}>
        <View style={styles.sessionHeaderStatic}>
          <View style={styles.sessionHeaderLeft}>
            <View style={styles.sessionBadge}>
              {isUniqueSession ? (
                <CheckCircle2 size={24} color="#FFFFFF" strokeWidth={2.5} />
              ) : (
                <Text style={styles.sessionBadgeText}>{session.number}</Text>
              )}
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.sessionNumber}>
                {isUniqueSession ? 'Sesión única' : `Sesión ${session.number}`}
              </Text>
              {session.title && <Text style={styles.sessionTitle}>{session.title}</Text>}
            </View>
          </View>
        </View>

        <View style={styles.sessionContent}>
          {session.steps.map((step, idx) =>
            renderStepCard(step, `session-${session.number}-step-${idx}`)
          )}
        </View>
      </View>
    );
  };

  const renderHeroSection = () => {
    const brandLine = brand ? `${brand}${productLine ? ` · ${productLine}` : ''}` : null;
    const plannedSessions = totalSessions > 0
      ? totalSessions === 1
        ? '1 sesión diseñada de principio a fin'
        : `${totalSessions} sesiones escalonadas`
      : executiveSummaryDetails.sessions;
    const objective = executiveSummaryDetails.objective;

    const metaItems: { id: string; icon: IconRenderer; label: string; value: string }[] = [];

    if (clientName) {
      metaItems.push({ id: 'client', icon: User, label: 'Para', value: clientName });
    }

    if (brandLine) {
      metaItems.push({ id: 'brand', icon: PackageOpen, label: 'Catálogo de trabajo', value: brandLine });
    }

    if (technique) {
      metaItems.push({ id: 'technique', icon: CheckCircle2, label: 'Técnica base', value: technique });
    }

    if (plannedSessions) {
      metaItems.push({ id: 'sessions', icon: Clock, label: 'Ritmo sugerido', value: plannedSessions });
    }

    return (
      <View style={styles.heroCard}>
        <Text style={styles.heroEyebrow}>Paso 5 · Formulación a medida</Text>
        <Text style={styles.heroTitle}>Tu plan maestro de color</Text>
        <Text style={styles.heroSubtitle}>
          Vamos a trabajar juntas paso a paso: sabrás qué preparar, cómo aplicarlo y qué observar en cada momento.
        </Text>

        {objective && (
          <View style={styles.heroObjectiveBox}>
            <Target size={16} color={Colors.light.primary} strokeWidth={2.4} />
            <View style={{ flex: 1 }}>
              <Text style={styles.heroObjectiveLabel}>Objetivo trazado</Text>
              <Text style={styles.heroObjectiveValue}>{objective}</Text>
            </View>
          </View>
        )}

        {metaItems.length > 0 && (
          <View style={styles.heroMetaGrid}>
            {metaItems.map((item) => {
              const IconComponent = item.icon;
              return (
                <View key={item.id} style={styles.heroMetaItem}>
                  <View style={styles.heroMetaIcon}>
                    <IconComponent size={16} color={Colors.light.primary} strokeWidth={2.4} />
                  </View>
                  <Text style={styles.heroMetaLabel}>{item.label}</Text>
                  <Text style={styles.heroMetaValue}>{item.value}</Text>
                </View>
              );
            })}
          </View>
        )}
      </View>
    );
  };

  const renderLearningShortcuts = () => {
    if (learningShortcuts.length === 0) return null;

    return (
      <View style={styles.learningCard}>
        <View style={styles.learningHeader}>
          <View style={styles.learningIcon}>
            <BookOpenCheck size={18} color={Colors.light.primary} strokeWidth={2.4} />
          </View>
          <View style={{ flex: 1 }}>
            <Text style={styles.learningTitle}>Aprendizaje inmediato</Text>
            <Text style={styles.learningSubtitle}>
              Pulsa para abrir el chat con guías listas y profundizar al momento.
            </Text>
          </View>
        </View>
        <View style={styles.learningButtons}>
          {learningShortcuts.map(item => (
            <TouchableOpacity
              key={item.id}
              style={styles.learningButton}
              onPress={() => handleLearningShortcut(item.prompt)}
              activeOpacity={0.85}
            >
              <View style={styles.learningButtonHeader}>
                <Text style={styles.learningButtonTitle}>{item.title}</Text>
                <ArrowRight size={16} color={Colors.light.primary} strokeWidth={2.5} />
              </View>
              <Text style={styles.learningButtonSubtitle}>{item.subtitle}</Text>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderPreflightChecklist = () => {
    const brandLine = brand ? `${brand}${productLine ? ` · ${productLine}` : ''}` : null;
    const mixingLine = mixingRatioDetails
      ? mixingRatioDetails.split('\n').map(line => line.trim()).find(line => line.length > 0)
      : null;
    const displayedChecklist = validationChecklist.slice(0, 4);

    if (displayedChecklist.length === 0 && !mixingLine) {
      return null;
    }

    const isCollapsed = isSectionCollapsed('preflight');

    return (
      <View style={styles.preflightCard}>
        <TouchableOpacity
          style={styles.preflightHeader}
          onPress={() => toggleSection('preflight')}
          activeOpacity={0.85}
        >
          <View style={styles.preflightHeaderLeft}>
            <View style={styles.preflightIcon}>
              <ShieldCheck size={18} color={Colors.light.background} strokeWidth={2.5} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.preflightTitle}>Revisión técnica completada</Text>
              <Text style={styles.preflightSubtitle}>
                Repasé diagnóstico, compatibilidad con {brandLine || 'la marca elegida'} y proporciones antes de revelar la fórmula.
              </Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <>
            {displayedChecklist.length > 0 && (
              <View style={styles.preflightList}>
                {displayedChecklist.map((item, idx) => (
                  <View key={`checklist-${idx}`} style={styles.preflightListItem}>
                    <CheckCircle2 size={16} color={Colors.light.primary} strokeWidth={2.4} />
                    <Text style={styles.preflightListText}>{item}</Text>
                  </View>
                ))}
                {validationChecklist.length > displayedChecklist.length && (
                  <Text style={styles.preflightHint}>
                    Revisa el resto de verificaciones en la sección de estrategia y sesiones.
                  </Text>
                )}
              </View>
            )}

            {mixingLine && (
              <View style={styles.preflightMixing}>
                <Droplet size={18} color={'#0EA5E9'} strokeWidth={2.4} />
                <View style={{ flex: 1 }}>
                  <Text style={styles.preflightMixingLabel}>Proporción confirmada</Text>
                  <Text style={styles.preflightMixingValue}>{mixingLine}</Text>
                </View>
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  const renderProductSummaryCard = () => {
    if (productSummaryItems.length === 0) return null;

    const hasMixingRatio = Boolean(mixingRatioDetails);
    const isCollapsed = isSectionCollapsed('kit');

    return (
      <View style={styles.productSummaryCard}>
        <TouchableOpacity
          style={styles.productSummaryHeader}
          onPress={() => toggleSection('kit')}
          activeOpacity={0.85}
        >
          <View style={styles.productSummaryHeaderLeft}>
            <View style={styles.productSummaryIcon}>
              <PackageOpen size={18} color={Colors.light.primary} strokeWidth={2.5} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.productSummaryTitle}>Kit de formulación listo</Text>
              <Text style={styles.productSummarySubtitle}>
                Todo lo que necesitas sobre la mesa, con el porqué de cada elección.
              </Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <>
            {hasMixingRatio && (
              <View style={styles.productSummaryMixing}>
                <Droplet size={16} color={'#2563EB'} strokeWidth={2.4} />
                <Text style={styles.productSummaryMixingText}>Respeta la proporción verificada al preparar cada mezcla.</Text>
              </View>
            )}

            <View style={styles.productSummaryList}>
              {productSummaryItems.map(item => (
                <View key={item.name} style={styles.productSummaryItem}>
                  <View style={styles.productSummaryRow}>
                    <View style={styles.productSummaryBullet} />
                    <Text style={styles.productSummaryName}>{item.name}</Text>
                  </View>
                  {item.reasons.length > 0 && (
                    <Text style={styles.productSummaryReason}>
                      {item.reasons.join(' · ')}
                    </Text>
                  )}
                  {(item.sessions.length > 0 || item.stepHints.length > 0) && (
                    <View style={styles.productSummaryTagRow}>
                      {item.sessions.map(session => (
                        <View key={`${item.name}-${session}`} style={styles.productSummaryTag}>
                          <Clock size={12} color={Colors.light.primary} strokeWidth={2.4} />
                          <Text style={styles.productSummaryTagText}>{session}</Text>
                        </View>
                      ))}
                      {item.stepHints.map(hint => (
                        <View key={`${item.name}-${hint}`} style={styles.productSummaryTagAlt}>
                          <CheckCircle2 size={12} color={'#047857'} strokeWidth={2.4} />
                          <Text style={styles.productSummaryTagAltText}>{hint}</Text>
                        </View>
                      ))}
                    </View>
                  )}
                </View>
              ))}
            </View>
          </>
        )}
      </View>
    );
  };

  const renderTechnicalOverviewCard = () => {
    const { objective, sessions: sessionsPlanned, riskNotes, guidelines } = executiveSummaryDetails;

    if (!objective && !sessionsPlanned && riskNotes.length === 0 && guidelines.length === 0) {
      return null;
    }

    const isCollapsed = isSectionCollapsed('overview');

    return (
      <View style={styles.overviewCard}>
        <TouchableOpacity
          style={styles.overviewHeader}
          onPress={() => toggleSection('overview')}
          activeOpacity={0.85}
        >
          <View style={styles.overviewHeaderLeft}>
            <View style={styles.overviewIconWrapper}>
              <Sparkles size={18} color={Colors.light.primary} strokeWidth={2.4} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.overviewTitle}>Bitácora de ejecución</Text>
              <Text style={styles.overviewSubtitle}>Puntos de control para mantener el plan intacto.</Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <>
            {objective && (
              <View style={styles.overviewRow}>
                <Target size={16} color={Colors.light.primary} strokeWidth={2.4} />
                <View style={{ flex: 1 }}>
                  <Text style={styles.overviewRowLabel}>Objetivo</Text>
                  <Text style={styles.overviewRowValue}>{objective}</Text>
                </View>
              </View>
            )}

            {sessionsPlanned && (
              <View style={styles.overviewRow}>
                <Clock size={16} color={'#0EA5E9'} strokeWidth={2.4} />
                <View style={{ flex: 1 }}>
                  <Text style={styles.overviewRowLabel}>Itinerario</Text>
                  <Text style={styles.overviewRowValue}>{sessionsPlanned}</Text>
                </View>
              </View>
            )}

            {riskNotes.length > 0 && (
              <View style={styles.overviewRiskBlock}>
                <View style={styles.overviewRiskHeader}>
                  <ShieldAlert size={16} color={'#DC2626'} strokeWidth={2.4} />
                  <Text style={styles.overviewRowLabel}>Alertas que vigilar</Text>
                </View>
                {riskNotes.map((risk, idx) => (
                  <Text key={`risk-${idx}`} style={styles.overviewRiskText}>• {risk}</Text>
                ))}
              </View>
            )}

            {guidelines.length > 0 && (
              <View style={styles.overviewGuidelines}>
                <Text style={styles.overviewRowLabel}>Notas profesionales</Text>
                {guidelines.slice(0, 3).map((note, idx) => (
                  <Text key={`note-${idx}`} style={styles.overviewGuidelineText}>- {note}</Text>
                ))}
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  const renderColorJourneyCard = () => {
    if (!currentAnalysis || !desiredAnalysis) return null;

    const safeColor = (value?: string, fallback = '#4B5563') => {
      if (!value) return fallback;
      return value.startsWith('#') ? value : fallback;
    };

    const currentTone = currentAnalysis.roots.tone || 'Sin dato';
    const desiredTone = desiredAnalysis.tone || 'Sin dato';
    const currentLevel = currentAnalysis.roots.level;
    const desiredLevel = desiredAnalysis.level;
    const levelDelta = desiredLevel - currentLevel;
    const levelLabel = levelDelta === 0
      ? 'Mantener nivel actual'
      : levelDelta > 0
        ? `Aclarar ${levelDelta} nivel${levelDelta === 1 ? '' : 'es'}`
        : `Profundizar ${Math.abs(levelDelta)} nivel${Math.abs(levelDelta) === 1 ? '' : 'es'}`;

    const desiredResultLabel = desiredAnalysis.resultType
      ? `Resultado ${desiredAnalysis.resultType}`
      : null;

    const currentColorHex = safeColor(
      currentAnalysis.roots.toneColor || currentAnalysis.generalCharacteristics.predominantToneColor
    );
    const desiredColorHex = safeColor(
      desiredAnalysis.toneColor || desiredAnalysis.reflectionColor,
      '#8B5CF6'
    );

    const isCollapsed = isSectionCollapsed('journey');

    return (
      <View style={styles.colorJourneyCard}>
        <TouchableOpacity
          style={styles.colorJourneyHeader}
          onPress={() => toggleSection('journey')}
          activeOpacity={0.85}
        >
          <View style={styles.colorJourneyHeaderLeft}>
            <Text style={styles.colorJourneyTitle}>Ruta de color</Text>
            <View style={styles.colorJourneyTag}>
              <ArrowRight size={16} color={Colors.light.primary} strokeWidth={2.5} />
              <Text style={styles.colorJourneyTagText}>{levelLabel}</Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <>
            <Text style={styles.colorJourneySubtitle}>
              Del {currentTone.toLowerCase()} nivel {currentLevel}/10 al {desiredTone.toLowerCase()} nivel {desiredLevel}/10
            </Text>
            <View style={styles.colorJourneyContent}>
              <View style={styles.colorStop}>
                <View style={[styles.colorCircle, { backgroundColor: currentColorHex }]} />
                <Text style={styles.colorStopLabel}>Color actual</Text>
                <Text style={styles.colorStopText}>Nivel {currentLevel}/10 · {currentTone}</Text>
                <Text style={styles.colorStopMeta}>Estado: {currentAnalysis.roots.state}</Text>
                <Text style={styles.colorStopMeta}>Porosidad: {currentAnalysis.roots.porosity}</Text>
              </View>

              <View style={styles.colorConnector}>
                <ArrowRight size={24} color="#6B7280" strokeWidth={2.4} />
                {desiredResultLabel && (
                  <Text style={styles.colorConnectorText}>{desiredResultLabel}</Text>
                )}
              </View>

              <View style={styles.colorStop}>
                <View style={[styles.colorCircle, { backgroundColor: desiredColorHex }]} />
                <Text style={styles.colorStopLabel}>Color objetivo</Text>
                <Text style={styles.colorStopText}>Nivel {desiredLevel}/10 · {desiredTone}</Text>
                {desiredAnalysis.reflection ? (
                  <Text style={styles.colorStopMeta}>Reflejo: {desiredAnalysis.reflection}</Text>
                ) : null}
                {desiredAnalysis.grayCoverage !== undefined ? (
                  <Text style={styles.colorStopMeta}>
                    Cobertura de canas: {desiredAnalysis.grayCoverage}%
                  </Text>
                ) : null}
              </View>
            </View>
          </>
        )}
      </View>
    );
  };

  const renderPlanSummaryCard = () => {
    if (!hasStructuredFormula && strategyHighlights.length === 0 && totalSessions === 0) {
      return null;
    }

    const summaryLines = strategyHighlights;

    const visibleSessionHighlights = sessionHighlights
      .filter(item => item.highlight)
      .slice(0, 3);

    const metaChips: { id: string; icon: IconRenderer; label: string; value: string }[] = [];

    if (technique) {
      metaChips.push({ id: 'technique', icon: CheckCircle2, label: 'Técnica', value: technique });
    }

    if (brand) {
      metaChips.push({
        id: 'brand',
        icon: PackageOpen,
        label: 'Marca',
        value: productLine ? `${brand} · ${productLine}` : brand,
      });
    }

    const isCollapsed = isSectionCollapsed('plan');

    return (
      <View style={styles.planSummaryCard}>
        <TouchableOpacity
          style={styles.planSummaryHeader}
          onPress={() => toggleSection('plan')}
          activeOpacity={0.85}
        >
          <View style={styles.planSummaryHeaderLeft}>
            <View style={styles.planSummaryBadge}>
              <Target size={18} color={Colors.light.primary} strokeWidth={2.5} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.planSummaryTitle}>Mapa de sesiones guiadas</Text>
              <Text style={styles.planSummarySubtitle}>Qué lograr en cada parada, qué controles hacer y cuándo pausar.</Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <>
            {summaryLines.length > 0 && (
              <View style={styles.planSummaryList}>
                {summaryLines.map((line, idx) => (
                  <View key={`summary-${idx}`} style={styles.planSummaryListItem}>
                    <CheckCircle2 size={16} color={Colors.light.primary} strokeWidth={2.5} />
                    <Text style={styles.planSummaryListText}>{line}</Text>
                  </View>
                ))}
              </View>
            )}

            {visibleSessionHighlights.length > 0 && (
              <View style={styles.planTimeline}>
                {visibleSessionHighlights.map((session, idx) => (
                  <View key={`session-timeline-${session.number}`} style={styles.planTimelineRow}>
                    <View style={styles.planTimelineMarkerWrapper}>
                      <View style={styles.planTimelineMarker}>
                        <Text style={styles.planTimelineMarkerText}>{session.number}</Text>
                      </View>
                      {idx !== visibleSessionHighlights.length - 1 && <View style={styles.planTimelineLine} />}
                    </View>
                    <View style={{ flex: 1 }}>
                      <Text style={styles.planTimelineTitle}>{session.title || `Sesión ${session.number}`}</Text>
                      {session.highlight && (
                        <Text style={styles.planTimelineText}>{session.highlight}</Text>
                      )}
                    </View>
                  </View>
                ))}
              </View>
            )}

            {metaChips.length > 0 && (
              <View style={styles.planMetaRow}>
                {metaChips.map((chip) => {
                  const Icon = chip.icon;
                  return (
                    <View key={chip.id} style={styles.planMetaChip}>
                      <Icon size={15} color={Colors.light.primary} strokeWidth={2.5} />
                      <Text style={styles.planMetaChipLabel}>{chip.label}</Text>
                      <Text style={styles.planMetaChipValue}>{chip.value}</Text>
                    </View>
                  );
                })}
              </View>
            )}
          </>
        )}
      </View>
    );
  };

  const renderCheckpointsCard = () => {
    if (sessionCheckpoints.length === 0) return null;

    const isCollapsed = isSectionCollapsed('checkpoints');

    return (
      <View style={styles.checkpointsCard}>
        <TouchableOpacity
          style={styles.checkpointsHeader}
          onPress={() => toggleSection('checkpoints')}
          activeOpacity={0.85}
        >
          <View style={styles.checkpointsHeaderLeft}>
            <View style={styles.checkpointsIcon}>
              <AlertTriangle size={18} color={Colors.light.background} strokeWidth={2.5} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.checkpointsTitle}>Checkpoints en cabina</Text>
              <Text style={styles.checkpointsSubtitle}>
                Observa estos indicadores antes de avanzar al siguiente paso o sesión.
              </Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && (
          <View style={styles.checkpointsBody}>
            {sessionCheckpoints.map(block => (
              <View key={block.id} style={styles.checkpointGroup}>
                <Text style={styles.checkpointTitle}>{block.title}</Text>
                <View style={styles.checkpointList}>
                  {block.items.map((item, idx) => (
                    <View key={`${block.id}-${idx}`} style={styles.checkpointItem}>
                      <View style={styles.checkpointBullet} />
                      <Text style={styles.checkpointText}>{item}</Text>
                    </View>
                  ))}
                </View>
              </View>
            ))}
          </View>
        )}
      </View>
    );
  };

  const renderInsightCard = (
    key: string,
    title: string,
    IconComponent: IconRenderer,
    color: string,
    items: string[],
  ) => {
    if (items.length === 0) return null;

    return (
      <View key={key} style={[styles.insightCard, { borderColor: `${color}33` }]}> 
        <View style={styles.insightHeader}>
          <View style={[styles.insightIcon, { backgroundColor: `${color}15` }]}> 
            <IconComponent size={16} color={color} strokeWidth={2.4} />
          </View>
          <Text style={styles.insightTitle}>{title}</Text>
        </View>
        <Text style={styles.insightIntro}>Razonamientos que guían este paso:</Text>
        <View style={styles.insightList}>
          {items.map((item, idx) => (
            <View key={`${key}-${idx}`} style={styles.insightListItem}>
              <View style={[styles.insightBullet, { backgroundColor: color }]} />
              <Text style={styles.insightText}>{item}</Text>
            </View>
          ))}
        </View>
      </View>
    );
  };

  const renderExpertInsights = () => {
    const cards = [] as React.ReactNode[];

    if (diagnosticHighlights.length > 0) {
      cards.push(
        renderInsightCard(
          'diagnostic',
          diagnosticSection?.title || 'Diagnóstico profesional',
          AlertCircle,
          '#7C3AED',
          diagnosticHighlights,
        )
      );
    }

    if (strategyHighlights.length > 0) {
      cards.push(
        renderInsightCard(
          'strategy',
          strategySection?.title || 'Estrategia de transformación',
          BookOpenCheck,
          '#0EA5E9',
          strategyHighlights,
        )
      );
    }

    if (cards.length === 0) return null;

    const isCollapsed = isSectionCollapsed('insights');

    return (
      <View style={styles.insightCardWrapper}>
        <TouchableOpacity
          style={styles.insightHeaderRow}
          onPress={() => toggleSection('insights')}
          activeOpacity={0.85}
        >
          <View style={styles.insightHeaderLeft}>
            <View style={styles.insightHeaderIcon}>
              <BookOpenCheck size={18} color={Colors.light.background} strokeWidth={2.5} />
            </View>
            <View style={{ flex: 1 }}>
              <Text style={styles.insightHeaderTitle}>Claves del mentor</Text>
              <Text style={styles.insightHeaderSubtitle}>Explicaciones rápidas para comprender la estrategia.</Text>
            </View>
          </View>
          {isCollapsed ? (
            <ChevronDown size={18} color="#475569" strokeWidth={2.4} />
          ) : (
            <ChevronUp size={18} color={Colors.light.primary} strokeWidth={2.4} />
          )}
        </TouchableOpacity>

        {!isCollapsed && <View style={styles.insightStack}>{cards}</View>}
      </View>
    );
  };


  const renderChatMessage = ({ item }: { item: Message }) => {
    const isUser = item.role === 'user';

    return (
      <View style={[styles.messageContainer, isUser && styles.userMessageContainer]}>
        <View style={[styles.messageBubble, isUser ? styles.userBubble : styles.aiBubble]}>
          <FormattedMessageContent
            content={item.content}
            tone={isUser ? 'user' : 'assistant'}
          />
          <Text style={[styles.timestamp, isUser && styles.userTimestamp]}>
            {item.timestamp.toLocaleTimeString('es-ES', { hour: '2-digit', minute: '2-digit' })}
          </Text>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {isGenerating ? (
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.light.primary} style={styles.loadingSpinner} />
          <Text style={styles.loadingTitle}>Preparando tu fórmula profesional</Text>
          <Text style={styles.loadingSubtitle}>
            Analizamos diagnóstico, historial y objetivos para construir un plan seguro y accionable.
          </Text>
          <View style={styles.progressList}>
            {generationSteps.map((step) => {
              const status = getStepStatus(step.id);
              return (
                <View key={step.id} style={styles.progressItem}>
                  <View
                    style={[
                      styles.progressIcon,
                      status === 'done' && styles.progressIconDone,
                      status === 'active' && styles.progressIconActive,
                    ]}
                  >
                    {status === 'done' ? (
                      <CheckCircle2 size={18} color={Colors.light.background} strokeWidth={2.5} />
                    ) : status === 'active' ? (
                      <ActivityIndicator size="small" color={Colors.light.primary} />
                    ) : (
                      <Clock size={18} color={Colors.light.textLight} strokeWidth={2} />
                    )}
                  </View>
                  <View style={styles.progressTextWrapper}>
                    <Text style={styles.progressLabel}>{step.label}</Text>
                    <Text style={styles.progressHint}>{step.hint}</Text>
                  </View>
                </View>
              );
            })}
          </View>
        </View>
      ) : (
        <>
          {!showChat ? (
            <>
              <ScrollView 
                contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 200 }]}
                showsVerticalScrollIndicator={false}
              >
                {renderHeroSection()}
                {renderLearningShortcuts()}
                {renderPreflightChecklist()}
                {renderProductSummaryCard()}
                {renderColorJourneyCard()}
                {renderTechnicalOverviewCard()}
                {renderCheckpointsCard()}
                {renderPlanSummaryCard()}
                {renderExpertInsights()}

                <View style={styles.formulaContainer}>
                  {hasStructuredFormula ? (
                    <>
                      {preliminarySectionsToRender.map((section, idx) =>
                        renderStepCard(section, `prelim-${idx}`)
                      )}
                      {parsedFormula.sessions.map((session) =>
                        renderSessionCard(session, totalSessions === 1)
                      )}
                    </>
                  ) : (
                    <Text style={styles.formulaText}>{formula}</Text>
                  )}
                </View>
              </ScrollView>

              <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
                <TouchableOpacity
                  style={styles.chatButton}
                  onPress={() => setShowChat(true)}
                >
                  <MessageCircle color={Colors.light.primary} size={20} />
                  <Text style={styles.chatButtonText}>Conversar sobre la fórmula</Text>
                </TouchableOpacity>

                <View style={styles.actionsRow}>
                  <TouchableOpacity style={styles.actionButton}>
                    <Download color={Colors.light.primary} size={20} />
                    <Text style={styles.actionButtonText}>PDF</Text>
                  </TouchableOpacity>

                  <TouchableOpacity style={styles.actionButton}>
                    <Share2 color={Colors.light.primary} size={20} />
                    <Text style={styles.actionButtonText}>Compartir</Text>
                  </TouchableOpacity>
                </View>

                <TouchableOpacity
                  style={[styles.finishButton, isUploadingPhotos && styles.finishButtonDisabled]}
                  onPress={handleFinish}
                  disabled={isUploadingPhotos}
                >
                  {isUploadingPhotos ? (
                    <>
                      <ActivityIndicator color={Colors.light.background} size="small" />
                      <Text style={[styles.finishButtonText, { marginLeft: 10 }]}>
                        Guardando fotos...
                      </Text>
                    </>
                  ) : (
                    <Text style={styles.finishButtonText}>Finalizar</Text>
                  )}
                </TouchableOpacity>
              </View>
            </>
          ) : (
            <>
              <View style={styles.chatHeader}>
                <TouchableOpacity
                  style={styles.backButton}
                  onPress={() => setShowChat(false)}
                >
                  <Text style={styles.backButtonText}>← Volver a la fórmula</Text>
                </TouchableOpacity>
                <Text style={styles.chatHeaderTitle}>Conversación sobre la fórmula</Text>
              </View>

              <FlatList
                ref={chatListRef}
                data={chatMessages}
                renderItem={renderChatMessage}
                keyExtractor={(item) => item.id}
                contentContainerStyle={styles.chatList}
                onContentSizeChange={() => chatListRef.current?.scrollToEnd({ animated: true })}
                ListFooterComponent={
                  isSending ? (
                    <View style={styles.loadingMessageContainer}>
                      <ActivityIndicator size="small" color={Colors.light.primary} />
                      <Text style={styles.loadingMessageText}>Analizando...</Text>
                    </View>
                  ) : null
                }
              />

              <KeyboardAvoidingView
                behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
                keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
              >
                <View style={[styles.inputContainer, { paddingBottom: insets.bottom + 8 }]}>
                  <View style={styles.inputRow}>
                    <TextInput
                      style={styles.input}
                      placeholder="Pregunta sobre la fórmula..."
                      placeholderTextColor={Colors.light.textLight}
                      value={inputText}
                      onChangeText={setInputText}
                      multiline
                      maxLength={500}
                    />
                    <TouchableOpacity
                      style={[styles.sendButton, (!inputText.trim() || isSending) && styles.sendButtonDisabled]}
                      onPress={sendChatMessage}
                      disabled={!inputText.trim() || isSending}
                    >
                      <Send color={Colors.light.background} size={20} />
                    </TouchableOpacity>
                  </View>
                </View>
              </KeyboardAvoidingView>
            </>
          )}
        </>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    gap: 16,
  },
  loadingTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginTop: 8,
  },
  loadingSubtitle: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  loadingSpinner: {
    marginBottom: 8,
  },
  progressList: {
    width: '100%',
    marginTop: 12,
    gap: 14,
  },
  progressItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  progressIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    borderWidth: 2,
    borderColor: Colors.light.border,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#FFFFFF',
  },
  progressIconDone: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  progressIconActive: {
    borderColor: Colors.light.primary,
  },
  progressTextWrapper: {
    flex: 1,
  },
  progressLabel: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  progressHint: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    marginTop: 2,
    lineHeight: 18,
  },
  content: {
    padding: 20,
  },
  heroCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 22,
    padding: 22,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    marginBottom: 20,
    gap: 14,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.04,
    shadowRadius: 14,
    elevation: 2,
  },
  heroEyebrow: {
    fontSize: 12,
    letterSpacing: 0.6,
    textTransform: 'uppercase' as const,
    color: '#64748B',
    fontWeight: '600' as const,
  },
  heroTitle: {
    fontSize: 26,
    fontWeight: '800' as const,
    color: '#0F172A',
    letterSpacing: -0.6,
  },
  heroSubtitle: {
    fontSize: 14,
    lineHeight: 21,
    color: '#475569',
  },
  heroObjectiveBox: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
    backgroundColor: '#F5F3FF',
    borderRadius: 14,
    padding: 14,
  },
  heroObjectiveLabel: {
    fontSize: 12,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  heroObjectiveValue: {
    fontSize: 14,
    color: '#1E293B',
    lineHeight: 20,
  },
  heroMetaGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap' as const,
    gap: 12,
  },
  heroMetaItem: {
    borderRadius: 14,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 14,
    paddingVertical: 12,
    flexGrow: 1,
    flexBasis: '48%',
    gap: 6,
  },
  heroMetaIcon: {
    width: 30,
    height: 30,
    borderRadius: 10,
    backgroundColor: 'rgba(59, 130, 246, 0.12)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  heroMetaLabel: {
    fontSize: 11,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
    color: '#64748B',
    fontWeight: '600' as const,
  },
  heroMetaValue: {
    fontSize: 14,
    color: '#0F172A',
    lineHeight: 20,
  },
  learningCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
  },
  learningHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
  },
  learningIcon: {
    width: 42,
    height: 42,
    borderRadius: 14,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  learningTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#1E3A8A',
    letterSpacing: -0.2,
  },
  learningSubtitle: {
    fontSize: 13,
    color: '#475569',
    marginTop: 2,
    lineHeight: 19,
  },
  learningButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap' as const,
    gap: 12,
  },
  learningButton: {
    flexBasis: '48%',
    backgroundColor: '#F8FAFC',
    borderRadius: 14,
    padding: 14,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    gap: 8,
  },
  learningButtonHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  learningButtonTitle: {
    fontSize: 14,
    fontWeight: '700' as const,
    color: '#0F172A',
    flex: 1,
    marginRight: 10,
  },
  learningButtonSubtitle: {
    fontSize: 12,
    color: '#475569',
    lineHeight: 18,
  },
  preflightCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
  },
  preflightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  preflightHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
    flex: 1,
  },
  preflightIcon: {
    width: 40,
    height: 40,
    borderRadius: 14,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  preflightTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#0F172A',
    letterSpacing: -0.2,
  },
  preflightSubtitle: {
    fontSize: 13,
    color: '#475569',
    lineHeight: 19,
    marginTop: 2,
  },
  preflightList: {
    gap: 10,
  },
  preflightListItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  preflightListText: {
    flex: 1,
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
  },
  preflightHint: {
    fontSize: 12,
    color: '#64748B',
    marginTop: 4,
  },
  preflightMixing: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    backgroundColor: '#ECFEFF',
    borderRadius: 14,
    padding: 14,
  },
  preflightMixingLabel: {
    fontSize: 12,
    fontWeight: '600' as const,
    letterSpacing: 0.5,
    textTransform: 'uppercase' as const,
    color: '#0E7490',
  },
  preflightMixingValue: {
    fontSize: 14,
    color: '#0F172A',
    lineHeight: 20,
  },
  productSummaryCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: '#DBEAFE',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 2,
  },
  productSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  productSummaryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
    flex: 1,
  },
  productSummaryIcon: {
    width: 42,
    height: 42,
    borderRadius: 14,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  productSummaryTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#1E3A8A',
    letterSpacing: -0.2,
  },
  productSummarySubtitle: {
    fontSize: 13,
    color: '#475569',
    marginTop: 2,
    lineHeight: 19,
  },
  productSummaryMixing: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    backgroundColor: '#EFF6FF',
    padding: 12,
    borderRadius: 12,
  },
  productSummaryMixingText: {
    flex: 1,
    fontSize: 13,
    color: '#1D4ED8',
    lineHeight: 19,
  },
  productSummaryList: {
    gap: 16,
  },
  productSummaryItem: {
    backgroundColor: '#F8FAFC',
    borderRadius: 14,
    padding: 14,
    gap: 10,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  productSummaryRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
  },
  productSummaryBullet: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: '#2563EB',
  },
  productSummaryName: {
    flex: 1,
    fontSize: 15,
    fontWeight: '700' as const,
    color: '#0F172A',
  },
  productSummaryReason: {
    fontSize: 13,
    lineHeight: 19,
    color: '#334155',
  },
  productSummaryTagRow: {
    flexDirection: 'row',
    flexWrap: 'wrap' as const,
    gap: 8,
  },
  productSummaryTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#DBEAFE',
    borderRadius: 999,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  productSummaryTagText: {
    fontSize: 11,
    color: '#1E40AF',
    fontWeight: '600' as const,
  },
  productSummaryTagAlt: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    backgroundColor: '#DCFCE7',
    borderRadius: 999,
    paddingHorizontal: 10,
    paddingVertical: 6,
  },
  productSummaryTagAltText: {
    fontSize: 11,
    color: '#047857',
    fontWeight: '600' as const,
  },
  overviewCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 20,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 2,
  },
  overviewHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  overviewHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    flex: 1,
  },
  overviewIconWrapper: {
    width: 42,
    height: 42,
    borderRadius: 14,
    backgroundColor: 'rgba(59, 130, 246, 0.12)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  overviewTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#0F172A',
    letterSpacing: -0.2,
  },
  overviewSubtitle: {
    fontSize: 13,
    color: '#475569',
    marginTop: 2,
  },
  overviewRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  overviewRowLabel: {
    fontSize: 12,
    fontWeight: '600' as const,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
    color: '#64748B',
  },
  overviewRowValue: {
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
    marginTop: 4,
  },
  overviewRiskBlock: {
    backgroundColor: '#FEF2F2',
    padding: 14,
    borderRadius: 12,
    gap: 6,
  },
  overviewRiskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  overviewRiskText: {
    fontSize: 13,
    color: '#991B1B',
    lineHeight: 19,
  },
  overviewGuidelines: {
    backgroundColor: '#F8FAFC',
    padding: 14,
    borderRadius: 12,
    gap: 6,
  },
  overviewGuidelineText: {
    fontSize: 13,
    color: '#1E293B',
    lineHeight: 19,
  },
  colorJourneyCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 18,
    padding: 20,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.06,
    shadowRadius: 14,
    elevation: 3,
  },
  colorJourneyHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  colorJourneyHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  colorJourneyTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#0F172A',
  },
  colorJourneyTag: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: '#EEF2FF',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 999,
  },
  colorJourneyTagText: {
    fontSize: 13,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  colorJourneySubtitle: {
    fontSize: 14,
    color: '#4B5563',
    lineHeight: 20,
  },
  colorJourneyContent: {
    flexDirection: 'row',
    flexWrap: 'wrap' as const,
    gap: 18,
    alignItems: 'flex-start',
  },
  colorStop: {
    flex: 1,
    minWidth: 140,
    gap: 6,
  },
  colorCircle: {
    width: 56,
    height: 56,
    borderRadius: 28,
    borderWidth: 2,
    borderColor: '#FFFFFF',
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.12,
    shadowRadius: 10,
    elevation: 4,
    marginBottom: 6,
  },
  colorStopLabel: {
    fontSize: 13,
    fontWeight: '700' as const,
    color: '#2563EB',
    textTransform: 'uppercase' as const,
    letterSpacing: 0.6,
  },
  colorStopText: {
    fontSize: 15,
    color: '#111827',
    fontWeight: '600' as const,
  },
  colorStopMeta: {
    fontSize: 13,
    color: '#4B5563',
    lineHeight: 19,
  },
  colorConnector: {
    alignItems: 'center',
    gap: 6,
  },
  colorConnectorText: {
    fontSize: 12,
    color: '#6B7280',
    fontWeight: '600' as const,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.4,
  },
  formulaContainer: {
    gap: 16,
  },
  formulaComponents: {
    gap: 16,
  },
  formulaText: {
    fontSize: 15,
    lineHeight: 24,
    color: Colors.light.text,
  },
  planSummaryCard: {
    backgroundColor: '#F8FAFC',
    borderRadius: 18,
    padding: 20,
    marginBottom: 20,
    gap: 18,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  planSummaryHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 18,
  },
  planSummaryHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  planSummaryBadge: {
    width: 44,
    height: 44,
    borderRadius: 14,
    backgroundColor: 'rgba(59, 130, 246, 0.12)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  planSummaryTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#0F172A',
    letterSpacing: -0.2,
  },
  planSummarySubtitle: {
    fontSize: 13,
    color: '#475569',
    marginTop: 4,
  },
  planSummaryList: {
    gap: 12,
  },
  planSummaryListItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  planSummaryListText: {
    flex: 1,
    fontSize: 14,
    color: '#1F2937',
    lineHeight: 20,
  },
  planTimeline: {
    gap: 16,
    paddingTop: 4,
  },
  planTimelineRow: {
    flexDirection: 'row',
    gap: 12,
  },
  planTimelineMarkerWrapper: {
    alignItems: 'center',
  },
  planTimelineMarker: {
    width: 26,
    height: 26,
    borderRadius: 13,
    backgroundColor: '#1D4ED8',
    alignItems: 'center',
    justifyContent: 'center',
  },
  planTimelineMarkerText: {
    color: '#FFFFFF',
    fontSize: 13,
    fontWeight: '700' as const,
  },
  planTimelineLine: {
    width: 2,
    flex: 1,
    backgroundColor: '#BFDBFE',
    marginTop: 4,
  },
  planTimelineTitle: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#1E3A8A',
  },
  planTimelineText: {
    fontSize: 13,
    color: '#475569',
    marginTop: 4,
    lineHeight: 19,
  },
  planMetaRow: {
    flexDirection: 'row',
    flexWrap: 'wrap' as const,
    gap: 10,
    marginTop: 4,
  },
  planMetaChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    backgroundColor: '#E0F2FE',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 999,
  },
  planMetaChipLabel: {
    fontSize: 12,
    color: '#0284C7',
    fontWeight: '600' as const,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.4,
  },
  planMetaChipValue: {
    fontSize: 13,
    color: '#0369A1',
    fontWeight: '500' as const,
  },
  checkpointsCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 18,
    padding: 20,
    borderWidth: 1,
    borderColor: '#FFE4E6',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
  },
  checkpointsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  checkpointsHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 14,
    flex: 1,
  },
  checkpointsIcon: {
    width: 42,
    height: 42,
    borderRadius: 14,
    backgroundColor: '#FEE2E2',
    alignItems: 'center',
    justifyContent: 'center',
  },
  checkpointsTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#991B1B',
    letterSpacing: -0.2,
  },
  checkpointsSubtitle: {
    fontSize: 13,
    color: '#7F1D1D',
    marginTop: 2,
    lineHeight: 19,
  },
  checkpointsBody: {
    gap: 16,
  },
  checkpointGroup: {
    gap: 10,
    backgroundColor: '#FFF1F2',
    borderRadius: 14,
    padding: 14,
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  checkpointTitle: {
    fontSize: 14,
    fontWeight: '700' as const,
    color: '#9F1239',
    textTransform: 'uppercase' as const,
    letterSpacing: 0.4,
  },
  checkpointList: {
    gap: 8,
  },
  checkpointItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  checkpointBullet: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#F43F5E',
    marginTop: 6,
  },
  checkpointText: {
    flex: 1,
    fontSize: 13,
    color: '#7F1D1D',
    lineHeight: 19,
  },
  insightStack: {
    gap: 14,
    marginBottom: 20,
  },
  insightCardWrapper: {
    backgroundColor: '#FFFFFF',
    borderRadius: 18,
    padding: 20,
    borderWidth: 1,
    borderColor: '#DBEAFE',
    marginBottom: 20,
    gap: 16,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.04,
    shadowRadius: 12,
    elevation: 2,
  },
  insightHeaderRow: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 16,
  },
  insightHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 14,
    flex: 1,
  },
  insightHeaderIcon: {
    width: 42,
    height: 42,
    borderRadius: 14,
    backgroundColor: '#EEF2FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  insightHeaderTitle: {
    fontSize: 18,
    fontWeight: '700' as const,
    color: '#1E3A8A',
    letterSpacing: -0.2,
  },
  insightHeaderSubtitle: {
    fontSize: 13,
    color: '#475569',
    marginTop: 2,
    lineHeight: 19,
  },
  insightCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    padding: 18,
    gap: 14,
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  insightHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  insightIcon: {
    width: 34,
    height: 34,
    borderRadius: 10,
    alignItems: 'center',
    justifyContent: 'center',
  },
  insightTitle: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: '#0F172A',
  },
  insightIntro: {
    fontSize: 12,
    color: '#64748B',
    textTransform: 'uppercase' as const,
    letterSpacing: 0.4,
  },
  insightList: {
    gap: 10,
  },
  insightListItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
  },
  insightBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    marginTop: 7,
    flexShrink: 0,
  },
  insightText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 20,
    color: '#1F2937',
  },
  sessionCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    borderWidth: 1,
    borderColor: '#E2E8F0',
    overflow: 'hidden',
    shadowColor: '#0F172A',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 10,
    elevation: 2,
  },
  sessionHeaderStatic: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 18,
    backgroundColor: '#F8FAFC',
  },
  sessionHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    flex: 1,
  },
  sessionBadge: {
    width: 38,
    height: 38,
    borderRadius: 19,
    backgroundColor: '#1E40AF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  sessionBadgeText: {
    fontSize: 20,
    fontWeight: '800' as const,
    color: '#FFFFFF',
  },
  sessionNumber: {
    fontSize: 18,
    fontWeight: '800' as const,
    color: '#1E40AF',
    letterSpacing: -0.5,
  },
  sessionTitle: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: '#4B5563',
    marginTop: 2,
  },
  sessionContent: {
    padding: 16,
    gap: 12,
    backgroundColor: '#FFFFFF',
  },
  stepCard: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    overflow: 'hidden',
  },
  stepHeaderStatic: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 14,
    backgroundColor: '#FFFFFF',
  },
  stepHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flex: 1,
  },
  stepIconContainer: {
    width: 32,
    height: 32,
    borderRadius: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepTitle: {
    fontSize: 14,
    fontWeight: '700' as const,
    color: '#111827',
    letterSpacing: -0.2,
    flex: 1,
  },
  stepContent: {
    padding: 16,
    paddingTop: 12,
    gap: 12,
    backgroundColor: '#F9FAFB',
  },
  contentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  contentBullet: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: '#3B82F6',
    marginTop: 7,
    flexShrink: 0,
  },
  contentBody: {
    flex: 1,
    gap: 4,
  },
  contentExplanationBox: {
    marginTop: 4,
    paddingLeft: 10,
    borderLeftWidth: 2,
    borderLeftColor: '#D1D5DB',
    gap: 2,
  },
  contentExplanationLabel: {
    fontSize: 11,
    fontWeight: '600' as const,
    letterSpacing: 0.4,
    textTransform: 'uppercase' as const,
    color: '#64748B',
  },
  contentText: {
    flex: 1,
    fontSize: 14,
    lineHeight: 22,
    color: '#374151',
    letterSpacing: -0.1,
  },
  contentExplanation: {
    fontSize: 13,
    lineHeight: 19,
    color: '#4B5563',
  },
  productCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 10,
    padding: 14,
    borderLeftWidth: 3,
    borderLeftColor: '#2563EB',
  },
  productHeader: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10,
    marginBottom: 6,
  },
  productBullet: {
    width: 7,
    height: 7,
    borderRadius: 3.5,
    backgroundColor: '#2563EB',
    marginTop: 6,
    flexShrink: 0,
  },
  productName: {
    flex: 1,
    fontSize: 14,
    fontWeight: '700' as const,
    lineHeight: 21,
    color: '#111827',
    letterSpacing: -0.2,
  },
  productExplanation: {
    marginLeft: 17,
    marginTop: 6,
    paddingLeft: 10,
    borderLeftWidth: 2,
    borderLeftColor: '#D1D5DB',
    gap: 4,
  },
  productExplanationLabel: {
    fontSize: 11,
    fontWeight: '600' as const,
    letterSpacing: 0.4,
    textTransform: 'uppercase' as const,
    color: '#64748B',
  },
  productExplanationText: {
    fontSize: 13,
    lineHeight: 19,
    color: '#4B5563',
    letterSpacing: -0.1,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingHorizontal: 20,
    paddingTop: 16,
    gap: 12,
  },
  chatButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    paddingVertical: 14,
    borderRadius: 12,
    gap: 8,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  chatButtonText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  actionsRow: {
    flexDirection: 'row',
    gap: 12,
  },
  actionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    paddingVertical: 16,
    borderRadius: 16,
    gap: 10,
  },
  actionButtonText: {
    fontSize: 15,
    fontWeight: '700' as const,
    color: Colors.light.primary,
    letterSpacing: -0.2,
  },
  finishButton: {
    backgroundColor: Colors.light.primary,
    paddingVertical: 18,
    borderRadius: 16,
    alignItems: 'center',
    flexDirection: 'row',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  finishButtonDisabled: {
    opacity: 0.5,
  },
  finishButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
  chatHeader: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 12,
    backgroundColor: Colors.light.background,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  backButton: {
    paddingVertical: 8,
  },
  backButtonText: {
    fontSize: 15,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  chatHeaderTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginTop: 4,
  },
  chatList: {
    padding: 16,
    gap: 12,
  },
  messageContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
  },
  userMessageContainer: {
    justifyContent: 'flex-end',
  },
  messageBubble: {
    maxWidth: '80%',
    borderRadius: 24,
    padding: 16,
    gap: 8,
  },
  aiBubble: {
    backgroundColor: Colors.light.background,
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
  },
  userBubble: {
    backgroundColor: Colors.light.primary,
    borderBottomRightRadius: 6,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 2,
  },
  userMessageText: {
    color: Colors.light.background,
  },
  timestamp: {
    fontSize: 11,
    color: Colors.light.textLight,
    alignSelf: 'flex-end',
  },
  userTimestamp: {
    color: Colors.light.background,
    opacity: 0.7,
  },
  inputContainer: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  inputRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 10,
  },
  input: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 24,
    paddingHorizontal: 18,
    paddingVertical: 14,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
    maxHeight: 100,
  },
  sendButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 3,
  },
  sendButtonDisabled: {
    opacity: 0.4,
  },
  loadingMessageContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    paddingVertical: 8,
  },
  loadingMessageText: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontStyle: 'italic' as const,
  },
});
