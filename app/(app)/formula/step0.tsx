import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  FlatList,
  TouchableOpacity,
  TextInput,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter } from 'expo-router';
import { Search, User, ChevronRight, UserPlus } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useClients } from '@/contexts/ClientContext';
import { useFormula } from '@/contexts/FormulaContext';
import Button from '@/components/ui/Button';
import ProgressIndicator from '@/components/ProgressIndicator';
import type { Client } from '@/types';

export default function Step0Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clients } = useClients();
  const { selectClient, resetFormula } = useFormula();
  const [search, setSearch] = useState('');
  const [isResetting, setIsResetting] = useState(false);

  // REMOVED: Auto-clean logic that was clearing data during active workflow
  // The previous logic was too aggressive and cleared data when navigating between screens
  // Now we only clean explicitly when user selects a new client (see handleSelectClient)

  const filteredClients = clients.filter((client) =>
    client.name.toLowerCase().includes(search.toLowerCase())
  );

  const formatDate = (date?: Date) => {
    if (!date) return 'Nueva';
    return date.toLocaleDateString('es-ES', { day: 'numeric', month: 'short', year: 'numeric' });
  };

  const handleSelectClient = async (client: Client) => {
    if (isResetting) return; // Prevenir doble-tap

    // SECURITY: Validate that client exists in user's client list (prevent IDOR)
    const isValidClient = clients.some(c => c.id === client.id);
    if (!isValidClient) {
      console.error('[Step0][SECURITY] Attempted to select client not in user list:', client.id);
      Alert.alert(
        'Error',
        'Cliente no encontrado en tu lista.',
        [{ text: 'OK' }]
      );
      return;
    }

    setIsResetting(true);
    try {
      // Limpiar datos de sesión anterior antes de seleccionar nuevo cliente
      await resetFormula();
      selectClient(client);
      router.push(`/formula/step1?clientId=${client.id}`);
    } catch (error) {
      console.error('[Step0] Error al iniciar sesión:', error);
      Alert.alert(
        'Error',
        'No se pudo iniciar la sesión. Por favor, intenta de nuevo.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsResetting(false);
    }
  };

  return (
    <View style={styles.container}>
      <View style={[styles.header, { paddingTop: insets.top + 20 }]}>
        <Text style={styles.title}>Seleccionar Cliente</Text>
        <Text style={styles.subtitle}>Elige el cliente para esta formulación</Text>

        <View style={styles.searchContainer}>
          <Search color={Colors.light.textLight} size={20} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar cliente..."
            placeholderTextColor={Colors.light.textLight}
            value={search}
            onChangeText={setSearch}
          />
        </View>
      </View>

      <ProgressIndicator currentStep={1} totalSteps={6} />

      <FlatList
        data={filteredClients}
        keyExtractor={(item) => item.id}
        contentContainerStyle={[styles.list, { paddingBottom: insets.bottom + 100 }]}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[styles.clientCard, isResetting && styles.clientCardDisabled]}
            onPress={() => handleSelectClient(item)}
            activeOpacity={0.7}
            disabled={isResetting}
          >
            <View style={styles.avatar}>
              <User color={Colors.light.primary} size={24} />
            </View>
            <View style={styles.clientInfo}>
              <Text style={styles.clientName}>{item.name}</Text>
              {item.notes && <Text style={styles.clientNotes}>{item.notes}</Text>}
              {item.lastVisit && (
                <Text style={styles.clientDate}>Última visita: {formatDate(item.lastVisit)}</Text>
              )}
            </View>
            {isResetting ? (
              <ActivityIndicator color={Colors.light.primary} size="small" />
            ) : (
              <ChevronRight color={Colors.light.textSecondary} size={20} />
            )}
          </TouchableOpacity>
        )}
        ListEmptyComponent={
          <View style={styles.emptyContainer}>
            <UserPlus color={Colors.light.textLight} size={48} />
            <Text style={styles.emptyTitle}>No hay clientes</Text>
            <Text style={styles.emptySubtitle}>Agrega un nuevo cliente para comenzar</Text>
            <Button
              title="Agregar Cliente"
              onPress={() => router.push('/clients/new')}
              variant="primary"
              size="medium"
              icon={<UserPlus color={Colors.light.background} size={20} />}
            />
          </View>
        }
      />

      {filteredClients.length > 0 && (
        <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
          <Button
            title="Agregar Nuevo Cliente"
            onPress={() => router.push('/clients/new')}
            variant="secondary"
            size="medium"
            icon={<UserPlus color={Colors.light.primary} size={20} />}
            fullWidth
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  header: {
    paddingHorizontal: 24,
    paddingBottom: 24,
    backgroundColor: Colors.light.background,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  title: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: Colors.light.text,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 20,
    fontWeight: '500' as const,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 14,
    gap: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
  },
  list: {
    padding: 20,
    gap: 16,
  },
  clientCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 18,
    gap: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  clientCardDisabled: {
    opacity: 0.5,
  },
  avatar: {
    width: 56,
    height: 56,
    borderRadius: 28,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clientInfo: {
    flex: 1,
    gap: 6,
  },
  clientName: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.text,
    letterSpacing: -0.2,
  },
  clientNotes: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  clientDate: {
    fontSize: 13,
    color: Colors.light.textLight,
    fontWeight: '600' as const,
    marginTop: 2,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 60,
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
});
