import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  TextInput,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { ChevronRight, Search } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useFormula } from '@/contexts/FormulaContext';
import { useClients } from '@/contexts/ClientContext';
import { BrandAccordion } from '@/components/BrandAccordion';
import brandsData from '@/assets/data/brands.json';
import type { ProfessionalBrand } from '@/types';
import ProgressIndicator from '@/components/ProgressIndicator';

const BRANDS: ProfessionalBrand[] = brandsData as ProfessionalBrand[];

export default function Step4Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const { clients } = useClients();
  const { formulaData, selectedClient, selectClient, updateBrand } = useFormula();

  // Restore client from route params if context is empty
  React.useEffect(() => {
    if (clientId && !selectedClient) {
      const client = clients.find(c => c.id === clientId);
      if (client) {
        selectClient(client);
      }
    }
  }, [clientId, selectedClient, clients, selectClient]);

  const [searchQuery, setSearchQuery] = useState('');
  const [selectedBrandId, setSelectedBrandId] = useState<string>(
    formulaData.brand ? BRANDS.find(b => b.name === formulaData.brand)?.id || '' : ''
  );
  const [selectedLine, setSelectedLine] = useState<string>(formulaData.productLine || '');
  const [expandedBrandId, setExpandedBrandId] = useState<string | null>(null);

  const filteredBrands = BRANDS.filter((brand) =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleBrandToggle = (brandId: string) => {
    if (selectedBrandId === brandId) {
      // Toggle expansion if same brand clicked
      setExpandedBrandId(expandedBrandId === brandId ? null : brandId);
    } else {
      // Select new brand and auto-expand
      setSelectedBrandId(brandId);
      setSelectedLine(''); // Empty string = AI mode
      setExpandedBrandId(brandId);
    }
  };

  const handleLineSelect = (brandId: string, line: string) => {
    setSelectedLine(line);
  };

  const handleSave = () => {
    const selectedBrand = BRANDS.find(b => b.id === selectedBrandId);
    if (!selectedBrand) {
      alert('Por favor, selecciona una marca.');
      return;
    }

    updateBrand(selectedBrand.name, selectedLine || undefined, selectedBrand.tier);
    router.push(`/formula/step5?clientId=${clientId}`);
  };

  const canProceed = !!selectedBrandId;

  return (
    <View style={styles.container}>
      <ProgressIndicator currentStep={5} totalSteps={6} />
      <ScrollView
        contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 100 }]}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>Selección de Marca</Text>
        <Text style={styles.subtitle}>
          {BRANDS.length} marcas profesionales disponibles
        </Text>

        <View style={styles.searchContainer}>
          <Search color={Colors.light.textLight} size={20} />
          <TextInput
            style={styles.searchInput}
            placeholder="Buscar marca..."
            placeholderTextColor={Colors.light.textLight}
            value={searchQuery}
            onChangeText={setSearchQuery}
          />
        </View>

        <Text style={styles.resultsCount}>
          {filteredBrands.length} marca{filteredBrands.length !== 1 ? 's' : ''} encontrada
          {filteredBrands.length !== 1 ? 's' : ''}
        </Text>

        <View style={styles.brandsContainer}>
          {filteredBrands.map((brand) => (
            <BrandAccordion
              key={brand.id}
              brand={brand}
              isExpanded={expandedBrandId === brand.id}
              isSelected={selectedBrandId === brand.id}
              onToggle={() => handleBrandToggle(brand.id)}
              selectedLine={selectedBrandId === brand.id ? selectedLine : undefined}
              onLineSelect={(line) => handleLineSelect(brand.id, line)}
            />
          ))}
        </View>
      </ScrollView>

      <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[styles.nextButton, !canProceed && styles.nextButtonDisabled]}
          onPress={handleSave}
          disabled={!canProceed}
        >
          <Text style={styles.nextButtonText}>Generar Fórmula</Text>
          <ChevronRight color={Colors.light.background} size={20} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: Colors.light.text,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 24,
    fontWeight: '500' as const,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 14,
    gap: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
  },
  resultsCount: {
    fontSize: 14,
    color: Colors.light.textLight,
    marginBottom: 16,
    fontWeight: '500' as const,
  },
  brandsContainer: {
    gap: 12,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  nextButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 16,
    gap: 10,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
});
