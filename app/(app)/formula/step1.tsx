import React, { useState, useRef, useEffect } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Image,
  ActivityIndicator,
  TextInput,
  Switch,
  Modal,
  Pressable,
  Alert,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import * as ImagePicker from 'expo-image-picker';
import * as ImageManipulator from 'expo-image-manipulator';
import { Camera, ChevronRight, X, ImageIcon, ChevronDown, ChevronUp, Check } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useFormula } from '@/contexts/FormulaContext';
import { useClients } from '@/contexts/ClientContext';
import { generateTextSafe } from '@/lib/ai-client';
import { showVisionSafetyError, isVisionSafetyError } from '@/lib/vision-safety-utils';
import type { HairAnalysis, ZoneAnalysis } from '@/types';
import PhotoGuidance from '@/components/PhotoGuidance';
import ProgressIndicator from '@/components/ProgressIndicator';

type Section = 'chemical' | 'measurements' | 'general' | 'roots' | 'mids' | 'ends' | 'gray' | 'demarcation';

type ChemicalProcess = {
  value: string;
  label: string;
};

type ToneOption = {
  value: string;
  label: string;
  color: string;
};

type CuticleState = {
  value: string;
  label: string;
  icon: string;
};

type GrayType = {
  value: string;
  label: string;
};

type DistributionPattern = {
  value: string;
  label: string;
};

export default function Step1Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const { clients } = useClients();
  const { formulaData, selectedClient, selectClient, updateCurrentColorImages, updateCurrentColorAnalysis } = useFormula();

  // Restore client from route params if context is empty
  React.useEffect(() => {
    if (clientId && !selectedClient) {
      const client = clients.find(c => c.id === clientId);
      if (client) {
        selectClient(client);
      }
    }
  }, [clientId, selectedClient, clients, selectClient]);

  const [images, setImages] = useState<string[]>(formulaData.currentColorImages);
  const [analysis, setAnalysis] = useState<HairAnalysis | undefined>(formulaData.currentColorAnalysis);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [expandedSections, setExpandedSections] = useState<Set<Section>>(new Set());
  const [isVerified, setIsVerified] = useState(false);
  
  const [showChemicalModal, setShowChemicalModal] = useState(false);
  const [showToneModal, setShowToneModal] = useState(false);
  const [showReflectionModal, setShowReflectionModal] = useState(false);
  const [showCuticleModal, setShowCuticleModal] = useState(false);
  const [showGrayTypeModal, setShowGrayTypeModal] = useState(false);
  const [showDistributionModal, setShowDistributionModal] = useState(false);
  const [modalTarget, setModalTarget] = useState<{
    zone?: 'roots' | 'mids' | 'ends';
    field: string;
  } | null>(null);

  // Fix #6: AbortController to cancel AI analysis on unmount (memory leak prevention)
  const abortControllerRef = useRef<AbortController | null>(null);

  // Fix #6: Cleanup on unmount
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        console.log('[Step1] Aborting ongoing AI analysis on unmount');
        abortControllerRef.current.abort();
        abortControllerRef.current = null;
      }
    };
  }, []);

  const toggleSection = (section: Section) => {
    setExpandedSections((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(section)) {
        newSet.delete(section);
      } else {
        newSet.add(section);
      }
      return newSet;
    });
  };

  const pickImages = async () => {
    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsMultipleSelection: true,
      quality: 0.8,
    });

    if (!result.canceled && result.assets.length > 0) {
      setIsUploading(true);
      try {
        // Procesar cada imagen (resize + JPEG) para consistencia
        const processedUris = await Promise.all(
          result.assets.map(async (asset) => {
            const manipulated = await ImageManipulator.manipulateAsync(
              asset.uri,
              [{ resize: { width: 2048 } }], // Resize manteniendo aspect ratio
              { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
            );
            return manipulated.uri;
          })
        );

        // Guardar URIs locales procesadas (NO subir a Storage todavía)
        const newImages = [...images, ...processedUris].slice(0, 6);
        setImages(newImages);
      } catch (error) {
        console.error('Error al procesar imágenes:', error);
        alert('Error al procesar las imágenes. Por favor, intenta de nuevo.');
      } finally {
        setIsUploading(false);
      }
    }
  };

  const takePhoto = async () => {
    // Request camera permissions
    const { status } = await ImagePicker.requestCameraPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert('Permiso necesario', 'Se necesita permiso para acceder a la cámara');
      return;
    }

    setIsUploading(true);
    try {
      const result = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'],
        allowsEditing: false,
        quality: 0.8,
      });

      if (!result.canceled && result.assets.length > 0) {
        // Procesar imagen (resize + JPEG) para consistencia
        const manipulated = await ImageManipulator.manipulateAsync(
          result.assets[0].uri,
          [{ resize: { width: 2048 } }],
          { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
        );

        // Guardar URI local procesada (NO subir a Storage todavía)
        const newImages = [...images, manipulated.uri].slice(0, 6);
        setImages(newImages);
      }
    } catch (error) {
      console.error('Error al tomar foto:', error);
      Alert.alert('Error', 'Error al tomar la foto. Por favor, intenta de nuevo.');
    } finally {
      setIsUploading(false);
    }
  };

  const removeImage = (index: number) => {
    const newImages = images.filter((_, i) => i !== index);
    setImages(newImages);

    // Invalidar verificación siempre que se eliminen fotos (análisis puede estar desactualizado)
    if (analysis && isVerified) {
      setIsVerified(false);
    }

    // Si se eliminan todas las imágenes, limpiar el análisis completamente
    if (newImages.length === 0) {
      setAnalysis(undefined);
      setIsVerified(false);
    }
  };

  const analyzeImages = async () => {
    if (images.length === 0) return;

    setIsAnalyzing(true);

    // Fix #6: Create AbortController for this AI analysis (memory leak prevention)
    abortControllerRef.current = new AbortController();

    try {
      const systemPrompt = `Eres un experto analista de coloración capilar profesional especializado en análisis técnico de cabello.

IMPORTANTE: Tu trabajo es analizar EXCLUSIVAMENTE el cabello visible en las imágenes. NO analices ni comentes sobre rostros, personas, identidades, características faciales, edad, género, raza o cualquier característica personal. Si aparece una persona en la imagen, ignórala completamente y enfócate SOLO en el cabello.

Tu análisis debe enfocarse únicamente en:
- Color del cabello (tonos, matices, reflejos)
- Estructura del cabello (grosor, densidad, textura)
- Estado del cabello (daño, porosidad, elasticidad)
- Historial de tratamientos químicos visibles
- Canas y su distribución
- Zonas diferenciadas (raíces, medios, puntas)

Debes devolver un objeto JSON con esta estructura EXACTA:
{
  "chemicalHistory": {
    "lastProcessType": "tipo de último proceso" (coloración/permanente/mechas/balayage/alisado/queratina/henna/ninguno),
    "lastProcessDate": "estimación si es visible",
    "hasUsedHomeRemedies": boolean,
    "homeRemediesDetails": "detalles si aplica"
  },
  "physicalMeasurements": {
    "totalLength": número en cm (estimado),
    "monthlyGrowth": número en cm (estimado promedio 1-1.5cm)
  },
  "generalCharacteristics": {
    "thickness": "fino/medio/grueso",
    "density": "baja/media/alta",
    "predominantTone": "descripción del tono",
    "predominantToneColor": "color hex aproximado",
    "predominantReflection": "descripción del reflejo",
    "predominantReflectionColor": "color hex aproximado",
    "lighteningBase": "descripción del fondo de aclaración",
    "lighteningBaseColor": "color hex aproximado"
  },
  "roots": {
    "level": número 1-10,
    "tone": "descripción",
    "toneColor": "hex",
    "reflection": "descripción",
    "reflectionColor": "hex",
    "lighteningBase": "descripción",
    "lighteningBaseColor": "hex",
    "state": "excelente/bueno/regular/dañado/muy dañado",
    "unwantedShade": "descripción si existe",
    "unwantedShadeColor": "hex si existe",
    "pigmentAccumulation": boolean,
    "cuticleState": "cerrada/semiabierta/abierta/muy dañada",
    "porosity": "baja/media/alta",
    "elasticity": "buena/media/baja",
    "resistance": "alta/media/baja",
    "damageLevel": número 0-10
  },
  "mids": { misma estructura que roots },
  "ends": { misma estructura que roots },
  "grayAnalysis": {
    "percentage": número 0-100,
    "visibility": número 0-100 (qué tan visibles son),
    "type": "vítreo/normal/resistente/amarillento",
    "distributionPattern": "uniforme/en mechones/en zonas/corona/sienes"
  },
  "hasDemarcationBands": boolean,
  "demarcationBandsDetails": "descripción si existen"
}

Analiza cada zona (raíces, medios, puntas) por separado. Sé preciso con los niveles, tonos y estados.`;

      const response = await generateTextSafe({
        messages: [
          { role: 'system', content: systemPrompt },
          {
            role: 'user',
            content: 'Analiza SOLO el cabello visible en estas fotos. Ignora cualquier rostro o persona que aparezca. Proporciona el análisis técnico completo del cabello en formato JSON puro (sin markdown, sin explicaciones adicionales).',
          },
        ],
        maxRetries: 2,
        retryDelay: 1500,
        useCase: 'vision_analysis',
        imageUris: images,
        requestTimeout: 120000, // GPT-4o Vision puede tardar con varias fotos; ampliamos para evitar timeouts
        signal: abortControllerRef.current.signal, // Fix #6: Abort on unmount
      });

      // Intentar extraer JSON de diferentes formatos de respuesta
      let jsonString: string | null = null;

      // 1. Intentar extraer de markdown code block con ```json
      const jsonCodeBlockMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonCodeBlockMatch) {
        jsonString = jsonCodeBlockMatch[1];
      }

      // 2. Si no, intentar con code block genérico ```
      if (!jsonString) {
        const codeBlockMatch = response.match(/```\s*([\s\S]*?)\s*```/);
        if (codeBlockMatch) {
          jsonString = codeBlockMatch[1];
        }
      }

      // 3. Si no, intentar extraer JSON puro
      if (!jsonString) {
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          jsonString = jsonMatch[0];
        }
      }

      if (jsonString) {
        try {
          const parsedAnalysis = JSON.parse(jsonString.trim()) as HairAnalysis;
          setAnalysis(parsedAnalysis);
          setExpandedSections(new Set(['chemical', 'measurements', 'general', 'roots']));
        } catch (parseError) {
          console.error('JSON parse error:', parseError);
          console.error('Extracted JSON string:', jsonString.substring(0, 200));
          throw new Error('El análisis no tiene el formato esperado. Por favor, intenta nuevamente.');
        }
      } else {
        console.error('Could not extract JSON from response:', response.substring(0, 500));
        throw new Error('No se pudo parsear el análisis. La respuesta no contiene JSON válido.');
      }
    } catch (error: any) {
      console.error('[Step1] Error analyzing current color:', error);

      // Vision safety rejection handling
      if (isVisionSafetyError(error)) {
        showVisionSafetyError(
          () => setImages([]),
          () => analyzeImages()
        );
        return;
      }

    } finally {
      setIsAnalyzing(false);
      // Fix #6: Cleanup AbortController after analysis completes
      abortControllerRef.current = null;
    }
  };

  const handleSave = () => {
    if (!analysis || images.length === 0) {
      alert('Por favor, sube y analiza al menos una foto del cabello actual.');
      return;
    }

    if (!isVerified) {
      alert('Por favor, verifica que el análisis es correcto antes de continuar.');
      return;
    }

    updateCurrentColorImages(images);
    updateCurrentColorAnalysis(analysis);
    router.push(`/formula/step2?clientId=${clientId}`);
  };

  const chemicalProcessOptions: ChemicalProcess[] = [
    { value: 'Tinte permanente', label: 'Tinte permanente' },
    { value: 'Demi-permanente / Toner', label: 'Demi-permanente / Toner' },
    { value: 'Decoloración', label: 'Decoloración' },
    { value: 'Mechas / Balayage', label: 'Mechas / Balayage' },
    { value: 'Alisado químico / Formol', label: 'Alisado químico / Formol' },
    { value: 'Keratina', label: 'Keratina' },
    { value: 'Henna / Barros', label: 'Henna / Barros' },
    { value: 'Permanente', label: 'Permanente' },
    { value: 'Ninguno', label: 'Ninguno' },
    { value: 'Otro', label: 'Otro' },
  ];

  const toneOptions: ToneOption[] = [
    { value: 'Rubio platino', label: 'Rubio platino', color: '#F5F5DC' },
    { value: 'Rubio claro', label: 'Rubio claro', color: '#F0E68C' },
    { value: 'Rubio medio', label: 'Rubio medio', color: '#DAA520' },
    { value: 'Rubio oscuro', label: 'Rubio oscuro', color: '#B8860B' },
    { value: 'Castaño claro', label: 'Castaño claro', color: '#CD853F' },
    { value: 'Castaño medio', label: 'Castaño medio', color: '#8B4513' },
    { value: 'Castaño oscuro', label: 'Castaño oscuro', color: '#5C4033' },
    { value: 'Negro', label: 'Negro', color: '#1A1A1A' },
    { value: 'Pelirrojo', label: 'Pelirrojo', color: '#C1440E' },
    { value: 'Cobrizo', label: 'Cobrizo', color: '#B87333' },
  ];

  const reflectionOptions: ToneOption[] = [
    { value: 'Ceniza', label: 'Ceniza', color: '#C0C0C0' },
    { value: 'Dorado', label: 'Dorado', color: '#FFD700' },
    { value: 'Cobrizo', label: 'Cobrizo', color: '#B87333' },
    { value: 'Rojizo', label: 'Rojizo', color: '#DC143C' },
    { value: 'Caoba', label: 'Caoba', color: '#C04000' },
    { value: 'Violeta', label: 'Violeta', color: '#8B00FF' },
    { value: 'Perla', label: 'Perla', color: '#E8E8E8' },
    { value: 'Beige', label: 'Beige', color: '#D2B48C' },
    { value: 'Natural', label: 'Natural', color: '#8B7355' },
    { value: 'Neutro', label: 'Neutro', color: '#A0A0A0' },
  ];

  const cuticleStates: CuticleState[] = [
    { value: 'cerrada', label: 'Cerrada', icon: '🔒' },
    { value: 'semiabierta', label: 'Semiabierta', icon: '🔓' },
    { value: 'abierta', label: 'Abierta', icon: '🔓' },
    { value: 'muy dañada', label: 'Muy dañada', icon: '⚠️' },
  ];

  const grayTypes: GrayType[] = [
    { value: 'vítreo', label: 'Vítreo (transparente y brillante)' },
    { value: 'normal', label: 'Normal (blanco opaco)' },
    { value: 'resistente', label: 'Resistente (difícil de cubrir)' },
    { value: 'amarillento', label: 'Amarillento (con tono cálido)' },
  ];

  const distributionPatterns: DistributionPattern[] = [
    { value: 'uniforme', label: 'Uniforme (distribuidas por todo el cabello)' },
    { value: 'en mechones', label: 'En mechones (agrupadas en secciones)' },
    { value: 'en zonas', label: 'En zonas (concentradas en áreas específicas)' },
    { value: 'corona', label: 'Corona (principalmente en la parte superior)' },
    { value: 'sienes', label: 'Sienes (principalmente en los laterales)' },
    { value: 'frontal', label: 'Frontal (principalmente en la parte delantera)' },
  ];

  const renderColorIndicator = (color?: string) => {
    if (!color) return null;
    return (
      <View
        style={[
          styles.colorIndicator,
          { backgroundColor: color.startsWith('#') ? color : '#888' },
        ]}
      />
    );
  };

  const renderThicknessIndicator = (thickness: string) => {
    const thicknesses = [
      { value: 'fino', label: 'Fino', width: 1 },
      { value: 'medio', label: 'Medio', width: 2.5 },
      { value: 'grueso', label: 'Grueso', width: 4 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {thicknesses.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              thickness === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  generalCharacteristics: {
                    ...analysis.generalCharacteristics,
                    thickness: item.value as any,
                  },
                });
              }
            }}
          >
            <View
              style={[
                styles.thicknessLine,
                { height: item.width },
                thickness === item.value && styles.thicknessLineActive,
              ]}
            />
            <Text
              style={[
                styles.visualSelectLabel,
                thickness === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderDensityIndicator = (density: string) => {
    const densities = [
      { value: 'baja', label: 'Baja', lines: 3 },
      { value: 'media', label: 'Media', lines: 5 },
      { value: 'alta', label: 'Alta', lines: 7 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {densities.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              density === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  generalCharacteristics: {
                    ...analysis.generalCharacteristics,
                    density: item.value as any,
                  },
                });
              }
            }}
          >
            <View style={styles.densityLinesContainer}>
              {Array.from({ length: item.lines }).map((_, i) => (
                <View
                  key={i}
                  style={[
                    styles.densityLine,
                    density === item.value && styles.densityLineActive,
                  ]}
                />
              ))}
            </View>
            <Text
              style={[
                styles.visualSelectLabel,
                density === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderStateIndicator = (state: string, onSelect: (value: string) => void) => {
    const states = [
      { value: 'excelente', label: 'Excelente', color: '#10B981' },
      { value: 'bueno', label: 'Bueno', color: '#34D399' },
      { value: 'regular', label: 'Regular', color: '#FBBF24' },
      { value: 'dañado', label: 'Dañado', color: '#F59E0B' },
      { value: 'muy dañado', label: 'Muy dañado', color: '#EF4444' },
    ];

    return (
      <View style={styles.stateSelectContainer}>
        {states.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.stateOption,
              state === item.value && styles.stateOptionActive,
            ]}
            onPress={() => onSelect(item.value)}
          >
            <View
              style={[
                styles.stateColorDot,
                { backgroundColor: item.color },
                state === item.value && styles.stateColorDotActive,
              ]}
            />
            <Text
              style={[
                styles.stateLabel,
                state === item.value && styles.stateLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderPorosityIndicator = (porosity: string, onSelect: (value: string) => void) => {
    const porosities = [
      { value: 'baja', label: 'Baja', icon: '⬇️' },
      { value: 'media', label: 'Media', icon: '↔️' },
      { value: 'alta', label: 'Alta', icon: '⬆️' },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {porosities.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              porosity === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => onSelect(item.value)}
          >
            <Text style={styles.visualSelectIcon}>{item.icon}</Text>
            <Text
              style={[
                styles.visualSelectLabel,
                porosity === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderElasticityIndicator = (elasticity: string, onSelect: (value: string) => void) => {
    const elasticities = [
      { value: 'buena', label: 'Buena', springs: 3 },
      { value: 'media', label: 'Media', springs: 2 },
      { value: 'baja', label: 'Baja', springs: 1 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {elasticities.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              elasticity === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => onSelect(item.value)}
          >
            <View style={styles.elasticityContainer}>
              {Array.from({ length: item.springs }).map((_, i) => (
                <Text key={i} style={styles.elasticitySpring}>
                  🌀
                </Text>
              ))}
            </View>
            <Text
              style={[
                styles.visualSelectLabel,
                elasticity === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderResistanceIndicator = (resistance: string, onSelect: (value: string) => void) => {
    const resistances = [
      { value: 'alta', label: 'Alta', bars: 3 },
      { value: 'media', label: 'Media', bars: 2 },
      { value: 'baja', label: 'Baja', bars: 1 },
    ];

    return (
      <View style={styles.visualSelectContainer}>
        {resistances.map((item) => (
          <TouchableOpacity
            key={item.value}
            style={[
              styles.visualSelectOption,
              resistance === item.value && styles.visualSelectOptionActive,
            ]}
            onPress={() => onSelect(item.value)}
          >
            <View style={styles.resistanceBarsContainer}>
              {Array.from({ length: 3 }).map((_, i) => (
                <View
                  key={i}
                  style={[
                    styles.resistanceBar,
                    i < item.bars && resistance === item.value && styles.resistanceBarActive,
                    i < item.bars && resistance !== item.value && styles.resistanceBarInactive,
                  ]}
                />
              ))}
            </View>
            <Text
              style={[
                styles.visualSelectLabel,
                resistance === item.value && styles.visualSelectLabelActive,
              ]}
            >
              {item.label}
            </Text>
          </TouchableOpacity>
        ))}
      </View>
    );
  };

  const renderZoneAnalysis = (zone: ZoneAnalysis, zoneKey: 'roots' | 'mids' | 'ends', zoneName: string) => {
    return (
      <View style={styles.zoneContainer}>
        <View style={styles.fieldRow}>
          <Text style={styles.fieldLabel}>Nivel</Text>
          <View style={styles.fieldValueContainer}>
            <TextInput
              style={styles.fieldInput}
              value={zone.level.toString()}
              onChangeText={(text) => {
                if (analysis) {
                  setAnalysis({
                    ...analysis,
                    [zoneKey]: { ...zone, level: parseInt(text) || 1 },
                  });
                }
              }}
              keyboardType="numeric"
            />
            <Text style={styles.fieldUnit}>/10</Text>
          </View>
        </View>

        <TouchableOpacity
          style={styles.selectField}
          onPress={() => {
            setModalTarget({ zone: zoneKey, field: 'tone' });
            setShowToneModal(true);
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.selectFieldLabel}>Tono</Text>
          <View style={styles.selectFieldValue}>
            {renderColorIndicator(zone.toneColor)}
            <Text style={styles.selectFieldText}>{zone.tone || 'Seleccionar'}</Text>
            <ChevronDown color={Colors.light.textSecondary} size={20} />
          </View>
        </TouchableOpacity>

        <TouchableOpacity
          style={styles.selectField}
          onPress={() => {
            setModalTarget({ zone: zoneKey, field: 'reflection' });
            setShowReflectionModal(true);
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.selectFieldLabel}>Reflejo</Text>
          <View style={styles.selectFieldValue}>
            {renderColorIndicator(zone.reflectionColor)}
            <Text style={styles.selectFieldText}>{zone.reflection || 'Seleccionar'}</Text>
            <ChevronDown color={Colors.light.textSecondary} size={20} />
          </View>
        </TouchableOpacity>

        <View style={styles.fieldRow}>
          <Text style={styles.fieldLabel}>Fondo de aclaración</Text>
          <View style={styles.fieldValueContainer}>
            {renderColorIndicator(zone.lighteningBaseColor)}
            <TextInput
              style={[styles.fieldInput, styles.fieldInputExpanded]}
              value={zone.lighteningBase}
              onChangeText={(text) => {
                if (analysis) {
                  setAnalysis({
                    ...analysis,
                    [zoneKey]: { ...zone, lighteningBase: text },
                  });
                }
              }}
            />
          </View>
        </View>

        <View style={styles.fieldColumn}>
          <Text style={styles.fieldLabel}>Estado del cabello</Text>
          {renderStateIndicator(zone.state, (value) => {
            if (analysis) {
              setAnalysis({
                ...analysis,
                [zoneKey]: { ...zone, state: value as any },
              });
            }
          })}
        </View>

        {zone.unwantedShade && (
          <View style={styles.fieldRow}>
            <Text style={styles.fieldLabel}>Matiz no deseado</Text>
            <View style={styles.fieldValueContainer}>
              {renderColorIndicator(zone.unwantedShadeColor)}
              <TextInput
                style={[styles.fieldInput, styles.fieldInputExpanded]}
                value={zone.unwantedShade}
                onChangeText={(text) => {
                  if (analysis) {
                    setAnalysis({
                      ...analysis,
                      [zoneKey]: { ...zone, unwantedShade: text },
                    });
                  }
                }}
              />
            </View>
          </View>
        )}

        <View style={styles.fieldRow}>
          <Text style={styles.fieldLabel}>Acumulación de pigmentos</Text>
          <Switch
            value={zone.pigmentAccumulation}
            onValueChange={(value) => {
              if (analysis) {
                setAnalysis({
                  ...analysis,
                  [zoneKey]: { ...zone, pigmentAccumulation: value },
                });
              }
            }}
            trackColor={{ false: Colors.light.border, true: Colors.light.success }}
            thumbColor={Colors.light.background}
          />
        </View>

        <TouchableOpacity
          style={styles.selectField}
          onPress={() => {
            setModalTarget({ zone: zoneKey, field: 'cuticle' });
            setShowCuticleModal(true);
          }}
          activeOpacity={0.7}
        >
          <Text style={styles.selectFieldLabel}>Estado de la cutícula</Text>
          <View style={styles.selectFieldValue}>
            <Text style={styles.cuticleIcon}>{cuticleStates.find(s => s.value === zone.cuticleState)?.icon || '🔒'}</Text>
            <Text style={styles.selectFieldText}>{zone.cuticleState || 'Seleccionar'}</Text>
            <ChevronDown color={Colors.light.textSecondary} size={20} />
          </View>
        </TouchableOpacity>

        <Text style={styles.subsectionTitle}>Características Físicas</Text>

        <View style={styles.fieldColumn}>
          <Text style={styles.fieldLabel}>Porosidad</Text>
          {renderPorosityIndicator(zone.porosity, (value) => {
            if (analysis) {
              setAnalysis({
                ...analysis,
                [zoneKey]: { ...zone, porosity: value as any },
              });
            }
          })}
        </View>

        <View style={styles.fieldColumn}>
          <Text style={styles.fieldLabel}>Elasticidad</Text>
          {renderElasticityIndicator(zone.elasticity, (value) => {
            if (analysis) {
              setAnalysis({
                ...analysis,
                [zoneKey]: { ...zone, elasticity: value as any },
              });
            }
          })}
        </View>

        <View style={styles.fieldColumn}>
          <Text style={styles.fieldLabel}>Resistencia</Text>
          {renderResistanceIndicator(zone.resistance, (value) => {
            if (analysis) {
              setAnalysis({
                ...analysis,
                [zoneKey]: { ...zone, resistance: value as any },
              });
            }
          })}
        </View>

        <View style={styles.fieldRow}>
          <Text style={styles.fieldLabel}>Nivel de daño</Text>
          <View style={styles.fieldValueContainer}>
            <TextInput
              style={styles.fieldInput}
              value={zone.damageLevel.toString()}
              onChangeText={(text) => {
                if (analysis) {
                  setAnalysis({
                    ...analysis,
                    [zoneKey]: { ...zone, damageLevel: parseInt(text) || 0 },
                  });
                }
              }}
              keyboardType="numeric"
            />
            <Text style={styles.fieldUnit}>/10</Text>
          </View>
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      <ProgressIndicator currentStep={2} totalSteps={6} />
      <ScrollView
        contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 100 }]}
        showsVerticalScrollIndicator={false}
      >
        {selectedClient && (
          <View style={styles.clientBanner}>
            <View style={styles.clientBannerIcon}>
              <Text style={styles.clientBannerInitial}>
                {selectedClient.name.charAt(0).toUpperCase()}
              </Text>
            </View>
            <View style={styles.clientBannerInfo}>
              <Text style={styles.clientBannerLabel}>Cliente</Text>
              <Text style={styles.clientBannerName}>{selectedClient.name}</Text>
            </View>
          </View>
        )}
        <Text style={styles.title}>Analizar Color Actual</Text>
        <Text style={styles.subtitle}>Sube 3-6 fotos del cabello desde diferentes ángulos</Text>

        <PhotoGuidance />

        {isUploading && (
          <View style={styles.uploadingBanner}>
            <ActivityIndicator color={Colors.light.primary} />
            <Text style={styles.uploadingText}>Procesando fotos...</Text>
          </View>
        )}

        {images.length === 0 ? (
          <View style={styles.photoOptionsContainer}>
            <TouchableOpacity style={styles.photoOptionButton} onPress={takePhoto} disabled={isUploading}>
              <View style={styles.photoOptionIcon}>
                <Camera color={Colors.light.primary} size={32} />
              </View>
              <Text style={styles.photoOptionTitle}>Tomar foto</Text>
              <Text style={styles.photoOptionDesc}>Usa la cámara</Text>
            </TouchableOpacity>

            <TouchableOpacity style={styles.photoOptionButton} onPress={pickImages} disabled={isUploading}>
              <View style={styles.photoOptionIcon}>
                <ImageIcon color={Colors.light.primary} size={32} />
              </View>
              <Text style={styles.photoOptionTitle}>Subir fotos</Text>
              <Text style={styles.photoOptionDesc}>Desde galería</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <View style={styles.imagesGrid}>
            {images.map((uri, index) => (
              <View key={index} style={styles.imageContainer}>
                <Image source={{ uri }} style={styles.image} />
                <TouchableOpacity
                  style={styles.removeButton}
                  onPress={() => removeImage(index)}
                >
                  <X color={Colors.light.background} size={16} />
                </TouchableOpacity>
              </View>
            ))}
            
            {images.length < 6 && (
              <View style={styles.addMoreContainer}>
                <TouchableOpacity style={styles.addMoreButton} onPress={takePhoto} disabled={isUploading}>
                  <Camera color={Colors.light.primary} size={20} />
                  <Text style={styles.addMoreText}>Tomar</Text>
                </TouchableOpacity>
                <TouchableOpacity style={styles.addMoreButton} onPress={pickImages} disabled={isUploading}>
                  <ImageIcon color={Colors.light.primary} size={20} />
                  <Text style={styles.addMoreText}>Agregar</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        )}

        {images.length > 0 && !analysis && (
          <TouchableOpacity
            style={[styles.analyzeButton, isAnalyzing && styles.analyzeButtonDisabled]}
            onPress={analyzeImages}
            disabled={isAnalyzing}
          >
            {isAnalyzing ? (
              <>
                <ActivityIndicator color={Colors.light.background} />
                <Text style={styles.analyzeButtonText}>Analizando...</Text>
              </>
            ) : (
              <Text style={styles.analyzeButtonText}>Analizar con IA</Text>
            )}
          </TouchableOpacity>
        )}

        {analysis && (
          <View style={styles.analysisContainer}>
            <View style={styles.analysisHeaderSection}>
              <Text style={styles.analysisTitle}>Análisis Completo</Text>
              <Text style={styles.analysisSubtitle}>Revisa y edita la información si es necesario</Text>
            </View>

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('chemical')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Historial Químico</Text>
              {expandedSections.has('chemical') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('chemical') && (
              <View style={styles.sectionContent}>
                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ field: 'chemical' });
                    setShowChemicalModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Último proceso químico</Text>
                  <View style={styles.selectFieldValue}>
                    <Text style={styles.selectFieldText}>
                      {analysis.chemicalHistory?.lastProcessType || 'Seleccionar'}
                    </Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Fecha del proceso</Text>
                  <TextInput
                    style={[styles.fieldInput, styles.fieldInputExpanded]}
                    value={analysis.chemicalHistory?.lastProcessDate || ''}
                    onChangeText={(text) =>
                      setAnalysis({
                        ...analysis,
                        chemicalHistory: {
                          ...analysis.chemicalHistory,
                          lastProcessDate: text,
                        },
                      })
                    }
                    placeholder="Ej: hace 2 meses"
                  />
                </View>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Remedios caseros</Text>
                  <Switch
                    value={analysis.chemicalHistory?.hasUsedHomeRemedies || false}
                    onValueChange={(value) =>
                      setAnalysis({
                        ...analysis,
                        chemicalHistory: {
                          ...analysis.chemicalHistory,
                          hasUsedHomeRemedies: value,
                        },
                      })
                    }
                    trackColor={{ false: Colors.light.border, true: Colors.light.success }}
                    thumbColor={Colors.light.background}
                  />
                </View>

                {analysis.chemicalHistory?.hasUsedHomeRemedies && (
                  <View style={styles.fieldRow}>
                    <Text style={styles.fieldLabel}>Detalles</Text>
                    <TextInput
                      style={[styles.fieldInput, styles.fieldInputExpanded, styles.fieldInputMultiline]}
                      value={analysis.chemicalHistory?.homeRemediesDetails || ''}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          chemicalHistory: {
                            ...analysis.chemicalHistory,
                            homeRemediesDetails: text,
                          },
                        })
                      }
                      multiline
                      numberOfLines={2}
                    />
                  </View>
                )}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('measurements')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Mediciones Físicas</Text>
              {expandedSections.has('measurements') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('measurements') && (
              <View style={styles.sectionContent}>
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Longitud total</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.physicalMeasurements.totalLength.toString()}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          physicalMeasurements: {
                            ...analysis.physicalMeasurements,
                            totalLength: parseInt(text) || 0,
                          },
                        })
                      }
                      keyboardType="numeric"
                    />
                    <Text style={styles.fieldUnit}>cm</Text>
                  </View>
                </View>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Crecimiento mensual</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.physicalMeasurements.monthlyGrowth.toString()}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          physicalMeasurements: {
                            ...analysis.physicalMeasurements,
                            monthlyGrowth: parseFloat(text) || 0,
                          },
                        })
                      }
                      keyboardType="decimal-pad"
                    />
                    <Text style={styles.fieldUnit}>cm</Text>
                  </View>
                </View>
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('general')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Características Generales</Text>
              {expandedSections.has('general') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('general') && (
              <View style={styles.sectionContent}>
                <View style={styles.fieldColumn}>
                  <Text style={styles.fieldLabel}>Grosor del cabello</Text>
                  {renderThicknessIndicator(analysis.generalCharacteristics.thickness)}
                </View>

                <View style={styles.fieldColumn}>
                  <Text style={styles.fieldLabel}>Densidad del cabello</Text>
                  {renderDensityIndicator(analysis.generalCharacteristics.density)}
                </View>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ field: 'tone' });
                    setShowToneModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Tono predominante</Text>
                  <View style={styles.selectFieldValue}>
                    {renderColorIndicator(analysis.generalCharacteristics.predominantToneColor)}
                    <Text style={styles.selectFieldText}>
                      {analysis.generalCharacteristics.predominantTone || 'Seleccionar'}
                    </Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ field: 'reflection' });
                    setShowReflectionModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Reflejo predominante</Text>
                  <View style={styles.selectFieldValue}>
                    {renderColorIndicator(analysis.generalCharacteristics.predominantReflectionColor)}
                    <Text style={styles.selectFieldText}>
                      {analysis.generalCharacteristics.predominantReflection || 'Seleccionar'}
                    </Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Fondo de aclaración</Text>
                  <View style={styles.fieldValueContainer}>
                    {renderColorIndicator(analysis.generalCharacteristics.lighteningBaseColor)}
                    <TextInput
                      style={[styles.fieldInput, styles.fieldInputExpanded]}
                      value={analysis.generalCharacteristics.lighteningBase}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          generalCharacteristics: {
                            ...analysis.generalCharacteristics,
                            lighteningBase: text,
                          },
                        })
                      }
                    />
                  </View>
                </View>
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('roots')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Análisis de Raíces</Text>
              {expandedSections.has('roots') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('roots') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.roots, 'roots', 'Raíces')}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('mids')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Análisis de Medios</Text>
              {expandedSections.has('mids') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('mids') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.mids, 'mids', 'Medios')}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('ends')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Análisis de Puntas</Text>
              {expandedSections.has('ends') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('ends') && (
              <View style={styles.sectionContent}>
                {renderZoneAnalysis(analysis.ends, 'ends', 'Puntas')}
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('gray')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Análisis de Canas</Text>
              {expandedSections.has('gray') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('gray') && (
              <View style={styles.sectionContent}>
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Porcentaje de canas</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.grayAnalysis.percentage.toString()}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          grayAnalysis: {
                            ...analysis.grayAnalysis,
                            percentage: parseInt(text) || 0,
                          },
                        })
                      }
                      keyboardType="numeric"
                    />
                    <Text style={styles.fieldUnit}>%</Text>
                  </View>
                </View>

                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Visibilidad</Text>
                  <View style={styles.fieldValueContainer}>
                    <TextInput
                      style={styles.fieldInput}
                      value={analysis.grayAnalysis.visibility.toString()}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          grayAnalysis: {
                            ...analysis.grayAnalysis,
                            visibility: parseInt(text) || 0,
                          },
                        })
                      }
                      keyboardType="numeric"
                    />
                    <Text style={styles.fieldUnit}>%</Text>
                  </View>
                </View>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ field: 'grayType' });
                    setShowGrayTypeModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Tipo de cana</Text>
                  <View style={styles.selectFieldValue}>
                    <Text style={styles.selectFieldText}>
                      {analysis.grayAnalysis.type || 'Seleccionar'}
                    </Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.selectField}
                  onPress={() => {
                    setModalTarget({ field: 'distribution' });
                    setShowDistributionModal(true);
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={styles.selectFieldLabel}>Patrón de distribución</Text>
                  <View style={styles.selectFieldValue}>
                    <Text style={styles.selectFieldText}>
                      {analysis.grayAnalysis.distributionPattern || 'Seleccionar'}
                    </Text>
                    <ChevronDown color={Colors.light.textSecondary} size={20} />
                  </View>
                </TouchableOpacity>
              </View>
            )}

            <TouchableOpacity
              style={styles.sectionHeader}
              onPress={() => toggleSection('demarcation')}
              activeOpacity={0.7}
            >
              <Text style={styles.sectionTitle}>Bandas de Demarcación</Text>
              {expandedSections.has('demarcation') ? (
                <ChevronUp color={Colors.light.textSecondary} size={20} />
              ) : (
                <ChevronDown color={Colors.light.textSecondary} size={20} />
              )}
            </TouchableOpacity>

            {expandedSections.has('demarcation') && (
              <View style={styles.sectionContent}>
                <View style={styles.fieldRow}>
                  <Text style={styles.fieldLabel}>Presenta bandas</Text>
                  <Switch
                    value={analysis.hasDemarcationBands}
                    onValueChange={(value) =>
                      setAnalysis({
                        ...analysis,
                        hasDemarcationBands: value,
                      })
                    }
                    trackColor={{ false: Colors.light.border, true: Colors.light.success }}
                    thumbColor={Colors.light.background}
                  />
                </View>

                {analysis.hasDemarcationBands && (
                  <View style={styles.fieldRow}>
                    <Text style={styles.fieldLabel}>Detalles</Text>
                    <TextInput
                      style={[styles.fieldInput, styles.fieldInputExpanded, styles.fieldInputMultiline]}
                      value={analysis.demarcationBandsDetails || ''}
                      onChangeText={(text) =>
                        setAnalysis({
                          ...analysis,
                          demarcationBandsDetails: text,
                        })
                      }
                      multiline
                      numberOfLines={2}
                      placeholder="Describe la ubicación y características"
                    />
                  </View>
                )}
              </View>
            )}

            <TouchableOpacity
              style={styles.verificationSection}
              onPress={() => setIsVerified(!isVerified)}
              activeOpacity={0.7}
            >
              <View style={[styles.verificationCheckbox, isVerified && styles.verificationCheckboxActive]}>
                {isVerified && <Check color={Colors.light.background} size={20} strokeWidth={3} />}
              </View>
              <View style={styles.verificationContent}>
                <Text style={styles.verificationTitle}>He verificado el análisis</Text>
                <Text style={styles.verificationSubtitle}>La información es correcta y puedo continuar</Text>
              </View>
            </TouchableOpacity>
          </View>
        )}
      </ScrollView>

      <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[styles.nextButton, (!analysis || images.length === 0 || !isVerified) && styles.nextButtonDisabled]}
          onPress={handleSave}
          disabled={!analysis || images.length === 0 || !isVerified}
        >
          <Text style={styles.nextButtonText}>Continuar</Text>
          <ChevronRight color={Colors.light.background} size={20} />
        </TouchableOpacity>
      </View>

      <Modal
        visible={showChemicalModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowChemicalModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowChemicalModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Último proceso químico</Text>
              <TouchableOpacity
                onPress={() => setShowChemicalModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {chemicalProcessOptions.map((option) => {
                const isSelected = analysis?.chemicalHistory.lastProcessType === option.value;
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        setAnalysis({
                          ...analysis,
                          chemicalHistory: {
                            ...analysis.chemicalHistory,
                            lastProcessType: option.value,
                          },
                        });
                      }
                      setShowChemicalModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                      {option.label}
                    </Text>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showToneModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowToneModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowToneModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seleccionar tono</Text>
              <TouchableOpacity
                onPress={() => setShowToneModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {toneOptions.map((option) => {
                let isSelected = false;
                if (modalTarget?.zone) {
                  isSelected = analysis?.[modalTarget.zone]?.tone === option.value;
                } else {
                  isSelected = analysis?.generalCharacteristics.predominantTone === option.value;
                }
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        if (modalTarget?.zone) {
                          const zone = analysis[modalTarget.zone];
                          setAnalysis({
                            ...analysis,
                            [modalTarget.zone]: {
                              ...zone,
                              tone: option.value,
                              toneColor: option.color,
                            },
                          });
                        } else {
                          setAnalysis({
                            ...analysis,
                            generalCharacteristics: {
                              ...analysis.generalCharacteristics,
                              predominantTone: option.value,
                              predominantToneColor: option.color,
                            },
                          });
                        }
                      }
                      setShowToneModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.modalOptionContent}>
                      <View style={[styles.toneCircle, { backgroundColor: option.color }]} />
                      <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showReflectionModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowReflectionModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowReflectionModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Seleccionar reflejo</Text>
              <TouchableOpacity
                onPress={() => setShowReflectionModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {reflectionOptions.map((option) => {
                let isSelected = false;
                if (modalTarget?.zone) {
                  isSelected = analysis?.[modalTarget.zone]?.reflection === option.value;
                } else {
                  isSelected = analysis?.generalCharacteristics.predominantReflection === option.value;
                }
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        if (modalTarget?.zone) {
                          const zone = analysis[modalTarget.zone];
                          setAnalysis({
                            ...analysis,
                            [modalTarget.zone]: {
                              ...zone,
                              reflection: option.value,
                              reflectionColor: option.color,
                            },
                          });
                        } else {
                          setAnalysis({
                            ...analysis,
                            generalCharacteristics: {
                              ...analysis.generalCharacteristics,
                              predominantReflection: option.value,
                              predominantReflectionColor: option.color,
                            },
                          });
                        }
                      }
                      setShowReflectionModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.modalOptionContent}>
                      <View style={[styles.toneCircle, { backgroundColor: option.color }]} />
                      <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showCuticleModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowCuticleModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowCuticleModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Estado de la cutícula</Text>
              <TouchableOpacity
                onPress={() => setShowCuticleModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {cuticleStates.map((option) => {
                const isSelected = modalTarget?.zone && analysis?.[modalTarget.zone]?.cuticleState === option.value;
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis && modalTarget?.zone) {
                        const zone = analysis[modalTarget.zone];
                        setAnalysis({
                          ...analysis,
                          [modalTarget.zone]: {
                            ...zone,
                            cuticleState: option.value as any,
                          },
                        });
                      }
                      setShowCuticleModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <View style={styles.modalOptionContent}>
                      <Text style={styles.cuticleIconModal}>{option.icon}</Text>
                      <Text style={[styles.modalOptionText, isSelected && styles.modalOptionTextSelected]}>
                        {option.label}
                      </Text>
                    </View>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showGrayTypeModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowGrayTypeModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowGrayTypeModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Tipo de cana</Text>
              <TouchableOpacity
                onPress={() => setShowGrayTypeModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {grayTypes.map((option) => {
                const isSelected = analysis?.grayAnalysis.type === option.value;
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        setAnalysis({
                          ...analysis,
                          grayAnalysis: {
                            ...analysis.grayAnalysis,
                            type: option.value as any,
                          },
                        });
                      }
                      setShowGrayTypeModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.modalOptionTextMultiline, isSelected && styles.modalOptionTextSelected]}>
                      {option.label}
                    </Text>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>

      <Modal
        visible={showDistributionModal}
        transparent
        animationType="slide"
        onRequestClose={() => setShowDistributionModal(false)}
      >
        <Pressable
          style={styles.modalOverlay}
          onPress={() => setShowDistributionModal(false)}
        >
          <Pressable style={[styles.modalContent, { paddingBottom: insets.bottom + 20 }]} onPress={e => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Patrón de distribución</Text>
              <TouchableOpacity
                onPress={() => setShowDistributionModal(false)}
                hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
              >
                <X color={Colors.light.textSecondary} size={24} />
              </TouchableOpacity>
            </View>
            <ScrollView style={styles.modalScroll} showsVerticalScrollIndicator={false}>
              {distributionPatterns.map((option) => {
                const isSelected = analysis?.grayAnalysis.distributionPattern === option.value;
                return (
                  <TouchableOpacity
                    key={option.value}
                    style={[styles.modalOption, isSelected && styles.modalOptionSelected]}
                    onPress={() => {
                      if (analysis) {
                        setAnalysis({
                          ...analysis,
                          grayAnalysis: {
                            ...analysis.grayAnalysis,
                            distributionPattern: option.value as any,
                          },
                        });
                      }
                      setShowDistributionModal(false);
                    }}
                    activeOpacity={0.7}
                  >
                    <Text style={[styles.modalOptionTextMultiline, isSelected && styles.modalOptionTextSelected]}>
                      {option.label}
                    </Text>
                    {isSelected && <Check color={Colors.light.primary} size={20} />}
                  </TouchableOpacity>
                );
              })}
            </ScrollView>
          </Pressable>
        </Pressable>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: Colors.light.text,
    marginBottom: 8,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 24,
    fontWeight: '500' as const,
  },
  imagesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
    marginBottom: 20,
  },
  imageContainer: {
    position: 'relative',
    width: '30%',
    aspectRatio: 1,
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 12,
  },
  removeButton: {
    position: 'absolute',
    top: -6,
    right: -6,
    backgroundColor: Colors.light.text,
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  analyzeButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    borderRadius: 12,
    gap: 8,
    marginBottom: 20,
  },
  analyzeButtonDisabled: {
    opacity: 0.6,
  },
  analyzeButtonText: {
    color: Colors.light.background,
    fontSize: 16,
    fontWeight: '600' as const,
  },
  analysisContainer: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
    padding: 0,
    overflow: 'hidden',
  },
  analysisHeaderSection: {
    padding: 24,
    backgroundColor: '#F8FAFC',
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  analysisTitle: {
    fontSize: 22,
    fontWeight: '700' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  analysisSubtitle: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 18,
    backgroundColor: Colors.light.background,
    borderTopWidth: 1,
    borderTopColor: '#F1F5F9',
  },
  sectionTitle: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
    letterSpacing: -0.2,
  },
  sectionContent: {
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 20,
    backgroundColor: Colors.light.background,
  },
  subsectionTitle: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.textSecondary,
    marginTop: 12,
    marginBottom: 4,
  },
  fieldRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    gap: 12,
  },
  fieldLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    flex: 1,
  },
  fieldValueContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    flex: 1,
    justifyContent: 'flex-end',
  },
  fieldInput: {
    fontSize: 15,
    fontWeight: '500' as const,
    color: Colors.light.text,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    paddingVertical: 6,
    paddingHorizontal: 8,
    minWidth: 80,
    textAlign: 'right',
  },
  fieldInputExpanded: {
    flex: 1,
  },
  fieldInputMultiline: {
    textAlign: 'left',
    minHeight: 60,
  },
  fieldUnit: {
    fontSize: 14,
    color: Colors.light.textSecondary,
  },
  colorIndicator: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  zoneContainer: {
    gap: 16,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  nextButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 18,
    borderRadius: 16,
    gap: 10,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
  tipsHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 14,
    borderRadius: 12,
    marginBottom: 16,
  },
  tipsHeaderContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  tipsHeaderText: {
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  tipsContainer: {
    backgroundColor: '#F8F9FB',
    padding: 16,
    borderRadius: 12,
    marginBottom: 20,
    borderLeftWidth: 3,
    borderLeftColor: Colors.light.primary,
  },
  tipTitle: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 10,
  },
  tipsList: {
    gap: 6,
  },
  tipItem: {
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },
  photoOptionsContainer: {
    flexDirection: 'row',
    gap: 12,
    marginBottom: 20,
  },
  photoOptionButton: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 16,
    padding: 20,
    alignItems: 'center',
    gap: 8,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  photoOptionIcon: {
    width: 64,
    height: 64,
    borderRadius: 32,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 4,
  },
  photoOptionTitle: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  photoOptionDesc: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  addMoreContainer: {
    width: '30%',
    aspectRatio: 1,
    gap: 6,
  },
  addMoreButton: {
    flex: 1,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 4,
  },
  addMoreText: {
    fontSize: 10,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  fieldColumn: {
    gap: 12,
  },
  visualSelectContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  visualSelectOption: {
    flex: 1,
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 12,
    alignItems: 'center',
    justifyContent: 'center',
    gap: 8,
    borderWidth: 2,
    borderColor: Colors.light.border,
    minHeight: 80,
  },
  visualSelectOptionActive: {
    borderColor: Colors.light.primary,
    backgroundColor: '#EEF2FF',
  },
  visualSelectLabel: {
    fontSize: 13,
    fontWeight: '500' as const,
    color: Colors.light.textSecondary,
    textAlign: 'center',
  },
  visualSelectLabelActive: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  visualSelectIcon: {
    fontSize: 24,
  },
  thicknessLine: {
    width: 40,
    backgroundColor: Colors.light.border,
    borderRadius: 2,
  },
  thicknessLineActive: {
    backgroundColor: Colors.light.primary,
  },
  densityLinesContainer: {
    flexDirection: 'row',
    gap: 3,
    height: 30,
    alignItems: 'flex-end',
  },
  densityLine: {
    width: 2.5,
    height: '100%',
    backgroundColor: Colors.light.border,
    borderRadius: 1,
  },
  densityLineActive: {
    backgroundColor: Colors.light.primary,
  },
  stateSelectContainer: {
    gap: 8,
  },
  stateOption: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    backgroundColor: Colors.light.background,
    padding: 12,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  stateOptionActive: {
    borderColor: Colors.light.primary,
    backgroundColor: '#EEF2FF',
  },
  stateColorDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
  },
  stateColorDotActive: {
    width: 16,
    height: 16,
    borderRadius: 8,
  },
  stateLabel: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    fontWeight: '500' as const,
  },
  stateLabelActive: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  elasticityContainer: {
    flexDirection: 'row',
    gap: 2,
  },
  elasticitySpring: {
    fontSize: 16,
  },
  resistanceBarsContainer: {
    flexDirection: 'row',
    gap: 3,
    height: 30,
    alignItems: 'flex-end',
  },
  resistanceBar: {
    width: 8,
    height: '33%',
    backgroundColor: Colors.light.border,
    borderRadius: 2,
  },
  resistanceBarActive: {
    backgroundColor: Colors.light.primary,
    height: '100%',
  },
  resistanceBarInactive: {
    backgroundColor: '#C7D2FE',
    height: '100%',
  },
  selectField: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  selectFieldLabel: {
    fontSize: 13,
    fontWeight: '500' as const,
    color: Colors.light.textSecondary,
    marginBottom: 8,
  },
  selectFieldValue: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  selectFieldText: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: Colors.light.text,
    flex: 1,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContent: {
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingTop: 20,
    maxHeight: '80%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
  },
  modalTitle: {
    fontSize: 20,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  modalScroll: {
    paddingHorizontal: 20,
    paddingTop: 12,
  },
  modalOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.light.border,
    backgroundColor: Colors.light.background,
  },
  modalOptionSelected: {
    backgroundColor: '#F0F4FF',
  },
  modalOptionContent: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  modalOptionText: {
    fontSize: 16,
    fontWeight: '500' as const,
    color: Colors.light.text,
  },
  modalOptionTextSelected: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  modalOptionTextMultiline: {
    fontSize: 15,
    fontWeight: '500' as const,
    color: Colors.light.text,
    flex: 1,
    lineHeight: 22,
  },
  toneCircle: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  cuticleIcon: {
    fontSize: 18,
  },
  cuticleIconModal: {
    fontSize: 24,
  },
  verificationSection: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
    padding: 20,
    marginHorizontal: 20,
    marginTop: 24,
    marginBottom: 20,
    backgroundColor: '#F0F9FF',
    borderRadius: 16,
    borderWidth: 2,
    borderColor: '#E0F2FE',
  },
  verificationCheckbox: {
    width: 32,
    height: 32,
    borderRadius: 16,
    borderWidth: 2,
    borderColor: Colors.light.border,
    backgroundColor: Colors.light.background,
    alignItems: 'center',
    justifyContent: 'center',
  },
  verificationCheckboxActive: {
    backgroundColor: Colors.light.primary,
    borderColor: Colors.light.primary,
  },
  verificationContent: {
    flex: 1,
  },
  verificationTitle: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 2,
  },
  verificationSubtitle: {
    fontSize: 13,
    color: Colors.light.textSecondary,
  },
  clientBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.backgroundSecondary,
    borderRadius: 12,
    padding: 12,
    gap: 12,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  clientBannerIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
  clientBannerInitial: {
    color: Colors.light.background,
    fontSize: 18,
    fontWeight: '700' as const,
  },
  clientBannerInfo: {
    flex: 1,
  },
  clientBannerLabel: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    marginBottom: 2,
  },
  clientBannerName: {
    fontSize: 16,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  uploadingBanner: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    backgroundColor: '#EEF2FF',
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 16,
    borderWidth: 1,
    borderColor: Colors.light.primary,
  },
  uploadingText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
});
