import React, { useState, useEffect, useMemo, useCallback } from 'react';
import {
  StyleSheet,
  Text,
  View,
  ScrollView,
  TouchableOpacity,
  Switch,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useRouter, useLocalSearchParams } from 'expo-router';
import { Shield, AlertTriangle } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { useFormula } from '@/contexts/FormulaContext';
import { useClients } from '@/contexts/ClientContext';
import SignaturePad from '@/components/SignaturePad';
import ProgressIndicator from '@/components/ProgressIndicator';

type PatchTestResult = 'pending' | 'negative' | 'positive';
type StrandTestResult = 'pending' | 'pass' | 'fail';

export default function Step3Screen() {
  const insets = useSafeAreaInsets();
  const router = useRouter();
  const { clientId } = useLocalSearchParams<{ clientId: string }>();
  const { clients } = useClients();
  const { formulaData, selectedClient, selectClient, updateSafetyChecklist, updateClientInfo } = useFormula();

  // Restore client from route params if context is empty
  useEffect(() => {
    if (clientId && !selectedClient) {
      const client = clients.find(c => c.id === clientId);
      if (client) {
        selectClient(client);
      }
    }
  }, [clientId, selectedClient, clients, selectClient]);

  const [checklist, setChecklist] = useState(formulaData.safetyChecklist);
  const [signature, setSignature] = useState<string>(formulaData.clientSignature || '');
  const [patchTest, setPatchTest] = useState<PatchTestResult>('pending');
  const [strandTest, setStrandTest] = useState<StrandTestResult>('pending');
  const [metalSalts, setMetalSalts] = useState<boolean>(false);
  const [previousHenna, setPreviousHenna] = useState<boolean>(false);
  const [consent1, setConsent1] = useState<boolean>(false);
  const [consent2, setConsent2] = useState<boolean>(false);
  const [consent3, setConsent3] = useState<boolean>(false);
  const [consent4, setConsent4] = useState<boolean>(false);
  const [consent5, setConsent5] = useState<boolean>(false);
  
  const clientName = selectedClient?.name || formulaData.clientName || '';

  const handleSignatureChange = useCallback((sig: string) => {
    console.log('Firma actualizada, longitud:', sig.length);
    setSignature(sig);
  }, []);

  useEffect(() => {
    if (!selectedClient) {
      console.warn('No hay cliente seleccionado');
    }
  }, [selectedClient]);

  const allSafetyChecked = checklist.gloves && checklist.ventilation && checklist.hairCondition;
  
  const hasValidSignature = useMemo(() => {
    if (!signature || signature.length === 0) {
      console.log('✗ Firma vacía');
      return false;
    }
    try {
      const parsed = JSON.parse(signature);
      const isValid = Array.isArray(parsed) && parsed.length > 0;
      console.log('✓ Firma JSON válida:', isValid, 'paths:', parsed.length);
      return isValid;
    } catch {
      const isValid = signature.trim().length > 10;
      console.log('✓ Firma no-JSON, longitud:', signature.length, 'válida:', isValid);
      return isValid;
    }
  }, [signature]);
  
  const allConsentsChecked = consent1 && consent2 && consent3 && consent4 && consent5;

  const canContinue = allSafetyChecked && hasValidSignature && allConsentsChecked;
  
  useEffect(() => {
    console.log('=== Validación de formulario ===');
    console.log('allSafetyChecked:', allSafetyChecked, '(gloves:', checklist.gloves, ', ventilation:', checklist.ventilation, ', hairCondition:', checklist.hairCondition, ')');
    console.log('hasValidSignature:', hasValidSignature);
    console.log('allConsentsChecked:', allConsentsChecked, '(c1:', consent1, ', c2:', consent2, ', c3:', consent3, ', c4:', consent4, ', c5:', consent5, ')');
    console.log('canContinue:', canContinue);
  }, [allSafetyChecked, hasValidSignature, allConsentsChecked, canContinue, checklist.gloves, checklist.ventilation, checklist.hairCondition, consent1, consent2, consent3, consent4, consent5]);

  const toggleCheck = (key: keyof typeof checklist) => {
    setChecklist({ ...checklist, [key]: !checklist[key] });
  };

  const handleSave = () => {
    console.log('Validando formulario...');
    console.log('allSafetyChecked:', allSafetyChecked);
    console.log('hasValidSignature:', hasValidSignature, 'signature length:', signature.length);
    console.log('allConsentsChecked:', allConsentsChecked);
    
    if (!allSafetyChecked) {
      alert('Por favor, completa el checklist de seguridad.');
      return;
    }

    if (!allConsentsChecked) {
      alert('Por favor, acepta todos los consentimientos.');
      return;
    }

    if (!hasValidSignature) {
      alert('Por favor, firma el documento de seguridad.');
      return;
    }

    if (!clientName.trim()) {
      alert('No hay cliente seleccionado. Por favor, vuelve al paso anterior.');
      return;
    }

    updateSafetyChecklist({
      ...checklist,
      patchTest: patchTest !== 'pending',
      strandTest: strandTest !== 'pending',
      photoConsentGiven: consent5,
    });
    updateClientInfo(clientName.trim(), signature);
    router.push(`/formula/step4?clientId=${clientId}`);
  };

  return (
    <View style={styles.container}>
      <ProgressIndicator currentStep={4} totalSteps={6} />
      <ScrollView
        contentContainerStyle={[styles.content, { paddingBottom: insets.bottom + 100 }]}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>Seguridad</Text>
        <Text style={styles.subtitle}>Checklist y pruebas antes de formular</Text>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Shield color={Colors.light.primary} size={20} />
            <Text style={styles.cardTitle}>Checklist de seguridad</Text>
          </View>
          
          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>EPI (guantes) preparados</Text>
            <Switch
              value={checklist.gloves}
              onValueChange={() => toggleCheck('gloves')}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>Área ventilada correctamente</Text>
            <Switch
              value={checklist.ventilation}
              onValueChange={() => toggleCheck('ventilation')}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>Productos en buen estado (fechas y envases)</Text>
            <Switch
              value={checklist.hairCondition}
              onValueChange={() => toggleCheck('hairCondition')}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardIcon}>✓</Text>
            <Text style={styles.cardTitle}>Test de parche</Text>
          </View>

          <View style={styles.buttonGroup}>
            <TouchableOpacity
              style={[styles.optionButton, patchTest === 'negative' && styles.optionButtonSelected]}
              onPress={() => setPatchTest('negative')}
            >
              <Text style={[styles.optionButtonText, patchTest === 'negative' && styles.optionButtonTextSelected]}>
                Negativo
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionButton, patchTest === 'pending' && styles.optionButtonSelected]}
              onPress={() => setPatchTest('pending')}
            >
              <Text style={[styles.optionButtonText, patchTest === 'pending' && styles.optionButtonTextSelected]}>
                Pendiente
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionButton, patchTest === 'positive' && styles.optionButtonSelected]}
              onPress={() => setPatchTest('positive')}
            >
              <Text style={[styles.optionButtonText, patchTest === 'positive' && styles.optionButtonTextSelected]}>
                Positivo
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardIcon}>⚡</Text>
            <Text style={styles.cardTitle}>Test de resistencia/mechón</Text>
          </View>

          <View style={styles.buttonGroup}>
            <TouchableOpacity
              style={[styles.optionButton, strandTest === 'pass' && styles.optionButtonSelected]}
              onPress={() => setStrandTest('pass')}
            >
              <Text style={[styles.optionButtonText, strandTest === 'pass' && styles.optionButtonTextSelected]}>
                Pasa
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionButton, strandTest === 'pending' && styles.optionButtonSelected]}
              onPress={() => setStrandTest('pending')}
            >
              <Text style={[styles.optionButtonText, strandTest === 'pending' && styles.optionButtonTextSelected]}>
                No realizado
              </Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.optionButton, strandTest === 'fail' && styles.optionButtonSelected]}
              onPress={() => setStrandTest('fail')}
            >
              <Text style={[styles.optionButtonText, strandTest === 'fail' && styles.optionButtonTextSelected]}>
                Falla
              </Text>
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <AlertTriangle color="#F59E0B" size={20} />
            <Text style={styles.cardTitle}>Compatibilidad química</Text>
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>¿Sales metálicas detectadas?</Text>
            <Switch
              value={metalSalts}
              onValueChange={setMetalSalts}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.switchLabel}>¿Henna previa?</Text>
            <Switch
              value={previousHenna}
              onValueChange={setPreviousHenna}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>
        </View>

        <View style={styles.card}>
          <Text style={styles.cardTitle}>Consentimiento informado</Text>

          <View style={styles.switchItem}>
            <Text style={styles.consentLabel}>Entiendo los riesgos químicos (alergias, irritación, daños)</Text>
            <Switch
              value={consent1}
              onValueChange={setConsent1}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.consentLabel}>Confirmo test de parche o acepto proceder bajo mi responsabilidad</Text>
            <Switch
              value={consent2}
              onValueChange={setConsent2}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.consentLabel}>Entiendo que el resultado puede variar según el estado del cabello</Text>
            <Switch
              value={consent3}
              onValueChange={setConsent3}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.consentLabel}>Me comprometo a seguir los cuidados posteriores recomendados</Text>
            <Switch
              value={consent4}
              onValueChange={setConsent4}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>

          <View style={styles.switchItem}>
            <Text style={styles.consentLabel}>
              Autorizo a almacenar fotos de mi cabello de forma segura y cifrada por hasta 90 días
              para análisis y seguimiento de resultados. Puedo revocar este permiso en cualquier
              momento desde Ajustes.
            </Text>
            <Switch
              value={consent5}
              onValueChange={setConsent5}
              trackColor={{ false: '#D1D5DB', true: Colors.light.primary }}
              thumbColor={Colors.light.background}
            />
          </View>
        </View>

        <View style={styles.signatureCard}>
          <Text style={styles.cardTitle}>Firma del cliente</Text>
          <Text style={styles.signatureSubtitle}>{clientName}</Text>
          
          <View style={styles.signatureContainer}>
            <SignaturePad
              onSignatureChange={handleSignatureChange}
              height={200}
            />
          </View>
        </View>
      </ScrollView>

      <View style={[styles.footer, { paddingBottom: insets.bottom + 16 }]}>
        <TouchableOpacity
          style={[styles.backButton]}
          onPress={() => router.back()}
        >
          <Text style={styles.backButtonText}>Atrás</Text>
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.nextButton, !canContinue && styles.nextButtonDisabled]}
          onPress={handleSave}
          disabled={!canContinue}
        >
          <Text style={styles.nextButtonText}>Continuar a Formulación</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
  },
  content: {
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: '800' as const,
    color: Colors.light.text,
    marginBottom: 4,
    textAlign: 'center' as const,
    letterSpacing: -0.5,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.light.textSecondary,
    marginBottom: 24,
    textAlign: 'center' as const,
    fontWeight: '500' as const,
  },
  card: {
    backgroundColor: Colors.light.background,
    borderRadius: 20,
    padding: 20,
    marginBottom: 16,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    marginBottom: 16,
  },
  cardIcon: {
    fontSize: 20,
  },
  cardTitle: {
    fontSize: 17,
    fontWeight: '700' as const,
    color: Colors.light.text,
  },
  switchItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#F3F4F6',
  },
  switchLabel: {
    flex: 1,
    fontSize: 15,
    color: Colors.light.text,
    paddingRight: 12,
  },
  consentLabel: {
    flex: 1,
    fontSize: 14,
    color: Colors.light.text,
    paddingRight: 12,
    lineHeight: 20,
  },
  buttonGroup: {
    flexDirection: 'row',
    gap: 8,
  },
  optionButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 2,
    borderColor: '#E5E7EB',
    backgroundColor: Colors.light.background,
    alignItems: 'center' as const,
  },
  optionButtonSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: '#F3E8FF',
  },
  optionButtonText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  optionButtonTextSelected: {
    color: Colors.light.primary,
  },
  signatureCard: {
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    padding: 20,
    marginBottom: 16,
  },
  signatureSubtitle: {
    fontSize: 15,
    color: Colors.light.textSecondary,
    marginBottom: 16,
    marginTop: 8,
  },
  signatureContainer: {
    marginTop: 8,
  },
  footer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: Colors.light.background,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    paddingHorizontal: 20,
    paddingTop: 16,
    flexDirection: 'row',
    gap: 12,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: -2 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 8,
  },
  backButton: {
    flex: 1,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 16,
    borderRadius: 16,
  },
  backButtonText: {
    color: Colors.light.text,
    fontSize: 16,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
  nextButton: {
    flex: 2,
    backgroundColor: Colors.light.primary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: 16,
    borderRadius: 16,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 10,
    elevation: 4,
  },
  nextButtonDisabled: {
    opacity: 0.5,
  },
  nextButtonText: {
    color: Colors.light.background,
    fontSize: 17,
    fontWeight: '700' as const,
    letterSpacing: -0.2,
  },
});
