import { Stack, useRouter, useSegments } from 'expo-router';
import { useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export default function AuthLayout() {
  const { isAuthenticated, isLoading } = useAuth();
  const router = useRouter();
  const segments = useSegments();

  useEffect(() => {
    if (isLoading) {
      return;
    }

    const firstSegment = segments?.[0];

    // Solo redirigir si está autenticado y está en auth group
    if (isAuthenticated && firstSegment === '(auth)') {
      router.replace('/(app)/(tabs)/chat');
    }
  }, [isAuthenticated, isLoading, segments, router]);

  return <Stack screenOptions={{ headerShown: false }} />;
}
