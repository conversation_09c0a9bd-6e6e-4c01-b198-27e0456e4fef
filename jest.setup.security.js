// Minimal setup for security tests (no Expo, no React Native)

// Global test timeout
jest.setTimeout(10000);

// Silence console in tests
const originalConsole = { ...console };
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn((...args) => {
    const message = args[0]?.toString() || '';
    if (message.includes('[SECURITY]') || message.includes('CRITICAL')) {
      originalConsole.warn(...args);
    }
  }),
  error: jest.fn((...args) => {
    const message = args[0]?.toString() || '';
    if (message.includes('[SECURITY]') || message.includes('CRITICAL')) {
      originalConsole.error(...args);
    }
  }),
};
