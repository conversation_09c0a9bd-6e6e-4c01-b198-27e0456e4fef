#!/bin/bash

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# Test AI Orchestration System (Post-Deployment)
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

set -e

PROJECT_REF="guyxczavhtemwlrknqpm"
FUNCTION_NAME="ai-proxy"

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🧪 Testing AI Orchestration System"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# TEST 1: Check Edge Function Status
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "📊 Test 1: Checking edge function status..."
echo ""

supabase functions list --project-ref $PROJECT_REF

echo ""
echo "   ✅ Edge function status verified"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# TEST 2: Start Live Logs (Background Process)
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "📡 Test 2: Starting live logs monitoring..."
echo ""
echo "   Opening logs in background..."
echo "   (Logs will be saved to: ai-proxy-logs.txt)"
echo ""

# Kill any existing log processes
pkill -f "supabase functions logs $FUNCTION_NAME" 2>/dev/null || true

# Start logging in background
supabase functions logs $FUNCTION_NAME --project-ref $PROJECT_REF --tail > ai-proxy-logs.txt 2>&1 &
LOG_PID=$!

echo "   ✅ Logs monitoring started (PID: $LOG_PID)"
echo "   To view logs in real-time: tail -f ai-proxy-logs.txt"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# TEST 3: Manual Testing Instructions
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📝 MANUAL TESTING INSTRUCTIONS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🔹 Test 1: Hybrid Mode (Formula with Brand)"
echo ""
echo "   1. Start the app:"
echo "      bun run start-web"
echo ""
echo "   2. In the app:"
echo "      - Go to Formula → Step 1 (analyze current color)"
echo "      - Step 2 (analyze desired color)"
echo "      - Step 4 → Select 'L'Oréal Professionnel' + 'INOA'"
echo "      - Step 5 → Generate formula"
echo ""
echo "   3. Expected logs (check ai-proxy-logs.txt):"
echo "      [Formula Routing] Decision: hybrid"
echo "      [Hybrid Executor] Formula generated with X products"
echo "      [Self-Correction] X corrections made"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🔹 Test 2: OpenAI Mode (Formula without Brand)"
echo ""
echo "   1. In the app:"
echo "      - Formula → Step 4 → Leave brand empty"
echo "      - Step 5 → Generate formula"
echo ""
echo "   2. Expected logs:"
echo "      [Formula Routing] Decision: openai"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "🔹 Test 3: Intent Cache"
echo ""
echo "   1. In Chat, ask: 'Dame la lista de productos de Wella Koleston'"
echo "   2. First time → Expected log: [Intent Detection] GPT-4o-mini success"
echo "   3. Ask EXACTLY the same question again"
echo "   4. Second time → Expected log: [Intent Detection] Cache hit"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📊 Monitoring Commands:"
echo ""
echo "   - View live logs:"
echo "     tail -f ai-proxy-logs.txt"
echo ""
echo "   - Search logs for specific patterns:"
echo "     grep 'Hybrid Executor' ai-proxy-logs.txt"
echo "     grep 'Self-Correction' ai-proxy-logs.txt"
echo "     grep 'Cache HIT' ai-proxy-logs.txt"
echo ""
echo "   - Stop log monitoring:"
echo "     kill $LOG_PID"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "Press ENTER when you're done testing to stop log monitoring..."
read

# Stop logging
kill $LOG_PID 2>/dev/null || true
echo ""
echo "✅ Log monitoring stopped"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# TEST 4: Analyze Logs
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 LOG ANALYSIS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

if [ -f ai-proxy-logs.txt ]; then
    echo "🔍 Checking for key indicators..."
    echo ""

    # Check for routing decisions
    HYBRID_COUNT=$(grep -c "\[Formula Routing\] Decision: hybrid" ai-proxy-logs.txt 2>/dev/null || echo "0")
    OPENAI_COUNT=$(grep -c "\[Formula Routing\] Decision: openai" ai-proxy-logs.txt 2>/dev/null || echo "0")

    echo "   Routing Decisions:"
    echo "   - Hybrid mode: $HYBRID_COUNT times"
    echo "   - OpenAI mode: $OPENAI_COUNT times"
    echo ""

    # Check for hybrid executor
    HYBRID_EXEC=$(grep -c "\[Hybrid Executor\]" ai-proxy-logs.txt 2>/dev/null || echo "0")
    echo "   Hybrid Executor: $HYBRID_EXEC executions"
    echo ""

    # Check for self-correction
    CORRECTIONS=$(grep -c "\[Self-Correction\]" ai-proxy-logs.txt 2>/dev/null || echo "0")
    echo "   Self-Corrections: $CORRECTIONS attempts"
    echo ""

    # Check for cache hits
    CACHE_HITS=$(grep -c "Cache HIT" ai-proxy-logs.txt 2>/dev/null || echo "0")
    CACHE_MISS=$(grep -c "Cache MISS" ai-proxy-logs.txt 2>/dev/null || echo "0")

    echo "   Cache Performance:"
    echo "   - Cache HITs: $CACHE_HITS"
    echo "   - Cache MISSes: $CACHE_MISS"

    if [ $((CACHE_HITS + CACHE_MISS)) -gt 0 ]; then
        HIT_RATE=$(echo "scale=2; $CACHE_HITS * 100 / ($CACHE_HITS + $CACHE_MISS)" | bc)
        echo "   - Hit Rate: ${HIT_RATE}%"
    fi

    echo ""

    # Check for errors
    ERRORS=$(grep -c "ERROR" ai-proxy-logs.txt 2>/dev/null || echo "0")

    if [ $ERRORS -gt 0 ]; then
        echo "   ⚠️  Errors detected: $ERRORS"
        echo "   Run: grep 'ERROR' ai-proxy-logs.txt"
    else
        echo "   ✅ No errors detected"
    fi

    echo ""
    echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
    echo ""
    echo "📄 Full logs saved to: ai-proxy-logs.txt"
    echo "   View: cat ai-proxy-logs.txt"
    echo ""
else
    echo "⚠️  No logs file found. Make sure you ran the tests."
    echo ""
fi

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ TESTING COMPLETE"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
