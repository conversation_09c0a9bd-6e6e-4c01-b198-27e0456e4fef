module.exports = {
  // No preset - pure Node environment for security tests
  testEnvironment: 'node',

  // Only match security tests
  testMatch: ['**/__tests__/security/**/*.(test|spec).[jt]s?(x)'],

  // Module path aliases (match tsconfig.json)
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },

  // Transform TypeScript files with ts-jest
  preset: 'ts-jest',
  transform: {
    '^.+\\.tsx?$': 'ts-jest',
  },

  // Setup file (minimal, without Expo)
  setupFilesAfterEnv: ['<rootDir>/jest.setup.security.js'],

  // Coverage configuration
  collectCoverageFrom: [
    'lib/sanitize.ts',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],

  coverageThreshold: {
    'lib/sanitize.ts': {
      statements: 95,
      branches: 90,
      functions: 95,
      lines: 95,
    },
  },

  // Clear mocks between tests
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Verbose output
  verbose: true,
};
