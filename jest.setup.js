// Polyfill structuredClone before Expo's Winter runtime installs its own handler.
if (typeof global.structuredClone !== 'function') {
  global.structuredClone = value => JSON.parse(JSON.stringify(value));
}

// Mock Expo Winter runtime (Expo 54 compatibility)
global.__ExpoImportMetaRegistry = {
  register: jest.fn(),
  get: jest.fn(),
};

// Mock Expo modules
jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {},
  },
}));

jest.mock('expo-image-picker', () => ({
  launchImageLibraryAsync: jest.fn(),
  launchCameraAsync: jest.fn(),
  MediaTypeOptions: {
    Images: 'Images',
  },
}));

jest.mock('expo-image-manipulator', () => ({
  manipulateAsync: jest.fn(),
  SaveFormat: {
    JPEG: 'JPEG',
  },
}));

jest.mock('expo-file-system', () => ({
  readAsStringAsync: jest.fn(),
  EncodingType: {
    Base64: 'base64',
  },
}));

jest.mock('expo-router', () => ({
  useRouter: jest.fn(() => ({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  })),
  useLocalSearchParams: jest.fn(() => ({})),
  Link: 'Link',
  Stack: 'Stack',
  Tabs: 'Tabs',
}));

jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  selectionAsync: jest.fn(),
}));

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () =>
  require('@react-native-async-storage/async-storage/jest/async-storage-mock')
);

// Mock React Native modules (commented out - not needed for security tests)
// jest.mock('react-native/Libraries/Animated/NativeAnimatedHelper');

// Silence console warnings in tests (optional - comment out if you want to see warnings)
const originalConsole = { ...console };
global.console = {
  ...console,
  warn: jest.fn((...args) => {
    // Allow specific warnings through
    const message = args[0]?.toString() || '';
    if (message.includes('[SECURITY]') || message.includes('CRITICAL')) {
      originalConsole.warn(...args);
    }
  }),
  error: jest.fn((...args) => {
    // Allow specific errors through
    const message = args[0]?.toString() || '';
    if (message.includes('[SECURITY]') || message.includes('CRITICAL')) {
      originalConsole.error(...args);
    }
  }),
};

// Set environment variables for tests
process.env.EXPO_PUBLIC_SUPABASE_URL = 'https://test.supabase.co';
process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY = 'test-anon-key';

// Global test timeout
jest.setTimeout(10000);
