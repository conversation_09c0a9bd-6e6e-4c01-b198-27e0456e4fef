# Rollback Policy for Security Migrations

## ⚠️ CRITICAL: SQL Injection Fix Rollback

**File**: `DEPRECATED_20251029002932_fix_rate_limit_sql_injection_rollback.sql.bak`

**Status**: ❌ **DEPRECATED - DO NOT USE**

### Why This Rollback Was Deprecated

The rollback migration restores the original `check_rate_limit()` function which contains **CRITICAL security vulnerabilities**:

1. **SQL Injection** - No input validation
2. **IDOR** - Any user can check any other user's rate limits
3. **Privilege Escalation** - Uses `SECURITY DEFINER` without proper authorization checks

### Security Vulnerabilities Restored by Rollback

```sql
-- VULNERABLE FUNCTION (restored by rollback)
CREATE OR REPLACE FUNCTION check_rate_limit(...)
$$ LANGUAGE plpgsql SECURITY DEFINER;
-- ❌ No validation of p_user_id
-- ❌ No auth check (auth.uid() IS NULL)
-- ❌ No authorization check (auth.uid() != p_user_id)
-- ❌ SECURITY DEFINER allows privilege escalation
```

### Attack Vector (Proof of Exploit)

```javascript
// Attacker can bypass rate limits by checking victim's limits
const victimUserId = '00000000-0000-0000-0000-000000000001';
const { data } = await supabase.rpc('check_rate_limit', {
  p_user_id: victimUserId, // Not their own user_id!
  p_daily_limit: 100,
  p_hourly_limit: 20
});
// Returns victim's rate limit status - IDOR vulnerability
```

### Emergency Rollback Procedure (If Absolutely Necessary)

⚠️ **WARNING**: Only use in extreme emergency where the secure function breaks production

```bash
# 1. Apply rollback (ONLY IN EMERGENCY)
psql $DATABASE_URL -f supabase/migrations/DEPRECATED_20251029002932_fix_rate_limit_sql_injection_rollback.sql.bak

# 2. IMMEDIATELY re-apply secure function
psql $DATABASE_URL -f supabase/migrations/20251029002932_fix_rate_limit_sql_injection.sql

# 3. IMMEDIATELY investigate why secure function failed
# 4. Log security event
INSERT INTO security_events (event_type, severity, details)
VALUES ('unauthorized_rollback', 'critical', '{"reason": "emergency", "migration": "rate_limit_sql_injection"}');
```

### Recommended Actions Instead of Rollback

1. **Debug the secure function** - Check logs with `mcp__supabase__get_logs`
2. **Verify inputs** - Ensure p_user_id is valid UUID
3. **Check RLS policies** - Ensure rate_limits table has proper RLS
4. **Test in development** - Use Supabase branch to test fixes

### Audit Trail

- **Created**: 2025-10-29 (PR #24)
- **Deprecated**: 2025-10-29 (Security Reviewer found critical vulnerability)
- **Reviewed By**: Security Reviewer Agent, Code Reviewer Agent
- **Decision**: Rename to .bak, document in ROLLBACK_POLICY.md

---

## General Rollback Policy

### When Rollback is Acceptable

✅ **Schema changes** - Add/remove columns (reversible)
✅ **Index changes** - Create/drop indexes (no data loss)
✅ **View changes** - Recreate views (no security impact)

### When Rollback is PROHIBITED

❌ **Security fixes** - NEVER rollback security patches
❌ **Data migrations** - Risk of data loss
❌ **RLS policies** - NEVER disable Row Level Security
❌ **Authentication** - NEVER weaken auth checks

### Rollback Checklist

Before creating a rollback migration, verify:

- [ ] Does rollback introduce security vulnerabilities?
- [ ] Does rollback cause data loss?
- [ ] Does rollback violate GDPR compliance?
- [ ] Is there a safer alternative to rollback?
- [ ] Is rollback approved by Security Reviewer?

If any answer is YES or UNKNOWN, **DO NOT CREATE ROLLBACK**.

---

**Last Updated**: 2025-10-29
**Policy Owner**: Security Team
