-- Update clients table for multi-tenant support
-- Adds organization_id (nullable for freelancers) and created_by tracking

-- Add new columns
alter table public.clients
  add column if not exists organization_id uuid references public.organizations(id) on delete cascade,
  add column if not exists created_by uuid references auth.users(id) on delete set null;

-- Create indexes for faster queries
create index if not exists clients_organization_idx on public.clients using btree (organization_id);
create index if not exists clients_created_by_idx on public.clients using btree (created_by);

-- Drop existing RLS policies
drop policy if exists "Enable all operations for authenticated users" on public.clients;
drop policy if exists "Enable all operations for anon and authenticated users" on public.clients;

-- Create new RLS policies for multi-tenant

-- Policy: Users can read clients from their organization OR their own freelance clients
create policy "Users can read own organization or freelance clients"
  on public.clients
  for select
  using (
    -- Freelance: user created the client and organization_id is null
    (created_by = auth.uid() and organization_id is null)
    or
    -- Organization: user is member of the organization
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
    ))
  );

-- Policy: Users can create clients (organization_id determined by their membership)
create policy "Users can create clients"
  on public.clients
  for insert
  with check (
    -- Must be authenticated
    auth.uid() is not null
    and
    -- created_by must be the current user
    created_by = auth.uid()
    and
    (
      -- Freelance: organization_id must be null
      (organization_id is null)
      or
      -- Organization: user must be a member with manageClients permission
      (organization_id is not null and exists (
        select 1 from public.organization_members
        where organization_members.organization_id = clients.organization_id
        and organization_members.user_id = auth.uid()
        and organization_members.permissions->>'manageClients' = 'true'
      ))
    )
  );

-- Policy: Users can update clients from their organization or their own freelance clients
create policy "Users can update own organization or freelance clients"
  on public.clients
  for update
  using (
    -- Freelance: user created the client
    (created_by = auth.uid() and organization_id is null)
    or
    -- Organization: user is member with manageClients permission
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.permissions->>'manageClients' = 'true'
    ))
  )
  with check (
    -- Same conditions for update
    (created_by = auth.uid() and organization_id is null)
    or
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.permissions->>'manageClients' = 'true'
    ))
  );

-- Policy: Users can delete clients from their organization or their own freelance clients
create policy "Users can delete own organization or freelance clients"
  on public.clients
  for delete
  using (
    -- Freelance: user created the client
    (created_by = auth.uid() and organization_id is null)
    or
    -- Organization: user is owner or admin
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.role in ('owner', 'admin')
    ))
  );

-- Add comments for documentation
comment on column public.clients.organization_id is 'Organization ID (null for freelance stylists, set for salon clients)';
comment on column public.clients.created_by is 'User ID of the person who created this client record';
