-- Update conversations table for multi-tenant support
-- Adds user_id (owner of conversation) and organization_id (optional context)

-- Add new columns
alter table public.conversations
  add column if not exists user_id uuid references auth.users(id) on delete cascade,
  add column if not exists organization_id uuid references public.organizations(id) on delete set null;

-- Create indexes for faster queries
create index if not exists conversations_user_idx on public.conversations using btree (user_id);
create index if not exists conversations_organization_idx on public.conversations using btree (organization_id);

-- Drop existing RLS policies if any
drop policy if exists "Enable all operations for authenticated users" on public.conversations;
drop policy if exists "Enable all operations for anon and authenticated users" on public.conversations;

-- Create new RLS policies for multi-tenant

-- Policy: Users can read their own conversations
create policy "Users can read own conversations"
  on public.conversations
  for select
  using (user_id = auth.uid());

-- Policy: Users can create conversations (must set user_id to themselves)
create policy "Users can create own conversations"
  on public.conversations
  for insert
  with check (
    auth.uid() is not null
    and user_id = auth.uid()
  );

-- Policy: Users can update their own conversations
create policy "Users can update own conversations"
  on public.conversations
  for update
  using (user_id = auth.uid())
  with check (user_id = auth.uid());

-- Policy: Users can delete their own conversations
create policy "Users can delete own conversations"
  on public.conversations
  for delete
  using (user_id = auth.uid());

-- Add comments for documentation
comment on column public.conversations.user_id is 'User ID of the conversation owner';
comment on column public.conversations.organization_id is 'Optional organization ID for context (if conversation related to organization work)';
