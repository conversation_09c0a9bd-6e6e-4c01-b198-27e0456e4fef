-- Función para limpiar fotos antiguas con retención diferenciada
-- Consultations (chat): 14 días (consultas temporales)
-- Formulas (workflow): 90 días (seguimiento formal con consentimiento)
-- Cumplimiento GDPR: Minimización de datos (Art. 5.1.c), Limitación de almacenamiento (Art. 5.1.e)

CREATE OR REPLACE FUNCTION cleanup_old_hair_photos()
RETURNS TABLE(consultation_deleted INT, formula_deleted INT) AS $$
DECLARE
  consultation_count INT := 0;
  formula_count INT := 0;
  old_consultations TEXT[];
  old_formulas TEXT[];
BEGIN
  -- 1. Eliminar consultas del chat > 14 días (análisis temporal)
  SELECT ARRAY_AGG(name) INTO old_consultations
  FROM storage.objects
  WHERE bucket_id = 'hair-photos'
  AND name LIKE '%/consultations/%'
  AND created_at < now() - INTERVAL '14 days';

  IF old_consultations IS NOT NULL AND array_length(old_consultations, 1) > 0 THEN
    FOR i IN 1..array_length(old_consultations, 1) LOOP
      DELETE FROM storage.objects
      WHERE bucket_id = 'hair-photos'
      AND name = old_consultations[i];
    END LOOP;
    consultation_count := array_length(old_consultations, 1);
  END IF;

  -- 2. Eliminar fórmulas del workflow > 90 días (documentación formal)
  SELECT ARRAY_AGG(name) INTO old_formulas
  FROM storage.objects
  WHERE bucket_id = 'hair-photos'
  AND name LIKE '%/formulas/%'
  AND created_at < now() - INTERVAL '90 days';

  IF old_formulas IS NOT NULL AND array_length(old_formulas, 1) > 0 THEN
    FOR i IN 1..array_length(old_formulas, 1) LOOP
      DELETE FROM storage.objects
      WHERE bucket_id = 'hair-photos'
      AND name = old_formulas[i];
    END LOOP;
    formula_count := array_length(old_formulas, 1);
  END IF;

  RAISE NOTICE 'Eliminadas % consultas (>14 días), % fórmulas (>90 días)', consultation_count, formula_count;
  RETURN QUERY SELECT consultation_count, formula_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Nota: Para programar ejecución automática, se requiere pg_cron extension.
-- Si no está disponible en tu plan de Supabase, considera:
--
-- Opción 1: Ejecutar manualmente desde SQL Editor
-- SELECT cleanup_old_hair_photos();
--
-- Opción 2: Crear Edge Function + GitHub Actions para ejecutar diariamente
-- Ver: https://supabase.com/docs/guides/functions/schedule-functions
--
-- Opción 3 (si tienes pg_cron): Descomentar las siguientes líneas
-- SELECT cron.schedule(
--   'cleanup-old-hair-photos',
--   '0 2 * * *', -- Ejecutar diariamente a las 2 AM
--   'SELECT cleanup_old_hair_photos();'
-- );
