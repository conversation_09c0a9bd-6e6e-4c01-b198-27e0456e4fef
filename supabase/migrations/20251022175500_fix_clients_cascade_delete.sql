-- Fix clients.created_by foreign key to CASCADE instead of SET NULL
-- This ensures that when a user is deleted, all their clients are deleted too
-- preventing orphaned client records

ALTER TABLE public.clients
  DROP CONSTRAINT IF EXISTS clients_created_by_fkey;

ALTER TABLE public.clients
  ADD CONSTRAINT clients_created_by_fkey
    FOREIGN KEY (created_by)
    REFERENCES auth.users(id)
    ON DELETE CASCADE;

-- Add comment for documentation
COMMENT ON CONSTRAINT clients_created_by_fkey ON public.clients IS
  'Cascade delete clients when their creator (user) is deleted to prevent orphaned records';
