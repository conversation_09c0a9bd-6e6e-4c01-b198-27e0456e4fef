-- Migration: Fix update_conversations_updated_at trigger function
-- Date: 2025-10-22
-- Issue: Previous migration incorrectly modified the function to UPDATE conversations table
--        instead of simply updating the NEW record's updated_at field
-- Error: record "new" has no field "conversation_id"
-- Root cause: update_conversations_updated_at() is triggered on conversations table updates,
--             but was trying to access NEW.conversation_id which doesn't exist in conversations

-- Restore the original behavior: just update the NEW record's updated_at timestamp
CREATE OR REPLACE FUNCTION update_conversations_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

COMMENT ON FUNCTION update_conversations_updated_at() IS
'Automatically updates the updated_at column when a conversation is modified. Secured with explicit search_path.';
