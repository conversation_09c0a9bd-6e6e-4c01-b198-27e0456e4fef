-- Migration: Add user_brand_preferences table
-- Description: Store brand and line preferences per user
-- Author: <PERSON> Code
-- Date: 2025-10-28

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create user_brand_preferences table
CREATE TABLE IF NOT EXISTS user_brand_preferences (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
  brand_preferences JSONB NOT NULL DEFAULT '{"brands": [], "updatedAt": null}'::jsonb,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),

  -- Ensure one row per user
  UNIQUE(user_id)
);

-- Create index for faster lookups by user_id
CREATE INDEX IF NOT EXISTS idx_user_brand_preferences_user_id
  ON user_brand_preferences(user_id);

-- Create index for JSONB queries (optional, for analytics)
CREATE INDEX IF NOT EXISTS idx_user_brand_preferences_brands
  ON user_brand_preferences USING GIN (brand_preferences);

-- Enable Row Level Security
ALTER TABLE user_brand_preferences ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can view their own preferences
CREATE POLICY "Users can view own brand preferences"
  ON user_brand_preferences FOR SELECT
  USING (auth.uid() = user_id);

-- RLS Policy: Users can insert their own preferences
CREATE POLICY "Users can insert own brand preferences"
  ON user_brand_preferences FOR INSERT
  WITH CHECK (auth.uid() = user_id);

-- RLS Policy: Users can update their own preferences
CREATE POLICY "Users can update own brand preferences"
  ON user_brand_preferences FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- RLS Policy: Users can delete their own preferences
CREATE POLICY "Users can delete own brand preferences"
  ON user_brand_preferences FOR DELETE
  USING (auth.uid() = user_id);

-- Trigger to auto-update updated_at column
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply trigger to user_brand_preferences
CREATE TRIGGER update_user_brand_preferences_updated_at
  BEFORE UPDATE ON user_brand_preferences
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- Comments for documentation
COMMENT ON TABLE user_brand_preferences IS 'Stores user brand and line preferences for formula creation';
COMMENT ON COLUMN user_brand_preferences.brand_preferences IS 'JSONB structure: {"brands": [{"brandId": string, "preferredLines": string[], "isPrimary": boolean}], "updatedAt": ISO8601}';
COMMENT ON COLUMN user_brand_preferences.user_id IS 'Foreign key to auth.users, CASCADE delete when user is deleted';
