-- Migration: Fix function search_path security vulnerability
-- Date: 2025-10-22
-- Issue: Functions without explicit search_path are vulnerable to schema injection attacks
-- References: https://supabase.com/docs/guides/database/database-linter?lint=0011_function_search_path_mutable

-- This migration adds explicit search_path to all functions to prevent security vulnerabilities

-- 1. Fix handle_updated_at function
CREATE OR REPLACE FUNCTION handle_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$;

COMMENT ON FUNCTION handle_updated_at() IS
'Automatically updates the updated_at column when a row is modified. Secured with explicit search_path.';

-- 2. Fix update_conversations_updated_at function
CREATE OR REPLACE FUNCTION update_conversations_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
  UPDATE conversations
  SET updated_at = NOW()
  WHERE id = NEW.conversation_id;
  RETURN NEW;
END;
$$;

COMMENT ON FUNCTION update_conversations_updated_at() IS
'Automatically updates conversation updated_at when a message is added. Secured with explicit search_path.';

-- 3. Fix check_rate_limit function
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_daily_limit INTEGER DEFAULT 100,
  p_hourly_limit INTEGER DEFAULT 20
)
RETURNS BOOLEAN
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
DECLARE
  v_requests_today INTEGER;
  v_requests_this_hour INTEGER;
  v_now TIMESTAMPTZ := NOW();
BEGIN
  -- Insert or get current rate limit record
  INSERT INTO rate_limits (user_id, requests_today, requests_this_hour, last_request_at)
  VALUES (p_user_id, 0, 0, v_now)
  ON CONFLICT (user_id) DO NOTHING;

  -- Get current counts
  SELECT requests_today, requests_this_hour
  INTO v_requests_today, v_requests_this_hour
  FROM rate_limits
  WHERE user_id = p_user_id;

  -- Reset counters if needed
  IF (SELECT daily_reset_at FROM rate_limits WHERE user_id = p_user_id) < v_now THEN
    UPDATE rate_limits
    SET requests_today = 0,
        daily_reset_at = (CURRENT_DATE + INTERVAL '1 day')
    WHERE user_id = p_user_id;
    v_requests_today := 0;
  END IF;

  IF (SELECT hourly_reset_at FROM rate_limits WHERE user_id = p_user_id) < v_now THEN
    UPDATE rate_limits
    SET requests_this_hour = 0,
        hourly_reset_at = (DATE_TRUNC('hour', v_now) + INTERVAL '1 hour')
    WHERE user_id = p_user_id;
    v_requests_this_hour := 0;
  END IF;

  -- Check limits
  IF v_requests_today >= p_daily_limit OR v_requests_this_hour >= p_hourly_limit THEN
    RETURN FALSE;
  END IF;

  -- Increment counters
  UPDATE rate_limits
  SET requests_today = requests_today + 1,
      requests_this_hour = requests_this_hour + 1,
      last_request_at = v_now,
      updated_at = v_now
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$;

COMMENT ON FUNCTION check_rate_limit(UUID, INTEGER, INTEGER) IS
'Checks and updates rate limits for AI usage. Returns TRUE if request is allowed. Secured with explicit search_path.';

-- 4. Fix cleanup_product_cache function
CREATE OR REPLACE FUNCTION cleanup_product_cache()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
BEGIN
  -- Delete cache entries older than 30 days
  DELETE FROM product_cache
  WHERE updated_at < NOW() - INTERVAL '30 days';

  -- Delete least accessed entries if cache is too large (keep max 1000 entries)
  DELETE FROM product_cache
  WHERE id IN (
    SELECT id
    FROM product_cache
    ORDER BY access_count ASC, last_accessed_at ASC
    OFFSET 1000
  );
END;
$$;

COMMENT ON FUNCTION cleanup_product_cache() IS
'Cleans up old and least-used product cache entries. Secured with explicit search_path.';

-- Verification query (optional - for manual checking)
-- SELECT
--   routine_name,
--   routine_type,
--   proconfig
-- FROM information_schema.routines
-- LEFT JOIN pg_proc ON proname = routine_name
-- WHERE routine_schema = 'public'
--   AND routine_name IN ('handle_updated_at', 'update_conversations_updated_at', 'check_rate_limit', 'cleanup_product_cache');
