-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- AI CACHE TABLE - 3-LEVEL CACHE SYSTEM
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- Location: supabase/migrations/20251028_create_ai_cache_table.sql
-- Description: Multi-level cache for product catalog, mixing ratios, and formulas
-- Security: Service role only (RLS enabled)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 1. CREATE TABLE
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE TABLE IF NOT EXISTS ai_cache (
  -- Primary key (composite: cache_type + cache_key)
  cache_type TEXT NOT NULL CHECK (cache_type IN ('product_catalog', 'mixing_ratios', 'formulas')),
  cache_key TEXT NOT NULL, -- Unique identifier (e.g., "brand:wella_professionals")

  -- Cache data
  cache_value JSONB NOT NULL, -- Cached content (products, ratios, formula)

  -- Metadata
  brand TEXT, -- Brand ID (for filtering, optional)
  product_line TEXT, -- Product line (optional)
  expires_at TIMESTAMPTZ NOT NULL, -- TTL expiration (90d/180d/30d)
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),

  -- Analytics
  hit_count INTEGER NOT NULL DEFAULT 0, -- C<PERSON> hit counter
  last_accessed_at TIMESTAMPTZ DEFAULT NOW(),

  -- Constraints
  PRIMARY KEY (cache_type, cache_key)
);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 2. CREATE INDEXES
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

-- Index for expiration cleanup (used by cleanup function)
CREATE INDEX idx_ai_cache_expires_at ON ai_cache(expires_at);

-- Index for brand-based queries (for product catalog)
CREATE INDEX idx_ai_cache_brand ON ai_cache(brand) WHERE brand IS NOT NULL;

-- Index for cache type filtering
CREATE INDEX idx_ai_cache_type ON ai_cache(cache_type);

-- Index for hit analytics (find hot cache entries)
CREATE INDEX idx_ai_cache_hit_count ON ai_cache(hit_count DESC);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 3. ENABLE ROW LEVEL SECURITY (RLS)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

ALTER TABLE ai_cache ENABLE ROW LEVEL SECURITY;

-- Policy: Service role only (Edge functions with service_role key)
CREATE POLICY ai_cache_service_role_policy ON ai_cache
  FOR ALL
  USING (auth.role() = 'service_role');

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 4. AUTO-UPDATE TIMESTAMP TRIGGER
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE OR REPLACE FUNCTION update_ai_cache_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER ai_cache_updated_at_trigger
BEFORE UPDATE ON ai_cache
FOR EACH ROW
EXECUTE FUNCTION update_ai_cache_updated_at();

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 5. CLEANUP FUNCTION (Remove expired cache entries)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

CREATE OR REPLACE FUNCTION cleanup_expired_ai_cache()
RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  DELETE FROM ai_cache
  WHERE expires_at < NOW();

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  RAISE NOTICE 'Deleted % expired cache entries', deleted_count;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- 6. SCHEDULED CLEANUP (Cron job - runs daily at 2 AM UTC)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- NOTE: Requires pg_cron extension (enabled in Supabase by default)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

SELECT cron.schedule(
  'cleanup-expired-ai-cache',
  '0 2 * * *', -- Every day at 2 AM UTC
  $$SELECT cleanup_expired_ai_cache();$$
);

-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- MIGRATION COMPLETE
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
-- Table: ai_cache
-- Indexes: 4 (expires_at, brand, cache_type, hit_count)
-- RLS: Enabled (service_role only)
-- Triggers: 1 (auto-update updated_at)
-- Functions: 1 (cleanup_expired_ai_cache)
-- Cron Jobs: 1 (daily cleanup at 2 AM UTC)
-- ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
