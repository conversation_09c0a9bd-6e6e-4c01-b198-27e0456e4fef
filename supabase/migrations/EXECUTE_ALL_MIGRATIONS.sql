-- ==========================================
-- EJECUTAR TODAS LAS MIGRACIONES EN ORDEN
-- ==========================================
-- INSTRUCCIONES:
-- 1. Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
-- 2. Copiar TODO el contenido de este archivo
-- 3. Pegar en el SQL Editor
-- 4. Click en "RUN" (o Ctrl/Cmd + Enter)
-- ==========================================

-- ==========================================
-- MIGRACIÓN 1: Storage Bucket para imágenes temporales
-- ==========================================

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'hair-images-temp',
  'hair-images-temp',
  false, -- PRIVADO
  10485760, -- 10MB max
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Policy: solo usuarios autenticados pueden subir SUS propias imágenes
CREATE POLICY "Users can upload own images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: usuarios pueden leer sus propias imágenes
CREATE POLICY "Users can read own images"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: usuarios pueden eliminar sus propias imágenes
CREATE POLICY "Users can delete own images"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- ==========================================
-- MIGRACIÓN 2: Tabla de logging de uso de IA
-- ==========================================

CREATE TABLE ai_usage_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL CHECK (provider IN ('openai', 'perplexity')),
  model TEXT NOT NULL,
  use_case TEXT NOT NULL CHECK (use_case IN ('vision_analysis', 'formula_generation', 'product_search', 'chat')),
  prompt_tokens INT DEFAULT 0,
  completion_tokens INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  cost_usd DECIMAL(10, 6) DEFAULT 0,
  image_count INT DEFAULT 0,
  search_queries TEXT[],
  citations JSONB,
  latency_ms INT,
  error TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes para performance
CREATE INDEX idx_ai_usage_user_date ON ai_usage_log(user_id, created_at DESC);
CREATE INDEX idx_ai_usage_cost ON ai_usage_log(created_at DESC, cost_usd);
CREATE INDEX idx_ai_usage_provider ON ai_usage_log(provider, use_case);

-- RLS: usuarios solo ven su propio uso
ALTER TABLE ai_usage_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users view own AI usage"
ON ai_usage_log FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Policy para que Edge Function pueda insertar (usa service role key)
CREATE POLICY "Service role can insert usage logs"
ON ai_usage_log FOR INSERT
TO service_role
WITH CHECK (true);

-- ==========================================
-- MIGRACIÓN 3: Rate Limiting
-- ==========================================

CREATE TABLE rate_limits (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  requests_today INT DEFAULT 0,
  requests_this_hour INT DEFAULT 0,
  last_request_at TIMESTAMPTZ DEFAULT now(),
  daily_reset_at TIMESTAMPTZ DEFAULT (CURRENT_DATE + INTERVAL '1 day'),
  hourly_reset_at TIMESTAMPTZ DEFAULT (date_trunc('hour', now()) + INTERVAL '1 hour'),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE INDEX idx_rate_limits_resets ON rate_limits(daily_reset_at, hourly_reset_at);

-- RLS
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users view own rate limits"
ON rate_limits FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Function para verificar y actualizar límites
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_daily_limit INT DEFAULT 100,
  p_hourly_limit INT DEFAULT 20
) RETURNS BOOLEAN AS $$
DECLARE
  v_limits RECORD;
BEGIN
  -- Crear registro si no existe
  INSERT INTO rate_limits (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Obtener con lock para evitar race conditions
  SELECT * INTO v_limits FROM rate_limits
  WHERE user_id = p_user_id
  FOR UPDATE;

  -- Reset si pasó el periodo diario
  IF v_limits.daily_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_today = 0,
      daily_reset_at = CURRENT_DATE + INTERVAL '1 day',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_today := 0;
  END IF;

  -- Reset si pasó el periodo horario
  IF v_limits.hourly_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_this_hour = 0,
      hourly_reset_at = date_trunc('hour', now()) + INTERVAL '1 hour',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_this_hour := 0;
  END IF;

  -- Verificar límites
  IF v_limits.requests_today >= p_daily_limit THEN
    RETURN FALSE;
  END IF;

  IF v_limits.requests_this_hour >= p_hourly_limit THEN
    RETURN FALSE;
  END IF;

  -- Incrementar contadores
  UPDATE rate_limits SET
    requests_today = requests_today + 1,
    requests_this_hour = requests_this_hour + 1,
    last_request_at = now(),
    updated_at = now()
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==========================================
-- MIGRACIÓN 4: Cache de productos
-- ==========================================

CREATE TABLE product_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  brand TEXT NOT NULL,
  product_line TEXT,
  query_text TEXT NOT NULL,
  response_data JSONB NOT NULL,
  citations JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  access_count INT DEFAULT 0,
  last_accessed_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes
CREATE INDEX idx_product_cache_lookup ON product_cache(brand, product_line, query_text);
CREATE INDEX idx_product_cache_updated ON product_cache(updated_at DESC);
CREATE INDEX idx_product_cache_access ON product_cache(last_accessed_at DESC);

-- RLS: cache es accesible a todos los usuarios autenticados (datos públicos de productos)
ALTER TABLE product_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read cache"
ON product_cache FOR SELECT
TO authenticated
USING (true);

-- Function para limpiar cache viejo (ejecutar periódicamente)
CREATE OR REPLACE FUNCTION cleanup_product_cache(days_old INT DEFAULT 7)
RETURNS INT AS $$
DECLARE
  deleted_count INT;
BEGIN
  DELETE FROM product_cache
  WHERE updated_at < now() - (days_old || ' days')::INTERVAL;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ==========================================
-- ✅ MIGRACIONES COMPLETADAS
-- ==========================================
-- Si no hubo errores, las siguientes tablas/buckets deben existir:
-- 1. storage.buckets → 'hair-images-temp'
-- 2. ai_usage_log
-- 3. rate_limits (con función check_rate_limit)
-- 4. product_cache (con función cleanup_product_cache)
-- ==========================================
