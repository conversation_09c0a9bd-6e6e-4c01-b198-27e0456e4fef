-- Rollback for Security Fix: SQL Injection in check_rate_limit function
-- <PERSON>ores original SECURITY DEFINER function (use only if v2 has issues)

-- WARNING: This rollback restores a VULNERABLE function
-- Only use in emergency situations, plan to re-apply fix ASAP

-- Drop secure function
DROP FUNCTION IF EXISTS check_rate_limit(UUID, INT, INT);

-- Restore original function (VULNERABLE - has SECURITY DEFINER without validation)
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_daily_limit INT DEFAULT 100,
  p_hourly_limit INT DEFAULT 20
) RETURNS BOOLEAN AS $$
DECLARE
  v_limits RECORD;
BEGIN
  -- Crear registro si no existe
  INSERT INTO rate_limits (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Obtener con lock para evitar race conditions
  SELECT * INTO v_limits FROM rate_limits
  WHERE user_id = p_user_id
  FOR UPDATE;

  -- Reset si pasó el periodo diario
  IF v_limits.daily_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_today = 0,
      daily_reset_at = CURRENT_DATE + INTERVAL '1 day',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_today := 0;
  END IF;

  -- Reset si pasó el periodo horario
  IF v_limits.hourly_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_this_hour = 0,
      hourly_reset_at = date_trunc('hour', now()) + INTERVAL '1 hour',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_this_hour := 0;
  END IF;

  -- Verificar límites
  IF v_limits.requests_today >= p_daily_limit THEN
    RETURN FALSE;
  END IF;

  IF v_limits.requests_this_hour >= p_hourly_limit THEN
    RETURN FALSE;
  END IF;

  -- Incrementar contadores
  UPDATE rate_limits SET
    requests_today = requests_today + 1,
    requests_this_hour = requests_this_hour + 1,
    last_request_at = now(),
    updated_at = now()
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Log rollback event
DO $$
BEGIN
  RAISE WARNING 'SECURITY ROLLBACK: check_rate_limit function restored to VULNERABLE version. Re-apply fix ASAP.';
END $$;
