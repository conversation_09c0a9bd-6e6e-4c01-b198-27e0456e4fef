-- ============================================
-- MIGRATION: Fix intent_cache RLS policies (v2)
-- Date: 2025-10-28
-- Purpose: Block ALL access except service_role
-- ============================================

-- Drop all existing policies
DROP POLICY IF EXISTS intent_cache_service_role ON intent_cache;
DROP POLICY IF EXISTS intent_cache_select ON intent_cache;
DROP POLICY IF EXISTS intent_cache_insert ON intent_cache;
DROP POLICY IF EXISTS intent_cache_update ON intent_cache;
DROP POLICY IF EXISTS intent_cache_delete ON intent_cache;

-- Create strict policies that explicitly check for service_role
-- Using current_setting which is more reliable than auth.jwt()

-- SELECT: Only service_role
CREATE POLICY intent_cache_select_v2 ON intent_cache
  FOR SELECT
  USING (
    current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
  );

-- INSERT: Only service_role
CREATE POLICY intent_cache_insert_v2 ON intent_cache
  FOR INSERT
  WITH CHECK (
    current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
  );

-- UPDATE: Only service_role
CREATE POLICY intent_cache_update_v2 ON intent_cache
  FOR UPDATE
  USING (
    current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
  );

-- DELETE: Only service_role
CREATE POLICY intent_cache_delete_v2 ON intent_cache
  FOR DELETE
  USING (
    current_setting('request.jwt.claims', true)::json->>'role' = 'service_role'
  );

COMMENT ON TABLE intent_cache IS
  'Intent detection cache - RESTRICTED to service_role only (edge functions)';
