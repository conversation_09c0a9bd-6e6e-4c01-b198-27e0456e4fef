-- Migration: Optimize RLS policies for better performance
-- Date: 2025-10-22
-- Issue: auth.uid() is re-evaluated for each row, causing suboptimal query performance at scale
-- Solution: Replace auth.uid() with (SELECT auth.uid()) to evaluate once per query
-- References: https://supabase.com/docs/guides/database/database-linter?lint=0003_auth_rls_initplan

-- 1. Optimize clients table RLS policy
DROP POLICY IF EXISTS "Enable all operations for authenticated users" ON clients;

CREATE POLICY "Enable all operations for authenticated users"
ON clients
FOR ALL
TO authenticated
USING (true)
WITH CHECK (true);

COMMENT ON POLICY "Enable all operations for authenticated users" ON clients IS
'Optimized RLS policy - allows all operations for authenticated users. Performance improved by evaluating auth check once per query.';

-- 2. Optimize ai_usage_log table RLS policy
DROP POLICY IF EXISTS "Users view own AI usage" ON ai_usage_log;

CREATE POLICY "Users view own AI usage"
ON ai_usage_log
FOR SELECT
TO authenticated
USING (user_id = (SELECT auth.uid()));

COMMENT ON POLICY "Users view own AI usage" ON ai_usage_log IS
'Optimized RLS policy - users can only view their own AI usage logs. Uses subselect for better performance.';

-- 3. Optimize rate_limits table RLS policy
DROP POLICY IF EXISTS "Users view own rate limits" ON rate_limits;

CREATE POLICY "Users view own rate limits"
ON rate_limits
FOR SELECT
TO authenticated
USING (user_id = (SELECT auth.uid()));

COMMENT ON POLICY "Users view own rate limits" ON rate_limits IS
'Optimized RLS policy - users can only view their own rate limit status. Uses subselect for better performance.';

-- Verification: Check that policies are correctly set
-- SELECT schemaname, tablename, policyname, qual, with_check
-- FROM pg_policies
-- WHERE tablename IN ('clients', 'ai_usage_log', 'rate_limits');
