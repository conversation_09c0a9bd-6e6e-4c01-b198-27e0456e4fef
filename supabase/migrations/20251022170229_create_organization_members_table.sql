-- Create organization_members table (many-to-many relationship)
-- Links users to organizations with roles and permissions

create table if not exists public.organization_members (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid not null references public.organizations(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  role text not null check (role in ('owner', 'admin', 'stylist', 'receptionist')),
  permissions jsonb not null default '{
    "manageClients": true,
    "createFormulas": true,
    "viewReports": false,
    "manageTeam": false,
    "manageSettings": false
  }'::jsonb,
  is_active boolean not null default true,
  joined_date timestamp with time zone not null default now(),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  unique(organization_id, user_id)
);

-- Create indexes for faster queries
create index if not exists organization_members_org_idx on public.organization_members using btree (organization_id);
create index if not exists organization_members_user_idx on public.organization_members using btree (user_id);
create index if not exists organization_members_role_idx on public.organization_members using btree (role);

-- Enable Row Level Security (RLS)
alter table public.organization_members enable row level security;

-- Create policy: Users can read members of their organizations
create policy "Users can read members of own organizations"
  on public.organization_members
  for select
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
    )
  );

-- Create policy: Owners and admins can invite/add members
create policy "Owners and admins can add members"
  on public.organization_members
  for insert
  with check (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organization_members.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.role in ('owner', 'admin')
    )
  );

-- Create policy: Owners and admins can update members (except changing owner role)
create policy "Owners and admins can update members"
  on public.organization_members
  for update
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role in ('owner', 'admin')
    )
  )
  with check (
    -- Prevent changing owner role unless you are an owner
    (organization_members.role != 'owner' or exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role = 'owner'
    ))
  );

-- Create policy: Owners can remove members
create policy "Owners can remove members"
  on public.organization_members
  for delete
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role = 'owner'
    )
  );

-- Create trigger to automatically update updated_at timestamp
create trigger set_organization_members_updated_at
  before update on public.organization_members
  for each row
  execute function public.handle_updated_at();

-- Function to set default permissions based on role
create or replace function public.set_default_permissions()
returns trigger as $$
begin
  case new.role
    when 'owner' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": true,
        "manageTeam": true,
        "manageSettings": true
      }'::jsonb;
    when 'admin' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": true,
        "manageTeam": true,
        "manageSettings": false
      }'::jsonb;
    when 'stylist' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": false,
        "manageTeam": false,
        "manageSettings": false
      }'::jsonb;
    when 'receptionist' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": false,
        "viewReports": false,
        "manageTeam": false,
        "manageSettings": false
      }'::jsonb;
  end case;
  return new;
end;
$$ language plpgsql;

-- Trigger to set default permissions on insert
create trigger set_member_default_permissions
  before insert on public.organization_members
  for each row
  execute function public.set_default_permissions();

-- Function to update user's is_freelance status when they join an organization
create or replace function public.update_freelance_status()
returns trigger as $$
begin
  update public.profiles
  set is_freelance = false
  where id = new.user_id;
  return new;
end;
$$ language plpgsql;

-- Trigger to update freelance status
create trigger update_user_freelance_status
  after insert on public.organization_members
  for each row
  execute function public.update_freelance_status();

-- Add comments for documentation
comment on table public.organization_members is 'Links users to organizations with roles and permissions';
comment on column public.organization_members.role is 'User role: owner, admin, stylist, or receptionist';
comment on column public.organization_members.permissions is 'JSON object with boolean flags for various permissions';
comment on column public.organization_members.is_active is 'Whether the member is currently active in the organization';
