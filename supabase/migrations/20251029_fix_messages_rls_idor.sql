-- Fix CRITICAL IDOR vulnerability in messages table
-- Security issue: Anyone can read ANY message from ANY conversation
-- CVSS Score: 8.1 HIGH
-- Reported by: Security-Reviewer + Supabase-Specialist agents

-- DROP the vulnerable policy that allows ALL operations
DROP POLICY IF EXISTS "Enable all operations for messages" ON messages;

-- Create proper RLS policies with user isolation

-- Policy: Users can only read messages in their own conversations
CREATE POLICY "Users can read messages in own conversations"
  ON messages FOR SELECT
  USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  );

-- Policy: Users can only insert messages in their own conversations
CREATE POLICY "Users can insert messages in own conversations"
  ON messages FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  );

-- Policy: Users can only update messages in their own conversations
CREATE POLICY "Users can update messages in own conversations"
  ON messages FOR UPDATE
  USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  )
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  );

-- Policy: Users can only delete messages in their own conversations
CREATE POLICY "Users can delete messages in own conversations"
  ON messages FOR DELETE
  USING (
    EXISTS (
      SELECT 1 FROM conversations
      WHERE conversations.id = messages.conversation_id
      AND conversations.user_id = auth.uid()
    )
  );

-- Add index to optimize the EXISTS subquery performance
CREATE INDEX IF NOT EXISTS idx_conversations_id_user_id
  ON conversations(id, user_id);

-- Add index for messages conversation lookup
CREATE INDEX IF NOT EXISTS idx_messages_conversation_id
  ON messages(conversation_id);

-- Security comment
COMMENT ON POLICY "Users can read messages in own conversations" ON messages IS
  'SECURITY FIX (2025-10-29): Prevent IDOR vulnerability by verifying conversation ownership via user_id check';

COMMENT ON POLICY "Users can insert messages in own conversations" ON messages IS
  'SECURITY FIX (2025-10-29): Prevent unauthorized message insertion';

COMMENT ON POLICY "Users can update messages in own conversations" ON messages IS
  'SECURITY FIX (2025-10-29): Prevent unauthorized message modification';

COMMENT ON POLICY "Users can delete messages in own conversations" ON messages IS
  'SECURITY FIX (2025-10-29): Prevent unauthorized message deletion';

-- Rollback SQL (for reference - run manually if needed):
-- DROP POLICY IF EXISTS "Users can read messages in own conversations" ON messages;
-- DROP POLICY IF EXISTS "Users can insert messages in own conversations" ON messages;
-- DROP POLICY IF EXISTS "Users can update messages in own conversations" ON messages;
-- DROP POLICY IF EXISTS "Users can delete messages in own conversations" ON messages;
-- CREATE POLICY "Enable all operations for messages" ON messages FOR ALL USING (true) WITH CHECK (true);
