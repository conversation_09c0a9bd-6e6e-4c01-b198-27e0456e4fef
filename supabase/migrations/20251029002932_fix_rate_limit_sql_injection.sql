-- Security Fix: <PERSON>QL Injection in check_rate_limit function
-- Adds parameter validation and removes SECURITY DEFINER vulnerability

-- Drop old function
DROP FUNCTION IF EXISTS check_rate_limit(UUID, INT, INT);

-- Recreate with proper security and validation
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_daily_limit INT DEFAULT 100,
  p_hourly_limit INT DEFAULT 20
) RETURNS BOOLEAN AS $$
DECLARE
  v_limits RECORD;
BEGIN
  -- SECURITY: Validate input parameters
  IF p_user_id IS NULL THEN
    RAISE EXCEPTION 'user_id cannot be null';
  END IF;

  IF p_daily_limit < 1 OR p_daily_limit > 10000 THEN
    RAISE EXCEPTION 'daily_limit must be between 1 and 10000';
  END IF;

  IF p_hourly_limit < 1 OR p_hourly_limit > 1000 THEN
    RAISE EXCEPTION 'hourly_limit must be between 1 and 1000';
  END IF;

  -- SECURITY: Verify caller is authenticated
  IF auth.uid() IS NULL THEN
    RAISE EXCEPTION 'Authentication required';
  END IF;

  -- SECURITY: Verify caller can only check their own rate limit
  IF auth.uid() != p_user_id THEN
    RAISE EXCEPTION 'Unauthorized: Can only check own rate limit';
  END IF;

  -- Crear registro si no existe (using parameterized query via SELECT)
  INSERT INTO rate_limits (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Obtener con lock para evitar race conditions
  -- SECURITY: user_id is UUID type, no string interpolation possible
  SELECT * INTO v_limits FROM rate_limits
  WHERE user_id = p_user_id
  FOR UPDATE;

  -- Reset si pasó el periodo diario
  IF v_limits.daily_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_today = 0,
      daily_reset_at = CURRENT_DATE + INTERVAL '1 day',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_today := 0;
  END IF;

  -- Reset si pasó el periodo horario
  IF v_limits.hourly_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_this_hour = 0,
      hourly_reset_at = date_trunc('hour', now()) + INTERVAL '1 hour',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_this_hour := 0;
  END IF;

  -- Verificar límites
  IF v_limits.requests_today >= p_daily_limit THEN
    RETURN FALSE;
  END IF;

  IF v_limits.requests_this_hour >= p_hourly_limit THEN
    RETURN FALSE;
  END IF;

  -- Incrementar contadores
  UPDATE rate_limits SET
    requests_today = requests_today + 1,
    requests_this_hour = requests_this_hour + 1,
    last_request_at = now(),
    updated_at = now()
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql
SECURITY INVOKER  -- Changed from SECURITY DEFINER
SET search_path = public, pg_temp;  -- Prevent search_path attacks

-- Grant execute only to authenticated users
REVOKE ALL ON FUNCTION check_rate_limit(UUID, INT, INT) FROM PUBLIC;
GRANT EXECUTE ON FUNCTION check_rate_limit(UUID, INT, INT) TO authenticated;

-- Add comment for documentation
COMMENT ON FUNCTION check_rate_limit IS 'Rate limiting function with SQL injection protection. Users can only check their own rate limits. Uses SECURITY INVOKER to prevent privilege escalation.';
