-- Create clients table
create table if not exists public.clients (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  email text,
  phone text,
  last_visit timestamp with time zone,
  photo text,
  notes text,
  known_allergies text,
  is_pregnant_or_breastfeeding boolean not null default false,
  has_sensitive_scalp boolean not null default false,
  chemical_treatments jsonb not null default '{"henna": false, "chemicalStraightening": false, "keratin": false}'::jsonb,
  communication_preferences jsonb not null default '{"acceptsReminders": true, "preferredMethod": "whatsapp"}'::jsonb,
  additional_notes text,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now()
);

-- Create index on name for faster searches
create index if not exists clients_name_idx on public.clients using btree (name);

-- Create index on email for lookups
create index if not exists clients_email_idx on public.clients using btree (email);

-- Enable Row Level Security (RLS)
alter table public.clients enable row level security;

-- Create policy to allow all operations for authenticated users
-- Note: Adjust these policies based on your authentication setup
create policy "Enable all operations for authenticated users"
  on public.clients
  for all
  using (auth.role() = 'authenticated' or auth.role() = 'anon')
  with check (auth.role() = 'authenticated' or auth.role() = 'anon');

-- Create function to automatically update updated_at timestamp
create or replace function public.handle_updated_at()
returns trigger as $$
begin
  new.updated_at = now();
  return new;
end;
$$ language plpgsql;

-- Create trigger to call the function on update
create trigger set_updated_at
  before update on public.clients
  for each row
  execute function public.handle_updated_at();

-- Add comments for documentation
comment on table public.clients is 'Stores client information for the salon';
comment on column public.clients.id is 'Unique identifier for the client';
comment on column public.clients.name is 'Full name of the client';
comment on column public.clients.email is 'Email address of the client';
comment on column public.clients.phone is 'Phone number of the client';
comment on column public.clients.last_visit is 'Timestamp of the last visit';
comment on column public.clients.chemical_treatments is 'JSON object containing boolean flags for henna, chemicalStraightening, and keratin treatments';
comment on column public.clients.communication_preferences is 'JSON object containing acceptsReminders (boolean) and preferredMethod (whatsapp/sms)';
