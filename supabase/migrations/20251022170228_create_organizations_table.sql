-- Create organizations table (salons/businesses)
-- Migrates BusinessProfile data structure to database

create table if not exists public.organizations (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  logo text,
  street text,
  city text,
  postal_code text,
  state text,
  country text,
  phone text,
  email text,
  website text,
  instagram text,
  business_hours jsonb default '{
    "monday": {"open": "09:00", "close": "18:00", "closed": false},
    "tuesday": {"open": "09:00", "close": "18:00", "closed": false},
    "wednesday": {"open": "09:00", "close": "18:00", "closed": false},
    "thursday": {"open": "09:00", "close": "18:00", "closed": false},
    "friday": {"open": "09:00", "close": "18:00", "closed": false},
    "saturday": {"open": "10:00", "close": "16:00", "closed": false},
    "sunday": {"open": "10:00", "close": "16:00", "closed": true}
  }'::jsonb,
  plan text not null default 'free' check (plan in ('free', 'premium', 'enterprise')),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now()
);

-- Create index on name for searches
create index if not exists organizations_name_idx on public.organizations using btree (name);

-- Enable Row Level Security (RLS)
alter table public.organizations enable row level security;

-- Create policy: Users can read organizations they are members of
create policy "Users can read own organizations"
  on public.organizations
  for select
  using (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
    )
  );

-- Create policy: Only organization owners can update
create policy "Owners can update organization"
  on public.organizations
  for update
  using (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
      and organization_members.role = 'owner'
    )
  )
  with check (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
      and organization_members.role = 'owner'
    )
  );

-- Create policy: Authenticated users can create organizations
create policy "Authenticated users can create organizations"
  on public.organizations
  for insert
  with check (auth.role() = 'authenticated');

-- Create trigger to automatically update updated_at timestamp
create trigger set_organizations_updated_at
  before update on public.organizations
  for each row
  execute function public.handle_updated_at();

-- Add comments for documentation
comment on table public.organizations is 'Salons and businesses (premium tier)';
comment on column public.organizations.id is 'Unique identifier for the organization';
comment on column public.organizations.name is 'Business name';
comment on column public.organizations.business_hours is 'JSON object with opening hours for each day of the week';
comment on column public.organizations.plan is 'Subscription plan: free, premium, or enterprise';
