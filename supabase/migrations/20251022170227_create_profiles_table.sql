-- Create profiles table (1:1 with auth.users)
-- Stores user profile information for both freelancers and salon members

create table if not exists public.profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  name text not null,
  email text not null,
  photo text,
  phone text,
  specialty text,
  license_number text,
  bio text,
  is_freelance boolean not null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now()
);

-- Create index on email for faster lookups
create index if not exists profiles_email_idx on public.profiles using btree (email);

-- Enable Row Level Security (RLS)
alter table public.profiles enable row level security;

-- Create policy: Users can read their own profile and profiles of members in their organizations
create policy "Users can read own profile and org members profiles"
  on public.profiles
  for select
  using (
    auth.uid() = id
    or
    exists (
      select 1 from public.organization_members om1
      join public.organization_members om2 on om1.organization_id = om2.organization_id
      where om1.user_id = auth.uid()
      and om2.user_id = profiles.id
    )
  );

-- Create policy: Users can update their own profile
create policy "Users can update own profile"
  on public.profiles
  for update
  using (auth.uid() = id)
  with check (auth.uid() = id);

-- Create trigger to automatically update updated_at timestamp
create trigger set_profiles_updated_at
  before update on public.profiles
  for each row
  execute function public.handle_updated_at();

-- Function to automatically create profile on user signup
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, name, email)
  values (
    new.id,
    coalesce(new.raw_user_meta_data->>'name', new.email),
    new.email
  );
  return new;
end;
$$ language plpgsql security definer;

-- Trigger to create profile automatically when user signs up
create trigger on_auth_user_created
  after insert on auth.users
  for each row
  execute function public.handle_new_user();

-- Add comments for documentation
comment on table public.profiles is 'User profiles for both freelance stylists and salon members';
comment on column public.profiles.id is 'User ID (references auth.users)';
comment on column public.profiles.name is 'Full name of the user';
comment on column public.profiles.email is 'Email address';
comment on column public.profiles.is_freelance is 'True if user is a freelance stylist without organization, false if belongs to a salon';
comment on column public.profiles.specialty is 'Professional specialty (e.g., Colorista, Estilista)';
comment on column public.profiles.license_number is 'Professional license number';
