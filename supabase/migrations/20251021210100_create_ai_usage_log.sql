-- Migración 2: Tabla de logging de uso de IA
-- Tabla de auditoría de uso de IA

CREATE TABLE ai_usage_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL CHECK (provider IN ('openai', 'perplexity')),
  model TEXT NOT NULL,
  use_case TEXT NOT NULL CHECK (use_case IN ('vision_analysis', 'formula_generation', 'product_search', 'chat')),
  prompt_tokens INT DEFAULT 0,
  completion_tokens INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  cost_usd DECIMAL(10, 6) DEFAULT 0,
  image_count INT DEFAULT 0,
  search_queries TEXT[],
  citations JSONB,
  latency_ms INT,
  error TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes para performance
CREATE INDEX idx_ai_usage_user_date ON ai_usage_log(user_id, created_at DESC);
CREATE INDEX idx_ai_usage_cost ON ai_usage_log(created_at DESC, cost_usd);
CREATE INDEX idx_ai_usage_provider ON ai_usage_log(provider, use_case);

-- RLS: usuarios solo ven su propio uso
ALTER TABLE ai_usage_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users view own AI usage"
ON ai_usage_log FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Policy para que Edge Function pueda insertar (usa service role key)
CREATE POLICY "Service role can insert usage logs"
ON ai_usage_log FOR INSERT
TO service_role
WITH CHECK (true);
