-- Create analytics_events table for tracking user behavior
-- Used to measure impact of multi-turn chat implementation

CREATE TABLE IF NOT EXISTS analytics_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_name TEXT NOT NULL,
  properties JSONB,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for efficient queries
CREATE INDEX IF NOT EXISTS idx_analytics_events_name ON analytics_events(event_name);
CREATE INDEX IF NOT EXISTS idx_analytics_events_timestamp ON analytics_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_analytics_events_user_id ON analytics_events(user_id);

-- Enable Row Level Security
ALTER TABLE analytics_events ENABLE ROW LEVEL SECURITY;

-- RLS Policy: Users can only read their own events
CREATE POLICY "Users can read own analytics events"
  ON analytics_events
  FOR SELECT
  USING (user_id = auth.uid());

-- RLS Policy: Users can insert their own events
CREATE POLICY "Users can insert own analytics events"
  ON analytics_events
  FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL
    AND user_id = auth.uid()
  );

-- Add comments for documentation
COMMENT ON TABLE analytics_events IS 'Tracks user behavior and feature usage for analytics and optimization';
COMMENT ON COLUMN analytics_events.event_name IS 'Name of the event (e.g., chat_message_sent, conversation_started)';
COMMENT ON COLUMN analytics_events.properties IS 'Additional event metadata stored as JSONB';
COMMENT ON COLUMN analytics_events.user_id IS 'User who triggered the event';
COMMENT ON COLUMN analytics_events.timestamp IS 'When the event occurred';
