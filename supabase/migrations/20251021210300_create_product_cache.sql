-- Migración 4: Cache de productos
-- Tabla de cache para búsquedas de Perplexity (reduce costos)

CREATE TABLE product_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  brand TEXT NOT NULL,
  product_line TEXT,
  query_text TEXT NOT NULL,
  response_data JSONB NOT NULL,
  citations JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  access_count INT DEFAULT 0,
  last_accessed_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes
CREATE INDEX idx_product_cache_lookup ON product_cache(brand, product_line, query_text);
CREATE INDEX idx_product_cache_updated ON product_cache(updated_at DESC);
CREATE INDEX idx_product_cache_access ON product_cache(last_accessed_at DESC);

-- RLS: cache es accesible a todos los usuarios autenticados (datos públicos de productos)
ALTER TABLE product_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read cache"
ON product_cache FOR SELECT
TO authenticated
USING (true);

-- Function para limpiar cache viejo (ejecutar periódicamente)
CREATE OR REPLACE FUNCTION cleanup_product_cache(days_old INT DEFAULT 7)
RETURNS INT AS $$
DECLARE
  deleted_count INT;
BEGIN
  DELETE FROM product_cache
  WHERE updated_at < now() - (days_old || ' days')::INTERVAL;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
