-- ============================================
-- MIGRATION: Fix intent_cache RLS policies
-- Date: 2025-10-28
-- Purpose: Block anon access, allow only service_role
-- ============================================

-- Drop existing policy
DROP POLICY IF EXISTS intent_cache_service_role ON intent_cache;

-- Create restrictive policies for each operation
-- Only service_role can SELECT
CREATE POLICY intent_cache_select ON intent_cache
  FOR SELECT
  USING (auth.jwt() ->> 'role' = 'service_role');

-- Only service_role can INSERT
CREATE POLICY intent_cache_insert ON intent_cache
  FOR INSERT
  WITH CHECK (auth.jwt() ->> 'role' = 'service_role');

-- Only service_role can UPDATE
CREATE POLICY intent_cache_update ON intent_cache
  FOR UPDATE
  USING (auth.jwt() ->> 'role' = 'service_role');

-- Only service_role can DELETE
CREATE POLICY intent_cache_delete ON intent_cache
  FOR DELETE
  USING (auth.jwt() ->> 'role' = 'service_role');

COMMENT ON POLICY intent_cache_select ON intent_cache IS
  'Cache access restricted to service_role (edge functions) only';
