-- ============================================
-- MIGRATION: Fix intent_cache RLS policies (v3 - FINAL)
-- Date: 2025-10-28
-- Purpose: Block anon users, allow service_role only
-- ============================================

-- Drop all existing policies
DROP POLICY IF EXISTS intent_cache_service_role ON intent_cache;
DROP POLICY IF EXISTS intent_cache_select ON intent_cache;
DROP POLICY IF EXISTS intent_cache_insert ON intent_cache;
DROP POLICY IF EXISTS intent_cache_update ON intent_cache;
DROP POLICY IF EXISTS intent_cache_delete ON intent_cache;
DROP POLICY IF EXISTS intent_cache_select_v2 ON intent_cache;
DROP POLICY IF EXISTS intent_cache_insert_v2 ON intent_cache;
DROP POLICY IF EXISTS intent_cache_update_v2 ON intent_cache;
DROP POLICY IF EXISTS intent_cache_delete_v2 ON intent_cache;

-- Create NO policies for anon or authenticated
-- Service role will bypass RLS automatically
-- This effectively blocks all access except service_role

-- Optionally, create explicit denial policies for anon
-- (though not having any policies for anon role achieves the same effect)

-- Ensure table is locked down: no policies = no access for non-service roles
-- Service role bypasses RLS by default

COMMENT ON TABLE intent_cache IS
  'Intent detection cache - NO RLS policies = service_role access only';
