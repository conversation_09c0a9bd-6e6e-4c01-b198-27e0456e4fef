-- ============================================
-- MIGRATION: Create intent_cache table
-- Date: 2025-10-28
-- Purpose: Cache for GPT-4o-mini intent detection results (30-day TTL)
-- ============================================

-- Intent detection cache table
CREATE TABLE IF NOT EXISTS intent_cache (
  query_hash TEXT PRIMARY KEY, -- SHA-256 hash of normalized query
  intent_result JSONB NOT NULL, -- IntentDetectionResult JSON
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  access_count INTEGER NOT NULL DEFAULT 0,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for performance
CREATE INDEX IF NOT EXISTS idx_intent_cache_created_at
  ON intent_cache(created_at DESC);

CREATE INDEX IF NOT EXISTS idx_intent_cache_access_count
  ON intent_cache(access_count DESC);

-- Enable RLS
ALTER TABLE intent_cache ENABLE ROW LEVEL SECURITY;

-- Policy: Allow service role to manage cache (edge function only)
CREATE POLICY intent_cache_service_role ON intent_cache
  FOR ALL
  USING (auth.role() = 'service_role');

COMMENT ON TABLE intent_cache IS
  'Cache for GPT-4o-mini intent detection results (30-day TTL)';

COMMENT ON COLUMN intent_cache.query_hash IS
  'SHA-256 hash of normalized query (lowercase, trimmed)';

COMMENT ON COLUMN intent_cache.intent_result IS
  'JSON result from GPT-4o-mini: {requires_current_info, entities, confidence, reason}';

COMMENT ON COLUMN intent_cache.created_at IS
  'Cache entry creation timestamp (used for TTL expiry)';

COMMENT ON COLUMN intent_cache.access_count IS
  'Number of times this cache entry has been accessed (analytics)';
