-- Security Events Table
-- Logs security-relevant events for monitoring and incident response

-- Configuration Constants (documented in comments)
-- SECURITY_EVENT_RETENTION_DAYS: 90 days
-- MAX_SECURITY_EVENTS_PER_USER_PER_HOUR: 100 (rate limiting)

CREATE TABLE IF NOT EXISTS security_events (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  event_type TEXT NOT NULL CHECK (event_type IN (
    'auth_failed',
    'rate_limit_exceeded',
    'idor_attempt',
    'prompt_injection_detected',
    'xss_attempt',
    'sql_injection_attempt',
    'cors_violation',
    'invalid_api_key',
    'unauthorized_access'
  )),
  severity TEXT NOT NULL CHECK (severity IN ('low', 'medium', 'high', 'critical')),
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL, -- NULL for unauthenticated attempts
  ip_address TEXT,
  user_agent TEXT,
  request_path TEXT,
  request_method TEXT,
  request_body JSONB,
  details JSONB, -- Additional event-specific data
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Indexes for efficient querying
CREATE INDEX idx_security_events_type ON security_events(event_type);
CREATE INDEX idx_security_events_severity ON security_events(severity);
CREATE INDEX idx_security_events_user_id ON security_events(user_id);
CREATE INDEX idx_security_events_created_at ON security_events(created_at DESC);
CREATE INDEX idx_security_events_ip_address ON security_events(ip_address);

-- RLS: Only service role can read/write security events
ALTER TABLE security_events ENABLE ROW LEVEL SECURITY;

CREATE POLICY security_events_service_role_policy ON security_events
  FOR ALL
  USING (auth.role() = 'service_role');

-- Function to log security events (called from edge functions)
CREATE OR REPLACE FUNCTION log_security_event(
  p_event_type TEXT,
  p_severity TEXT,
  p_user_id UUID DEFAULT NULL,
  p_ip_address TEXT DEFAULT NULL,
  p_user_agent TEXT DEFAULT NULL,
  p_request_path TEXT DEFAULT NULL,
  p_request_method TEXT DEFAULT NULL,
  p_request_body JSONB DEFAULT NULL,
  p_details JSONB DEFAULT NULL
) RETURNS UUID AS $$
DECLARE
  v_event_id UUID;
BEGIN
  -- Validate event_type
  IF p_event_type NOT IN (
    'auth_failed',
    'rate_limit_exceeded',
    'idor_attempt',
    'prompt_injection_detected',
    'xss_attempt',
    'sql_injection_attempt',
    'cors_violation',
    'invalid_api_key',
    'unauthorized_access'
  ) THEN
    RAISE EXCEPTION 'Invalid event_type: %', p_event_type;
  END IF;

  -- Validate severity
  IF p_severity NOT IN ('low', 'medium', 'high', 'critical') THEN
    RAISE EXCEPTION 'Invalid severity: %', p_severity;
  END IF;

  -- Insert event
  INSERT INTO security_events (
    event_type,
    severity,
    user_id,
    ip_address,
    user_agent,
    request_path,
    request_method,
    request_body,
    details
  ) VALUES (
    p_event_type,
    p_severity,
    p_user_id,
    p_ip_address,
    p_user_agent,
    p_request_path,
    p_request_method,
    p_request_body,
    p_details
  ) RETURNING id INTO v_event_id;

  RETURN v_event_id;
END;
$$ LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public, pg_temp;

-- Grant execute to service_role only
REVOKE ALL ON FUNCTION log_security_event FROM PUBLIC;
GRANT EXECUTE ON FUNCTION log_security_event TO service_role;

-- Cleanup function for old security events (run via cron)
CREATE OR REPLACE FUNCTION cleanup_old_security_events() RETURNS INTEGER AS $$
DECLARE
  deleted_count INTEGER;
BEGIN
  -- Delete events older than 90 days
  DELETE FROM security_events
  WHERE created_at < NOW() - INTERVAL '90 days';

  GET DIAGNOSTICS deleted_count = ROW_COUNT;

  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql
SECURITY INVOKER
SET search_path = public, pg_temp;

-- Comments for documentation
COMMENT ON TABLE security_events IS 'Security event log for monitoring and incident response. Retention: 90 days.';
COMMENT ON FUNCTION log_security_event IS 'Log security-related events. Only callable by service_role.';
COMMENT ON FUNCTION cleanup_old_security_events IS 'Delete security events older than 90 days. Run via cron daily.';
