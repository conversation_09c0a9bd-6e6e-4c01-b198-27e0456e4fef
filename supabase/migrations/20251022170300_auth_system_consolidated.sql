-- Consolidated migration for authentication system
-- Creates all tables first, then adds RLS policies

-- ============================================================================
-- STEP 1: Create tables (no policies yet)
-- ============================================================================

-- 1.1 Create organizations table
create table if not exists public.organizations (
  id uuid primary key default gen_random_uuid(),
  name text not null,
  logo text,
  street text,
  city text,
  postal_code text,
  state text,
  country text,
  phone text,
  email text,
  website text,
  instagram text,
  business_hours jsonb default '{
    "monday": {"open": "09:00", "close": "18:00", "closed": false},
    "tuesday": {"open": "09:00", "close": "18:00", "closed": false},
    "wednesday": {"open": "09:00", "close": "18:00", "closed": false},
    "thursday": {"open": "09:00", "close": "18:00", "closed": false},
    "friday": {"open": "09:00", "close": "18:00", "closed": false},
    "saturday": {"open": "10:00", "close": "16:00", "closed": false},
    "sunday": {"open": "10:00", "close": "16:00", "closed": true}
  }'::jsonb,
  plan text not null default 'free' check (plan in ('free', 'premium', 'enterprise')),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now()
);

create index if not exists organizations_name_idx on public.organizations using btree (name);

-- 1.2 Create profiles table
create table if not exists public.profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  name text not null,
  email text not null,
  photo text,
  phone text,
  specialty text,
  license_number text,
  bio text,
  is_freelance boolean not null default true,
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now()
);

create index if not exists profiles_email_idx on public.profiles using btree (email);

-- 1.3 Create organization_members table
create table if not exists public.organization_members (
  id uuid primary key default gen_random_uuid(),
  organization_id uuid not null references public.organizations(id) on delete cascade,
  user_id uuid not null references auth.users(id) on delete cascade,
  role text not null check (role in ('owner', 'admin', 'stylist', 'receptionist')),
  permissions jsonb not null default '{
    "manageClients": true,
    "createFormulas": true,
    "viewReports": false,
    "manageTeam": false,
    "manageSettings": false
  }'::jsonb,
  is_active boolean not null default true,
  joined_date timestamp with time zone not null default now(),
  created_at timestamp with time zone not null default now(),
  updated_at timestamp with time zone not null default now(),
  unique(organization_id, user_id)
);

create index if not exists organization_members_org_idx on public.organization_members using btree (organization_id);
create index if not exists organization_members_user_idx on public.organization_members using btree (user_id);
create index if not exists organization_members_role_idx on public.organization_members using btree (role);

-- 1.4 Update clients table
alter table public.clients
  add column if not exists organization_id uuid references public.organizations(id) on delete cascade,
  add column if not exists created_by uuid references auth.users(id) on delete set null;

create index if not exists clients_organization_idx on public.clients using btree (organization_id);
create index if not exists clients_created_by_idx on public.clients using btree (created_by);

-- 1.5 Update conversations table
alter table public.conversations
  add column if not exists user_id uuid references auth.users(id) on delete cascade,
  add column if not exists organization_id uuid references public.organizations(id) on delete set null;

create index if not exists conversations_user_idx on public.conversations using btree (user_id);
create index if not exists conversations_organization_idx on public.conversations using btree (organization_id);

-- ============================================================================
-- STEP 2: Enable RLS on all tables
-- ============================================================================

alter table public.profiles enable row level security;
alter table public.organizations enable row level security;
alter table public.organization_members enable row level security;

-- ============================================================================
-- STEP 3: Drop existing policies
-- ============================================================================

drop policy if exists "Enable all operations for authenticated users" on public.clients;
drop policy if exists "Enable all operations for anon and authenticated users" on public.clients;
drop policy if exists "Enable all operations for authenticated users" on public.conversations;
drop policy if exists "Enable all operations for anon and authenticated users" on public.conversations;

-- ============================================================================
-- STEP 4: Create RLS policies
-- ============================================================================

-- 4.1 Profiles policies
create policy "Users can read own profile and org members profiles"
  on public.profiles
  for select
  using (
    auth.uid() = id
    or
    exists (
      select 1 from public.organization_members om1
      join public.organization_members om2 on om1.organization_id = om2.organization_id
      where om1.user_id = auth.uid()
      and om2.user_id = profiles.id
    )
  );

create policy "Users can update own profile"
  on public.profiles
  for update
  using (auth.uid() = id)
  with check (auth.uid() = id);

-- 4.2 Organizations policies
create policy "Users can read own organizations"
  on public.organizations
  for select
  using (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
    )
  );

create policy "Owners can update organization"
  on public.organizations
  for update
  using (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
      and organization_members.role = 'owner'
    )
  )
  with check (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organizations.id
      and organization_members.user_id = auth.uid()
      and organization_members.role = 'owner'
    )
  );

create policy "Authenticated users can create organizations"
  on public.organizations
  for insert
  with check (auth.role() = 'authenticated');

-- 4.3 Organization members policies
create policy "Users can read members of own organizations"
  on public.organization_members
  for select
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
    )
  );

create policy "Owners and admins can add members"
  on public.organization_members
  for insert
  with check (
    exists (
      select 1 from public.organization_members
      where organization_members.organization_id = organization_members.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.role in ('owner', 'admin')
    )
  );

create policy "Owners and admins can update members"
  on public.organization_members
  for update
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role in ('owner', 'admin')
    )
  )
  with check (
    (organization_members.role != 'owner' or exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role = 'owner'
    ))
  );

create policy "Owners can remove members"
  on public.organization_members
  for delete
  using (
    exists (
      select 1 from public.organization_members om
      where om.organization_id = organization_members.organization_id
      and om.user_id = auth.uid()
      and om.role = 'owner'
    )
  );

-- 4.4 Clients policies (multi-tenant)
create policy "Users can read own organization or freelance clients"
  on public.clients
  for select
  using (
    (created_by = auth.uid() and organization_id is null)
    or
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
    ))
  );

create policy "Users can create clients"
  on public.clients
  for insert
  with check (
    auth.uid() is not null
    and
    created_by = auth.uid()
    and
    (
      (organization_id is null)
      or
      (organization_id is not null and exists (
        select 1 from public.organization_members
        where organization_members.organization_id = clients.organization_id
        and organization_members.user_id = auth.uid()
        and organization_members.permissions->>'manageClients' = 'true'
      ))
    )
  );

create policy "Users can update own organization or freelance clients"
  on public.clients
  for update
  using (
    (created_by = auth.uid() and organization_id is null)
    or
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.permissions->>'manageClients' = 'true'
    ))
  )
  with check (
    (created_by = auth.uid() and organization_id is null)
    or
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.permissions->>'manageClients' = 'true'
    ))
  );

create policy "Users can delete own organization or freelance clients"
  on public.clients
  for delete
  using (
    (created_by = auth.uid() and organization_id is null)
    or
    (organization_id is not null and exists (
      select 1 from public.organization_members
      where organization_members.organization_id = clients.organization_id
      and organization_members.user_id = auth.uid()
      and organization_members.role in ('owner', 'admin')
    ))
  );

-- 4.5 Conversations policies
create policy "Users can read own conversations"
  on public.conversations
  for select
  using (user_id = auth.uid());

create policy "Users can create own conversations"
  on public.conversations
  for insert
  with check (
    auth.uid() is not null
    and user_id = auth.uid()
  );

create policy "Users can update own conversations"
  on public.conversations
  for update
  using (user_id = auth.uid())
  with check (user_id = auth.uid());

create policy "Users can delete own conversations"
  on public.conversations
  for delete
  using (user_id = auth.uid());

-- ============================================================================
-- STEP 5: Create triggers
-- ============================================================================

create trigger set_profiles_updated_at
  before update on public.profiles
  for each row
  execute function public.handle_updated_at();

create trigger set_organizations_updated_at
  before update on public.organizations
  for each row
  execute function public.handle_updated_at();

create trigger set_organization_members_updated_at
  before update on public.organization_members
  for each row
  execute function public.handle_updated_at();

-- ============================================================================
-- STEP 6: Create functions
-- ============================================================================

-- Function to automatically create profile on user signup
create or replace function public.handle_new_user()
returns trigger as $$
begin
  insert into public.profiles (id, name, email)
  values (
    new.id,
    coalesce(new.raw_user_meta_data->>'name', new.email),
    new.email
  );
  return new;
end;
$$ language plpgsql security definer;

-- Trigger to create profile automatically when user signs up
drop trigger if exists on_auth_user_created on auth.users;
create trigger on_auth_user_created
  after insert on auth.users
  for each row
  execute function public.handle_new_user();

-- Function to set default permissions based on role
create or replace function public.set_default_permissions()
returns trigger as $$
begin
  case new.role
    when 'owner' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": true,
        "manageTeam": true,
        "manageSettings": true
      }'::jsonb;
    when 'admin' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": true,
        "manageTeam": true,
        "manageSettings": false
      }'::jsonb;
    when 'stylist' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": true,
        "viewReports": false,
        "manageTeam": false,
        "manageSettings": false
      }'::jsonb;
    when 'receptionist' then
      new.permissions = '{
        "manageClients": true,
        "createFormulas": false,
        "viewReports": false,
        "manageTeam": false,
        "manageSettings": false
      }'::jsonb;
  end case;
  return new;
end;
$$ language plpgsql;

-- Trigger to set default permissions on insert
create trigger set_member_default_permissions
  before insert on public.organization_members
  for each row
  execute function public.set_default_permissions();

-- Function to update user's is_freelance status when they join an organization
create or replace function public.update_freelance_status()
returns trigger as $$
begin
  update public.profiles
  set is_freelance = false
  where id = new.user_id;
  return new;
end;
$$ language plpgsql;

-- Trigger to update freelance status
create trigger update_user_freelance_status
  after insert on public.organization_members
  for each row
  execute function public.update_freelance_status();

-- ============================================================================
-- STEP 7: Add comments
-- ============================================================================

comment on table public.profiles is 'User profiles for both freelance stylists and salon members';
comment on table public.organizations is 'Salons and businesses (premium tier)';
comment on table public.organization_members is 'Links users to organizations with roles and permissions';
comment on column public.clients.organization_id is 'Organization ID (null for freelance stylists, set for salon clients)';
comment on column public.clients.created_by is 'User ID of the person who created this client record';
comment on column public.conversations.user_id is 'User ID of the conversation owner';
comment on column public.conversations.organization_id is 'Optional organization ID for context';