-- Fix RLS infinite recursion by creating helper function and updating policies
-- Problem: Policies that check organization_members were creating circular references
-- Solution: Use SECURITY DEFINER function to break the recursion chain

-- Create helper function to check organization membership (breaks RLS recursion)
CREATE OR REPLACE FUNCTION public.user_is_org_member(org_id uuid, user_id uuid)
RETURNS boolean AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.organization_members
    WHERE organization_id = org_id
    AND organization_members.user_id = user_is_org_member.user_id
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER STABLE;

-- Grant execute to authenticated users
GRANT EXECUTE ON FUNCTION public.user_is_org_member(uuid, uuid) TO authenticated;

COMMENT ON FUNCTION public.user_is_org_member(uuid, uuid) IS
  'Helper function with SECURITY DEFINER to check organization membership without RLS recursion';

-- Drop old clients policies
DROP POLICY IF EXISTS "Users can read own organization or freelance clients" ON public.clients;
DROP POLICY IF EXISTS "Users can create clients" ON public.clients;
DROP POLICY IF EXISTS "Users can update own organization or freelance clients" ON public.clients;
DROP POLICY IF EXISTS "Users can delete own organization or freelance clients" ON public.clients;

-- Recreate clients policies using helper function (no recursion)
CREATE POLICY "Users can read own organization or freelance clients"
  ON public.clients
  FOR SELECT
  USING (
    (created_by = auth.uid() AND organization_id IS NULL)
    OR
    (organization_id IS NOT NULL AND public.user_is_org_member(organization_id, auth.uid()))
  );

CREATE POLICY "Users can create clients"
  ON public.clients
  FOR INSERT
  WITH CHECK (
    auth.uid() IS NOT NULL
    AND created_by = auth.uid()
    AND (
      organization_id IS NULL
      OR
      public.user_is_org_member(organization_id, auth.uid())
    )
  );

CREATE POLICY "Users can update own organization or freelance clients"
  ON public.clients
  FOR UPDATE
  USING (
    (created_by = auth.uid() AND organization_id IS NULL)
    OR
    (organization_id IS NOT NULL AND public.user_is_org_member(organization_id, auth.uid()))
  )
  WITH CHECK (
    (created_by = auth.uid() AND organization_id IS NULL)
    OR
    (organization_id IS NOT NULL AND public.user_is_org_member(organization_id, auth.uid()))
  );

CREATE POLICY "Users can delete own organization or freelance clients"
  ON public.clients
  FOR DELETE
  USING (
    (created_by = auth.uid() AND organization_id IS NULL)
    OR
    (organization_id IS NOT NULL AND public.user_is_org_member(organization_id, auth.uid()))
  );

-- Simplify profiles policy to avoid recursion (users can only read their own profile)
DROP POLICY IF EXISTS "Users can read own profile and org members profiles" ON public.profiles;

CREATE POLICY "Users can read own profile"
  ON public.profiles
  FOR SELECT
  USING (auth.uid() = id);

COMMENT ON POLICY "Users can read own profile" ON public.profiles IS
  'Simplified policy to avoid RLS recursion. Organization member profiles will be handled separately when needed.';

-- Fix organization_members INSERT policy (had wrong self-reference)
DROP POLICY IF EXISTS "Owners and admins can add members" ON public.organization_members;

CREATE POLICY "Owners and admins can add members"
  ON public.organization_members
  FOR INSERT
  WITH CHECK (
    EXISTS (
      SELECT 1 FROM public.organization_members om
      WHERE om.organization_id = organization_members.organization_id
      AND om.user_id = auth.uid()
      AND om.role IN ('owner', 'admin')
    )
  );
