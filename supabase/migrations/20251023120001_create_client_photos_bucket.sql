-- Bucket para fotos de cabello (workflow de fórmulas + chat AI)
-- Privado, cifrado, con políticas RLS estrictas
-- Retención: 90 días para GDPR compliance
-- Cumplimiento: AES-256 en reposo, TLS 1.2+ en tránsito

INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'hair-photos',
  'hair-photos',
  false, -- PRIVADO: Solo accesible con autenticación y políticas RLS
  10485760, -- 10MB max por foto
  ARRAY['image/jpeg', 'image/png', 'image/webp']
)
ON CONFLICT (id) DO NOTHING;

-- RLS Policy: Solo el usuario que sube la foto puede insertarla
-- Path structure: userId/clientId/fileName.jpg o userId/fileName.jpg
CREATE POLICY "Users can upload own photos"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'hair-photos'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- RLS Policy: Solo el usuario que subió la foto puede verla
CREATE POLICY "Users can view own photos"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'hair-photos'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- RLS Policy: Solo el usuario que subió la foto puede eliminarla
CREATE POLICY "Users can delete own photos"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'hair-photos'
  AND auth.uid()::text = (storage.foldername(name))[1]
);
