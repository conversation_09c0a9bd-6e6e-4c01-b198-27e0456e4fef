-- Migration: Create formulas and formula_notes tables
-- Purpose: Store formula history per client for continuity and learning

-- =====================================================
-- TABLE: formulas
-- =====================================================
-- Stores complete hair color formulas generated for clients

CREATE TABLE IF NOT EXISTS formulas (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id uuid NOT NULL REFERENCES clients(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Session tracking
  session_number integer NOT NULL DEFAULT 1,
  service_type text NOT NULL CHECK (service_type IN ('color', 'highlights', 'balayage', 'toner', 'correction')),

  -- Color transformation data
  current_level integer CHECK (current_level >= 1 AND current_level <= 10),
  target_level integer CHECK (target_level >= 1 AND target_level <= 10),
  target_tone text,
  target_reflection text,

  -- Brand and technique
  brand text NOT NULL,
  product_line text,
  technique text,

  -- Formula content (AI-generated)
  formula_text text NOT NULL,

  -- Structured product data (JSONB for easy querying)
  products_used jsonb DEFAULT '[]'::jsonb,
  -- Example: [{"name": "Koleston Perfect 7/0", "amount": "30g", "code": "8005610534527"}]

  -- Formula metadata
  total_cost numeric(10,2), -- Cost in user's currency
  estimated_duration_minutes integer,

  -- Analysis snapshots (for reference in future sessions)
  current_color_analysis jsonb,
  desired_color_analysis jsonb,
  safety_checklist jsonb,

  -- Timestamps
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Indexes for performance
CREATE INDEX idx_formulas_client_id ON formulas(client_id);
CREATE INDEX idx_formulas_user_id ON formulas(user_id);
CREATE INDEX idx_formulas_created_at ON formulas(created_at DESC);
CREATE INDEX idx_formulas_brand ON formulas(brand);

-- Updated_at trigger
CREATE TRIGGER update_formulas_updated_at
  BEFORE UPDATE ON formulas
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- TABLE: formula_notes
-- =====================================================
-- Personal notes added by stylists for individual learning

CREATE TABLE IF NOT EXISTS formula_notes (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  formula_id uuid NOT NULL REFERENCES formulas(id) ON DELETE CASCADE,
  user_id uuid NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,

  -- Note content
  note_text text NOT NULL,
  note_type text CHECK (note_type IN ('observation', 'reminder', 'learning', 'adjustment')) DEFAULT 'observation',

  -- Positioning (which section of the formula this note relates to)
  section_reference text, -- e.g., 'application', 'timing', 'products'

  -- Timestamps
  created_at timestamptz NOT NULL DEFAULT now(),
  updated_at timestamptz NOT NULL DEFAULT now()
);

-- Indexes
CREATE INDEX idx_formula_notes_formula_id ON formula_notes(formula_id);
CREATE INDEX idx_formula_notes_user_id ON formula_notes(user_id);

-- Updated_at trigger
CREATE TRIGGER update_formula_notes_updated_at
  BEFORE UPDATE ON formula_notes
  FOR EACH ROW
  EXECUTE FUNCTION update_updated_at_column();

-- =====================================================
-- ROW LEVEL SECURITY (RLS)
-- =====================================================

-- Enable RLS
ALTER TABLE formulas ENABLE ROW LEVEL SECURITY;
ALTER TABLE formula_notes ENABLE ROW LEVEL SECURITY;

-- Formulas RLS Policies
CREATE POLICY "Users can view own formulas"
  ON formulas FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own formulas"
  ON formulas FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own formulas"
  ON formulas FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own formulas"
  ON formulas FOR DELETE
  USING (auth.uid() = user_id);

-- Formula Notes RLS Policies
CREATE POLICY "Users can view own formula notes"
  ON formula_notes FOR SELECT
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own formula notes"
  ON formula_notes FOR INSERT
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update own formula notes"
  ON formula_notes FOR UPDATE
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own formula notes"
  ON formula_notes FOR DELETE
  USING (auth.uid() = user_id);

-- =====================================================
-- COMMENTS
-- =====================================================

COMMENT ON TABLE formulas IS 'Stores complete hair color formulas with product details and analysis snapshots';
COMMENT ON TABLE formula_notes IS 'Personal stylist notes for individual learning and client-specific observations';

COMMENT ON COLUMN formulas.session_number IS 'Sequential session number for multi-session transformations';
COMMENT ON COLUMN formulas.products_used IS 'Structured array of products: [{name, amount, code}]';
COMMENT ON COLUMN formulas.current_color_analysis IS 'Snapshot of hair analysis from step1';
COMMENT ON COLUMN formulas.desired_color_analysis IS 'Snapshot of target color from step2';
COMMENT ON COLUMN formula_notes.section_reference IS 'Which part of the formula this note relates to';
