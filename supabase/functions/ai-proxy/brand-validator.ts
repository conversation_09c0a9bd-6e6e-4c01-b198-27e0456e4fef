/**
 * Brand validation and domain filter mapping
 * Location: supabase/functions/ai-proxy/brand-validator.ts
 *
 * SECURITY: Whitelist-only validation to prevent prompt injection
 */

// Import brands from local data directory (deployed with edge function)
import brandsData from './data/brands.json' assert { type: 'json' };

/**
 * Domain mappings for Perplexity search filtering
 * Only includes official brand domains (curated list)
 */
const DOMAIN_MAPPINGS: Record<string, string[]> = {
  // Tier 1: Marcas Globales Consolidadas
  'loreal_professionnel': ['loreal-paris.com', 'lorealpro.com', 'loreal.fr', 'loreal.es'],
  'wella_professionals': ['wella.com', 'wella.es', 'wella.co.uk', 'wella.de'],
  'schwarzkopf_professional': ['schwarzkopf.com', 'schwarzkopf-professional.com', 'schwarzkopf.de'],
  'redken': ['redken.com', 'redken.es', 'redken.fr'],
  'goldwell': ['goldwell.com', 'goldwell.de', 'goldwell.co.uk'],
  'matrix': ['matrix.com', 'matrixprofessional.com'],
  'joico': ['joico.com'],
  'pravana': ['pravana.com'],
  'paul_mitchell': ['paulmitchell.com'],
  'davines': ['davines.com', 'davines.it'],
  'alfaparf_milano': ['alfaparf.com', 'alfaparfmilano.com'],
  'keune': ['keune.com'],
  'aveda': ['aveda.com'],
  'framesi': ['framesiusa.com', 'framesi.it'],
  'moroccanoil': ['moroccanoil.com'],

  // Tier 2: Marcas Profesionales Especializadas
  'chi': ['farouk.com', 'chibeauty.com'],
  'kenra_professional': ['kenraprofessional.com'],
  'indola': ['indola.com'],
  'kaaral': ['kaaral.it', 'kaaralusa.com'],
  'lakme': ['lakmeprofessional.com', 'lakme.es'],
  'pulp_riot': ['pulpriothair.com'],
  'guy_tang_mydentity': ['mydentity.com', 'guytang.com'],
  'elgon': ['elgoncosmetic.it', 'elgonusa.com'],
  'kevin_murphy': ['kevinmurphy.com.au'],
  'salerm_cosmetics': ['salerm.com', 'salerm.es'],
  'lendan_cosmetics': ['lendan.com', 'lendan.es'],
  'arkhe_cosmetics': ['arkhecosmetics.com'],
  'j_beverly_hills': ['jbeverlyhills.com'],
  'fanola': ['fanola.com', 'fanola.it'],
  'revlon_professional': ['revlonprofessional.com'],

  // Tier 3: Marcas Nicho/Orgánicas
  'oway': ['oway.it'],
  'organic_colour_systems': ['ocs.uk.com'],
  'natulique': ['natulique.com'],
  'inebrya': ['inebryacolor.com', 'inebrya.it'],
  'selective_professional': ['selectiveprofessional.com'],
  'scruples': ['scruples.com'],
  'tressa': ['tressapro.com'],
  'rusk': ['rusk1.com'],
  'agebeautiful': ['agebeautiful.com'],
  'zotos_professional': ['zotos.com'],
  'graham_webb': ['grahamwebb.com'],
};

/**
 * Validate brand and get domain filter for Perplexity search
 *
 * SECURITY: Whitelist-only approach - rejects any brand not in approved list
 *
 * @param brandId Brand ID from brands.json (e.g., "wella_professionals")
 * @returns Array of approved domains for search filtering, or null if invalid
 */
export function validateBrandAndGetDomains(brandId: string): string[] | null {
  // Security: Validate brandId is in brands.json whitelist
  const brand = brandsData.find((b: any) => b.id === brandId);

  if (!brand) {
    console.warn('[Brand Validator] Invalid brand ID:', brandId);
    return null; // Reject request
  }

  // Get pre-defined domain mappings (curated list)
  const domains = DOMAIN_MAPPINGS[brandId];

  if (!domains) {
    console.warn('[Brand Validator] No domain mapping for brand:', brandId);
    return null; // No domains = cannot search safely
  }

  console.log(`[Brand Validator] Approved domains for ${brandId}:`, domains);
  return domains;
}

/**
 * Get brand name from ID (for display purposes)
 *
 * @param brandId Brand ID from brands.json
 * @returns Brand display name or null if invalid
 */
export function getBrandName(brandId: string): string | null {
  const brand = brandsData.find((b: any) => b.id === brandId);
  return brand ? brand.name : null;
}

/**
 * Get all valid brand IDs (for validation purposes)
 *
 * @returns Array of all valid brand IDs
 */
export function getAllValidBrandIds(): string[] {
  return brandsData.map((b: any) => b.id);
}

/**
 * Check if brand ID is valid
 *
 * @param brandId Brand ID to validate
 * @returns true if brand exists in whitelist
 */
export function isValidBrandId(brandId: string): boolean {
  return brandsData.some((b: any) => b.id === brandId);
}
