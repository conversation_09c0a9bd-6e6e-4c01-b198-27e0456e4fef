/**
 * Self-Correction Loop - Auto-corrects formulas with unverified products
 * Location: supabase/functions/ai-proxy/self-correction.ts
 */

import OpenAI from 'https://esm.sh/openai@4.28.0';
import { ProductVerification, VerifiedProduct } from './types.ts';

const MAX_CORRECTION_ATTEMPTS = 2; // Prevent infinite loops

/**
 * Replace products section in formula with corrected version
 */
function replaceProductsSection(
  originalFormula: string,
  correctedSection: string
): string {
  // Find the "🛒 TU LISTA DE COMPRA" section
  const sectionRegex = /🛒\s*TU\s*LISTA\s*DE\s*COMPRA[\s\S]*?(?=━|$)/i;

  if (sectionRegex.test(originalFormula)) {
    return originalFormula.replace(sectionRegex, correctedSection);
  }

  // If section not found, append at the end
  console.warn('[Self-Correction] Products section not found, appending correction');
  return `${originalFormula}\n\n${correctedSection}`;
}

/**
 * Self-correct formula with unverified products
 *
 * @param openai - OpenAI client
 * @param originalFormula - Original formula text
 * @param verifiedProducts - Products with verification status
 * @param brand - Brand name for context
 * @param productLine - Product line for context
 * @returns Corrected formula or original if correction fails
 */
export async function selfCorrectFormula(
  openai: OpenAI,
  originalFormula: string,
  verifiedProducts: VerifiedProduct[],
  brand: string,
  productLine?: string
): Promise<{
  correctedFormula: string;
  correctionsMade: number;
  attemptedCorrections: string[];
}> {
  // Filter unverified products
  const unverifiedProducts = verifiedProducts.filter((p) => !p.verified);

  if (unverifiedProducts.length === 0) {
    console.log('[Self-Correction] No unverified products, skipping correction');
    return {
      correctedFormula: originalFormula,
      correctionsMade: 0,
      attemptedCorrections: [],
    };
  }

  console.log(
    `[Self-Correction] Attempting to correct ${unverifiedProducts.length} unverified products...`
  );

  // Build error list for correction prompt
  const errors = unverifiedProducts.map((p) => ({
    mentioned: p.name,
    code: p.code,
    issue: 'Producto no encontrado en catálogo oficial',
    alternative: p.alternative || 'Consulta con el fabricante',
  }));

  const correctionPrompt = `You are correcting a hair color formula that contains products that don't exist in the official catalog.

BRAND: ${brand}${productLine ? ` - ${productLine}` : ''}

ERRORS FOUND (productos que NO existen):
${JSON.stringify(errors, null, 2)}

TASK:
1. Rewrite ONLY the "🛒 TU LISTA DE COMPRA" section
2. Replace non-existent products with the suggested alternatives
3. Keep the same format and structure
4. DO NOT modify other sections (diagnóstico, sesiones, instrucciones, etc.)
5. Maintain the conversational mentor style

ORIGINAL FORMULA:
${originalFormula}

RESPOND WITH:
Only the corrected "🛒 TU LISTA DE COMPRA" section (including the heading and separator lines).
Keep the exact format with emoji and separators.`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o',
      messages: [{ role: 'user', content: correctionPrompt }],
      temperature: 0.2, // Low temperature for precise correction
      max_completion_tokens: 800,
    });

    const correctedSection = completion.choices[0].message.content || '';

    if (!correctedSection || correctedSection.length < 50) {
      console.warn('[Self-Correction] Correction result too short, using original formula');
      return {
        correctedFormula: originalFormula,
        correctionsMade: 0,
        attemptedCorrections: errors.map((e) => e.mentioned),
      };
    }

    // Replace section in original formula
    const correctedFormula = replaceProductsSection(originalFormula, correctedSection);

    console.log(
      `[Self-Correction] Successfully corrected ${unverifiedProducts.length} products`
    );

    return {
      correctedFormula,
      correctionsMade: unverifiedProducts.length,
      attemptedCorrections: errors.map((e) => e.mentioned),
    };
  } catch (error: any) {
    console.error('[Self-Correction] Correction failed:', error);

    // Return original formula if correction fails (safe fallback)
    return {
      correctedFormula: originalFormula,
      correctionsMade: 0,
      attemptedCorrections: errors.map((e) => e.mentioned),
    };
  }
}

/**
 * Self-correction with retry logic
 * Attempts correction multiple times if products still unverified
 *
 * @param openai - OpenAI client
 * @param originalFormula - Original formula
 * @param verificationResults - Initial verification results
 * @param brand - Brand name
 * @param productLine - Product line
 * @returns Final corrected formula
 */
export async function selfCorrectWithRetry(
  openai: OpenAI,
  originalFormula: string,
  verificationResults: ProductVerification[],
  brand: string,
  productLine?: string
): Promise<{
  finalFormula: string;
  totalCorrections: number;
  attempts: number;
}> {
  let currentFormula = originalFormula;
  let totalCorrections = 0;
  let attempts = 0;

  const verifiedProducts: VerifiedProduct[] = verificationResults.map((r) => ({
    name: r.product.name,
    code: r.product.code,
    verified: r.exists,
    alternative: r.alternative,
    source: r.product.source,
  }));

  // Retry up to MAX_CORRECTION_ATTEMPTS times
  while (attempts < MAX_CORRECTION_ATTEMPTS) {
    const unverifiedCount = verifiedProducts.filter((p) => !p.verified).length;

    if (unverifiedCount === 0) {
      console.log('[Self-Correction] All products verified, no correction needed');
      break;
    }

    attempts++;
    console.log(
      `[Self-Correction] Attempt ${attempts}/${MAX_CORRECTION_ATTEMPTS} - Correcting ${unverifiedCount} products...`
    );

    const result = await selfCorrectFormula(
      openai,
      currentFormula,
      verifiedProducts,
      brand,
      productLine
    );

    if (result.correctionsMade > 0) {
      currentFormula = result.correctedFormula;
      totalCorrections += result.correctionsMade;

      // Mark corrected products as verified (assume alternatives are valid)
      for (const product of verifiedProducts) {
        if (!product.verified && result.attemptedCorrections.includes(product.name)) {
          product.verified = true; // Prevent re-correction
        }
      }
    } else {
      // No corrections made, stop retrying
      console.warn('[Self-Correction] No corrections made, stopping retry loop');
      break;
    }
  }

  console.log(
    `[Self-Correction] Completed with ${totalCorrections} total corrections in ${attempts} attempts`
  );

  return {
    finalFormula: currentFormula,
    totalCorrections,
    attempts,
  };
}
