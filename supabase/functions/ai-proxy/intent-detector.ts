/**
 * Intent detector using GPT-4o-mini
 * Location: supabase/functions/ai-proxy/intent-detector.ts
 */

import OpenAI from 'https://esm.sh/openai@4.28.0';
import { createClient, SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { INTENT_DETECTION_SYSTEM_PROMPT } from './prompts.ts';
import { IntentDetectionResult, IntentCacheEntry } from './types.ts';

const INTENT_CACHE_TTL_DAYS = 30;
const INTENT_DETECTION_TIMEOUT_MS = 3000;

/**
 * Sanitize user input to prevent prompt injection
 */
function sanitizeInput(query: string): string {
  if (!query) return '';

  const maxLength = 500;
  const truncated = query.substring(0, maxLength);

  return truncated
    .replace(/```/g, '') // Remove code blocks
    .replace(/\n\n+/g, '\n') // Collapse multiple newlines
    .trim();
}

/**
 * Generate cache key from query
 */
async function generateQueryHash(query: string): Promise<string> {
  const normalized = query.toLowerCase().trim();

  // Use Web Crypto API (available in Deno)
  const encoder = new TextEncoder();
  const data = encoder.encode(normalized);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');

  return hashHex;
}

/**
 * Check intent detection cache
 */
async function checkIntentCache(
  supabase: SupabaseClient,
  queryHash: string
): Promise<IntentCacheEntry | null> {
  const expiryDate = new Date();
  expiryDate.setDate(expiryDate.getDate() - INTENT_CACHE_TTL_DAYS);

  const { data, error } = await supabase
    .from('intent_cache')
    .select('*')
    .eq('query_hash', queryHash)
    .gte('created_at', expiryDate.toISOString())
    .maybeSingle();

  if (error) {
    console.error('[Intent Cache] Read error:', error);
    return null;
  }

  if (data) {
    // Update access count
    await supabase
      .from('intent_cache')
      .update({ access_count: data.access_count + 1 })
      .eq('query_hash', queryHash);

    console.log(`[Intent Cache HIT] ${queryHash}`);
  }

  return data;
}

/**
 * Save intent detection result to cache
 */
async function saveToIntentCache(
  supabase: SupabaseClient,
  queryHash: string,
  result: IntentDetectionResult
): Promise<void> {
  try {
    await supabase.from('intent_cache').upsert({
      query_hash: queryHash,
      intent_result: result,
      created_at: new Date().toISOString(),
      access_count: 0,
    });
  } catch (error) {
    console.error('[Intent Cache] Write error:', error);
  }
}

/**
 * Validate intent detection response
 */
function validateIntentResult(parsed: any): IntentDetectionResult {
  if (typeof parsed.requires_current_info !== 'boolean') {
    throw new Error('Invalid requires_current_info field');
  }

  if (!Array.isArray(parsed.entities)) {
    throw new Error('Invalid entities field');
  }

  if (
    typeof parsed.confidence !== 'number' ||
    parsed.confidence < 0 ||
    parsed.confidence > 1
  ) {
    throw new Error('Invalid confidence field (must be 0-1)');
  }

  if (typeof parsed.reason !== 'string' || parsed.reason.length === 0) {
    throw new Error('Invalid reason field');
  }

  return {
    requires_current_info: parsed.requires_current_info,
    entities: parsed.entities,
    confidence: parsed.confidence,
    reason: parsed.reason,
  };
}

/**
 * Fallback regex-based detection (if GPT-4o-mini fails or times out)
 */
function fallbackRegexDetection(query: string): IntentDetectionResult {
  const lowerQuery = query.toLowerCase();

  // High-confidence patterns for web search
  const webPatterns = [
    /\b(nombre|nombres|lista|catálogo|productos?)\s+(de|en)\s+\w+/i, // "nombres de Wella"
    /\b(mejor|mejores|recomendado|popular|tendencia)\s+\d{4}/i, // "mejor marca 2025"
    /\b(vs|versus|comparar|diferencia)\s+\w+/i, // "Wella vs Schwarzkopf"
    /\b(dónde|donde|comprar|precio|costo|tienda)/i, // "dónde comprar"
    /\b(proporción|mixing ratio|mezcla)\s+(de|para)\s+\w+/i, // "proporción de Goldwell"
  ];

  const hasWebPattern = webPatterns.some((pattern) => pattern.test(query));

  if (hasWebPattern) {
    return {
      requires_current_info: true,
      entities: ['brand'], // Conservative fallback
      confidence: 0.6, // Lower confidence for fallback
      reason: 'Fallback regex detection',
    };
  }

  return {
    requires_current_info: false,
    entities: [],
    confidence: 0.5, // Low confidence for safety
    reason: 'Fallback regex detection - no web pattern',
  };
}

/**
 * Detect intent using GPT-4o-mini
 */
export async function detectIntent(
  openai: OpenAI,
  supabase: SupabaseClient,
  query: string
): Promise<IntentDetectionResult> {
  const startTime = Date.now();

  // 1. Sanitize input
  const sanitizedQuery = sanitizeInput(query);
  const queryHash = await generateQueryHash(sanitizedQuery);

  // 2. Check cache
  const cached = await checkIntentCache(supabase, queryHash);
  if (cached) {
    console.log(`[Intent Detection] Cache hit - ${Date.now() - startTime}ms`);
    return cached.intent_result;
  }

  // 3. Call GPT-4o-mini with timeout (using Promise.race for reliability)
  try {
    const intentPromise = openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [
        { role: 'system', content: INTENT_DETECTION_SYSTEM_PROMPT },
        { role: 'user', content: sanitizedQuery },
      ],
      max_completion_tokens: 150,
      temperature: 0.1, // Deterministic
      response_format: { type: 'json_object' }, // Force JSON
    });

    const timeoutPromise = new Promise((_, reject) =>
      setTimeout(
        () => reject(new Error('Intent detection timeout')),
        INTENT_DETECTION_TIMEOUT_MS
      )
    );

    const completion = await Promise.race([intentPromise, timeoutPromise]) as any;

    const responseText = completion.choices[0].message.content || '{}';
    const parsed = JSON.parse(responseText);
    const validated = validateIntentResult(parsed);

    // 4. Save to cache
    await saveToIntentCache(supabase, queryHash, validated);

    const latency = Date.now() - startTime;
    console.log(
      `[Intent Detection] GPT-4o-mini success - ${latency}ms (confidence: ${validated.confidence})`
    );

    return validated;
  } catch (error: any) {
    const latency = Date.now() - startTime;
    console.error(
      `[Intent Detection] GPT-4o-mini failed after ${latency}ms:`,
      error.message
    );

    // 5. Fallback to regex
    const fallbackResult = fallbackRegexDetection(sanitizedQuery);
    console.log(
      `[Intent Detection] Using fallback regex (confidence: ${fallbackResult.confidence})`
    );

    return fallbackResult;
  }
}
