#!/usr/bin/env python3
"""
Deploy AI Proxy Edge Function via Supabase Management API
This bypasses CLI authentication by using Management API directly
"""

import os
import sys
import json
import tarfile
import io
import base64
from pathlib import Path

# Configuration
PROJECT_REF = "guyxczavhtemwlrknqpm"
FUNCTION_NAME = "ai-proxy"
FUNCTION_DIR = Path("supabase/functions/ai-proxy")

def create_function_bundle():
    """Create a tarball of all function files"""
    print("📦 Creating function bundle...")
    
    files_to_include = [
        "index.ts",
        "intelligent-router.ts",
        "hybrid-executor.ts",
        "self-correction.ts",
        "product-extractor.ts",
        "intent-detector.ts",
        "brand-validator.ts",
        "cache-manager.ts",
        "types.ts",
        "prompts.ts",
        "data/brands.json"
    ]
    
    # Create tarball in memory
    tar_buffer = io.BytesIO()
    with tarfile.open(fileobj=tar_buffer, mode='w:gz') as tar:
        for file_path in files_to_include:
            full_path = FUNCTION_DIR / file_path
            if not full_path.exists():
                print(f"   ⚠️  Warning: {file_path} not found")
                continue
            
            # Add file to tar with relative path
            tar.add(full_path, arcname=file_path)
            print(f"   ✅ Added: {file_path}")
    
    tar_buffer.seek(0)
    return tar_buffer.getvalue()

def main():
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print("🚀 Deploying AI Proxy via Supabase Management API")
    print("━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━")
    print()
    
    # Create bundle
    bundle = create_function_bundle()
    bundle_size = len(bundle) / 1024  # KB
    print(f"\n📊 Bundle size: {bundle_size:.2f} KB")
    
    print("\n⚠️  NOTE: Direct API deployment requires authentication token.")
    print("Please use one of these methods instead:")
    print()
    print("1. Browser login:")
    print("   supabase login")
    print("   supabase functions deploy ai-proxy --project-ref", PROJECT_REF)
    print()
    print("2. Or use the automated script:")
    print("   ./deploy-ai-proxy.sh")
    print()
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
