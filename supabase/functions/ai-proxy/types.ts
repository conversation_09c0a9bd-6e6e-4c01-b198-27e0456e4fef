/**
 * Shared TypeScript types for AI Proxy system
 * Location: supabase/functions/ai-proxy/types.ts
 */

/**
 * Intent detection response schema
 */
export interface IntentDetectionResult {
  requires_current_info: boolean;
  entities: IntentEntity[];
  confidence: number; // 0.0-1.0
  reason: string;
}

export type IntentEntity =
  | 'brand'
  | 'product'
  | 'date'
  | 'trend'
  | 'catalog'
  | 'comparison'
  | 'availability';

/**
 * Routing decision from intelligent router
 */
export interface RoutingDecision {
  provider: 'openai' | 'perplexity' | 'hybrid';
  model?: string; // 'gpt-4o' | 'gpt-4o-mini' | 'sonar-pro'
  reason: string;
  confidence: number;
  needsVerification?: boolean;
  domainFilter?: string[];
  cacheKey?: string;
  intentDetection?: IntentDetectionResult;
  fallbackUsed?: boolean;
  metadata?: {
    intentDetectionLatency?: number;
    cacheHit?: boolean;
    fallbackUsed?: string;
  };
}

/**
 * Intent detection cache entry
 */
export interface IntentCacheEntry {
  query_hash: string;
  intent_result: IntentDetectionResult;
  created_at: string;
  access_count: number;
}

/**
 * Product verification result from Perplexity
 */
export interface ProductVerification {
  product: {
    name: string;
    code?: string;
    verified: boolean;
    source?: string;
  };
  exists: boolean;
  alternative?: string;
  citations: any[];
}

/**
 * Extracted product from formula text
 */
export interface ExtractedProduct {
  name: string;
  code?: string;
  quantity?: string;
  type: 'color' | 'oxidant' | 'treatment' | 'lightener';
}

/**
 * Verified product with official info
 */
export interface VerifiedProduct {
  name: string;
  code?: string;
  verified: boolean;
  source?: string;
  alternative?: string;
}
