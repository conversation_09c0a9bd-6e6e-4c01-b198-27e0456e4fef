/**
 * Product Extractor - Extracts products from formula text
 * Location: supabase/functions/ai-proxy/product-extractor.ts
 *
 * Uses hybrid approach: Regex (fast) + NLP (accurate) for comprehensive extraction
 */

import OpenAI from 'https://esm.sh/openai@4.28.0';
import { ExtractedProduct } from './types.ts';

/**
 * Extract products from formula text using regex patterns
 * Fast extraction for obvious cases (80% of products)
 */
function extractWithRegex(formulaText: string): ExtractedProduct[] {
  const products: ExtractedProduct[] = [];

  // PATTERN 1: Color tones (7/03, 9.1, 6N, 8/0, etc.)
  const tonePattern = /\b(\d{1,2}[/.]?\d{0,2}[NABCRV]?)\b/g;
  const toneMatches = formulaText.matchAll(tonePattern);

  for (const match of toneMatches) {
    const code = match[1];
    // Filter out non-color numbers (like "30 minutes", "20 vol")
    if (!code.match(/^(30|40|45|60|90|120)$/)) {
      products.push({
        name: `Tono ${code}`,
        code: code,
        type: 'color',
      });
    }
  }

  // PATTERN 2: Oxidantes (20 Vol, 6%, oxidante 30, developer 20)
  const oxidantPattern = /\b(?:oxidante|revelador|developer|peróxido|welloxon|oreor)\s*(\d{1,2})\s*(?:vol|%|volumen)?/gi;
  const oxidantMatches = formulaText.matchAll(oxidantPattern);

  for (const match of oxidantMatches) {
    const volume = match[1];
    products.push({
      name: `Oxidante ${volume}Vol`,
      code: `${volume}vol`,
      type: 'oxidant',
    });
  }

  // PATTERN 3: Cantidades con producto (Majirel 7/03 60g, INOA 8.1 (50ml))
  const quantityPattern = /([A-Z][a-zA-Z\s]+\d{1,2}[/.]?\d{0,2})\s*[\(\[]?\s*(\d+)\s*(g|ml|gr)[\)\]]?/g;
  const quantityMatches = formulaText.matchAll(quantityPattern);

  for (const match of quantityMatches) {
    const productName = match[1].trim();
    const quantity = `${match[2]}${match[3]}`;

    products.push({
      name: productName,
      quantity: quantity,
      type: 'color',
    });
  }

  // PATTERN 4: Decolorantes / Lighteners (product names)
  const lightenerPattern = /\b(Blond\s+Studio|Platinium\s+Plus|Freelights|BlondMe|Blondor|Plex|Decolorante|Magic\s+Bleach)\b/gi;
  const lightenerMatches = formulaText.matchAll(lightenerPattern);

  for (const match of lightenerMatches) {
    products.push({
      name: match[1],
      type: 'lightener',
    });
  }

  // PATTERN 5: Tratamientos (Smartbond, Wellaplex, Olaplex, etc.)
  const treatmentPattern = /\b(Smartbond|Wellaplex|Olaplex|Bond|Plex|Metal\s+Detox)\b/gi;
  const treatmentMatches = formulaText.matchAll(treatmentPattern);

  for (const match of treatmentMatches) {
    products.push({
      name: match[1],
      type: 'treatment',
    });
  }

  return products;
}

/**
 * Extract products using NLP (GPT-4o-mini)
 * Handles complex cases and validates regex results
 */
async function extractWithNLP(
  openai: OpenAI,
  formulaText: string
): Promise<ExtractedProduct[]> {
  const extractionPrompt = `Extract ALL hair color products mentioned in this formula.

RESPOND ONLY WITH JSON ARRAY (no markdown, no explanations):
[
  {
    "name": "Product name as mentioned",
    "code": "Product code if mentioned (e.g., 7/03, 9.1)",
    "quantity": "Quantity if mentioned (e.g., 60g, 50ml)",
    "type": "color|oxidant|treatment|lightener"
  }
]

If no products found, return: []

FORMULA TEXT:
${formulaText}`;

  try {
    const completion = await openai.chat.completions.create({
      model: 'gpt-4o-mini',
      messages: [{ role: 'user', content: extractionPrompt }],
      temperature: 0.0,
      max_completion_tokens: 500,
      response_format: { type: 'json_object' },
    });

    const responseText = completion.choices[0].message.content || '{"products":[]}';
    const parsed = JSON.parse(responseText);

    // Handle both {products: [...]} and [...] formats
    const productsArray = Array.isArray(parsed) ? parsed : (parsed.products || []);

    return productsArray.map((p: any) => ({
      name: p.name || 'Unknown',
      code: p.code,
      quantity: p.quantity,
      type: p.type || 'color',
    }));
  } catch (error) {
    console.error('[Product Extractor] NLP extraction failed:', error);
    return []; // Fallback to empty array
  }
}

/**
 * Deduplicate products (merge regex + NLP results)
 */
function deduplicateProducts(products: ExtractedProduct[]): ExtractedProduct[] {
  const seen = new Set<string>();
  const deduplicated: ExtractedProduct[] = [];

  for (const product of products) {
    // Create unique key (normalize name)
    const key = product.code
      ? `${product.type}:${product.code}`.toLowerCase()
      : `${product.type}:${product.name}`.toLowerCase().replace(/\s+/g, '');

    if (!seen.has(key)) {
      seen.add(key);
      deduplicated.push(product);
    }
  }

  return deduplicated;
}

/**
 * Extract products from formula text (MAIN FUNCTION)
 * Combines regex (fast) + NLP (accurate) approaches
 *
 * @param openai - OpenAI client
 * @param formulaText - Formula text to extract products from
 * @returns Array of extracted products
 */
export async function extractProductsFromFormula(
  openai: OpenAI,
  formulaText: string
): Promise<ExtractedProduct[]> {
  console.log('[Product Extractor] Extracting products from formula...');

  // STEP 1: Fast extraction with regex (catches 80% of products)
  const regexProducts = extractWithRegex(formulaText);
  console.log(`[Product Extractor] Regex found ${regexProducts.length} products`);

  // STEP 2: NLP extraction for complex cases (validates + finds missed products)
  const nlpProducts = await extractWithNLP(openai, formulaText);
  console.log(`[Product Extractor] NLP found ${nlpProducts.length} products`);

  // STEP 3: Merge and deduplicate
  const allProducts = [...regexProducts, ...nlpProducts];
  const uniqueProducts = deduplicateProducts(allProducts);

  console.log(`[Product Extractor] Total unique products: ${uniqueProducts.length}`);

  return uniqueProducts;
}
