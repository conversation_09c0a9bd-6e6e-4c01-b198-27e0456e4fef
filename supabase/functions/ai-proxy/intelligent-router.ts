/**
 * Intelligent Router for AI requests
 * Location: supabase/functions/ai-proxy/intelligent-router.ts
 *
 * Routes requests to OpenAI, Perplexity, or Hybrid mode based on intent detection
 */

import OpenAI from 'https://esm.sh/openai@4.28.0';
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { detectIntent } from './intent-detector.ts';
import { validateBrandAndGetDomains } from './brand-validator.ts';
import { RoutingDecision, IntentDetectionResult } from './types.ts';

export interface RoutingRequest {
  useCase: 'vision_analysis' | 'formula_generation' | 'product_search' | 'chat';
  prompt: string;
  brand?: string;
  productLine?: string;
  hasImages: boolean;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string | Array<any>;
  }>;
  metadata?: {
    userId: string;
    timestamp: number;
  };
}

export interface FallbackStrategy {
  timeout_ms: number;
  max_retries: number;
  use_regex_fallback: boolean;
  confidence_threshold: number;
}

export const FALLBACK_CONFIG: FallbackStrategy = {
  timeout_ms: 3000, // 3 seconds timeout for intent detection
  max_retries: 1, // Retry once on timeout
  use_regex_fallback: true, // Use regex if GPT-4o-mini fails
  confidence_threshold: 0.7, // Minimum confidence to trust result
};

/**
 * Route AI request to appropriate provider
 *
 * @param openai OpenAI client
 * @param supabase Supabase client
 * @param request Routing request parameters
 * @returns Routing decision with provider, model, and metadata
 */
export async function routeRequest(
  openai: OpenAI,
  supabase: SupabaseClient,
  request: RoutingRequest
): Promise<RoutingDecision> {
  const startTime = Date.now();

  // ============================================
  // RULE 1: Images → OpenAI (only provider with vision)
  // ============================================
  if (request.hasImages) {
    return {
      provider: 'openai',
      model: 'gpt-4o',
      reason: 'Vision analysis requires OpenAI',
      confidence: 1.0,
      metadata: {
        intentDetectionLatency: 0,
        cacheHit: false,
      },
    };
  }

  // ============================================
  // RULE 2: Detect intent with GPT-4o-mini
  // ============================================
  let intentResult: IntentDetectionResult;
  let fallbackUsed = false;

  try {
    intentResult = await detectIntent(openai, supabase, request.prompt);

    // Check confidence threshold
    if (intentResult.confidence < FALLBACK_CONFIG.confidence_threshold) {
      console.warn(
        `[Router] Low confidence intent detection (${intentResult.confidence}), using conservative fallback`
      );

      // Conservative fallback: Use OpenAI for low confidence
      return {
        provider: 'openai',
        model: request.useCase === 'chat' ? 'gpt-4o-mini' : 'gpt-4o',
        reason: `Low confidence intent (${intentResult.confidence}) - using OpenAI`,
        confidence: intentResult.confidence,
        intentDetection: intentResult,
        fallbackUsed: true,
        metadata: {
          intentDetectionLatency: Date.now() - startTime,
          cacheHit: false,
          fallbackUsed: 'low_confidence',
        },
      };
    }
  } catch (error: any) {
    console.error('[Router] Intent detection failed completely:', error);
    fallbackUsed = true;

    // Ultimate fallback: Safe default (OpenAI)
    return {
      provider: 'openai',
      model: request.useCase === 'chat' ? 'gpt-4o-mini' : 'gpt-4o',
      reason: 'Intent detection failed - using safe default',
      confidence: 0.5,
      fallbackUsed: true,
      metadata: {
        intentDetectionLatency: Date.now() - startTime,
        cacheHit: false,
        fallbackUsed: 'intent_detection_error',
      },
    };
  }

  const intentLatency = Date.now() - startTime;

  // ============================================
  // RULE 3: Route based on intent detection
  // ============================================
  if (intentResult.requires_current_info) {
    return {
      provider: 'perplexity',
      model: 'sonar-pro',
      reason: intentResult.reason,
      confidence: intentResult.confidence,
      intentDetection: intentResult,
      fallbackUsed,
      metadata: {
        intentDetectionLatency: intentLatency,
        cacheHit: false,
      },
    };
  }

  // ============================================
  // RULE 4: Formula generation with brand → Hybrid mode
  // ============================================
  if (request.useCase === 'formula_generation' && request.brand) {
    // Validate brand and get domain filter
    const domains = validateBrandAndGetDomains(request.brand);

    if (!domains) {
      // Brand not in whitelist, use OpenAI only (no product verification)
      console.warn(
        '[Router] Brand not whitelisted, skipping verification:',
        request.brand
      );

      return {
        provider: 'openai',
        model: 'gpt-4o',
        reason: 'Formula generation without verification (brand not whitelisted)',
        confidence: 0.8,
        needsVerification: false,
        intentDetection: intentResult,
        metadata: {
          intentDetectionLatency: intentLatency,
          cacheHit: false,
        },
      };
    }

    // Check if brand has recent product cache (optimization)
    const { data: cachedProducts } = await supabase
      .from('product_cache')
      .select('*')
      .eq('brand', request.brand)
      .gte(
        'updated_at',
        new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString()
      ) // 30 days
      .limit(10);

    if (cachedProducts && cachedProducts.length > 5) {
      // Sufficient cached product data, use OpenAI with cached context
      console.log(
        `[Router] Using cached product context for ${request.brand} (${cachedProducts.length} products)`
      );

      return {
        provider: 'openai',
        model: 'gpt-4o',
        reason: 'Formula generation with cached product context',
        confidence: 0.95,
        needsVerification: false,
        intentDetection: intentResult,
        metadata: {
          intentDetectionLatency: intentLatency,
          cacheHit: true,
        },
      };
    }

    // Fresh brand or insufficient cache, use hybrid mode
    return {
      provider: 'hybrid',
      model: 'gpt-4o',
      reason: 'Formula requires real-time product verification',
      confidence: 0.95,
      needsVerification: true,
      domainFilter: domains,
      intentDetection: intentResult,
      metadata: {
        intentDetectionLatency: intentLatency,
        cacheHit: false,
      },
    };
  }

  // ============================================
  // RULE 5: Default to OpenAI (base knowledge)
  // ============================================
  const model = request.useCase === 'chat' ? 'gpt-4o-mini' : 'gpt-4o';

  return {
    provider: 'openai',
    model,
    reason: 'Conversational query - base knowledge',
    confidence: intentResult.confidence,
    intentDetection: intentResult,
    fallbackUsed,
    metadata: {
      intentDetectionLatency: intentLatency,
      cacheHit: false,
    },
  };
}
