import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import OpenAI from 'https://esm.sh/openai@4.28.0';

// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
// INTELLIGENT AI ORCHESTRATION (Phase 1-4)
// ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
import { routeRequest } from './intelligent-router.ts';
import { executeHybridFormula } from './hybrid-executor.ts';
import { selfCorrectWithRetry } from './self-correction.ts';
import { CacheManager } from './cache-manager.ts';

// SECURITY: Validate environment variables at startup
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY');
const PERPLEXITY_API_KEY = Deno.env.get('PERPLEXITY_API_KEY');
const SUPABASE_URL = Deno.env.get('SUPABASE_URL');
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY');

// Validate required secrets
const MISSING_SECRETS: string[] = [];
if (!OPENAI_API_KEY) MISSING_SECRETS.push('OPENAI_API_KEY');
if (!PERPLEXITY_API_KEY) MISSING_SECRETS.push('PERPLEXITY_API_KEY');
if (!SUPABASE_URL) MISSING_SECRETS.push('SUPABASE_URL');
if (!SUPABASE_SERVICE_KEY) MISSING_SECRETS.push('SUPABASE_SERVICE_ROLE_KEY');

if (MISSING_SECRETS.length > 0) {
  console.error('[AI Proxy][SECURITY] Missing required secrets:', MISSING_SECRETS.join(', '));
  throw new Error(`Missing required environment variables: ${MISSING_SECRETS.join(', ')}`);
}

// Now safe to use with non-null assertion
const OPENAI_KEY = OPENAI_API_KEY!;
const PERPLEXITY_KEY = PERPLEXITY_API_KEY!;
const SUPABASE_PROJECT_URL = SUPABASE_URL!;
const SUPABASE_SERVICE = SUPABASE_SERVICE_KEY!;

// Allow disabling rate limiting in local/testing environments.
const IS_LOCAL_SUPABASE = SUPABASE_PROJECT_URL.includes('localhost') || SUPABASE_PROJECT_URL.includes('127.0.0.1');
const RATE_LIMIT_MODE = (Deno.env.get('AI_PROXY_RATE_LIMIT_MODE') ?? 'disable').toLowerCase();
// Force rate limiting off for testing regardless of env configuration.
const SHOULD_BYPASS_RATE_LIMIT = true;

/**
 * TIMEOUT STRATEGY:
 * - OpenAI SDK: 140s for vision (GPT-4o Vision with large images can take 90-120s)
 * - Client requestTimeout: 45-60s for chat, 120s for vision/formula
 * - Formula generation: 60s client, 90s SDK (2 retries = max 180s total)
 * - Chat: 45s client (optimized for fast responses)
 * - Vision: 120s client, 140s SDK (image processing overhead, large images)
 *
 * GPT-4o family is 2-3x faster than GPT-4.5, but vision still needs time for large images
 */
const openai = new OpenAI({
  apiKey: OPENAI_KEY,
  timeout: 140000, // Increased to 140s to handle vision_analysis with large images
});

const MODEL_CONFIG = {
  chat: {
    model: 'gpt-4o-mini',
    maxTokens: {
      default: 1024,
      withImages: 2048,
      streamDefault: 960,
      streamWithImages: 1792,
    },
  },
  formula_generation: {
    model: 'gpt-4o',
    maxTokens: 2304,
    streamTokens: 2304,
  },
  vision_analysis: {
    model: 'gpt-4o',
    maxTokens: 1792,
  },
} as const;

const MODEL_PRICING: Record<string, { input: number; output: number }> = {
  'gpt-4o': { input: 5, output: 15 },
  'gpt-4o-mini': { input: 0.15, output: 0.6 },
};

function getModelPricing(model: string) {
  return MODEL_PRICING[model] || { input: 5, output: 15 };
}

/**
 * Type-safe helper to get model configuration by use case
 */
function getModelForUseCase(useCase: AIRequest['useCase']): string {
  switch (useCase) {
    case 'chat':
      return MODEL_CONFIG.chat.model;
    case 'formula_generation':
      return MODEL_CONFIG.formula_generation.model;
    case 'vision_analysis':
      return MODEL_CONFIG.vision_analysis.model;
    case 'product_search':
      return 'sonar-pro'; // Perplexity
    default:
      return 'openai-unknown';
  }
}

// Types
interface AppContext {
  screen: 'formula_step_5' | 'main_chat';
  currentFormula?: {
    naturalBase?: number;
    grayPercentage?: number;
    currentTone?: string;
    desiredLevel?: string;
    desiredTone?: string;
    selectedBrand?: string;
    productLine?: string;
    chemicalHistory?: string[];
    technique?: string;
    generatedFormula?: string; // 🆕 NEW: The generated formula text
  };
  userPreferences?: {
    brands: string[];
  };
}

interface AIRequest {
  useCase: 'vision_analysis' | 'formula_generation' | 'product_search' | 'chat';
  prompt: string;
  imageUrls?: string[];
  brand?: string;
  productLine?: string;
  systemPrompt?: string;
  temperature?: number;
  conversationHistory?: Array<{
    role: 'user' | 'assistant';
    content: string | Array<{ type: 'text'; text: string } | { type: 'image_url'; image_url: { url: string } }>;
  }>;
  stream?: boolean;
  appContext?: AppContext;
}

interface AIResponse {
  text: string;
  usage: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    cached?: boolean;
  };
  cost: number;
  latency: number;
  citations?: any[];
}

// SECURITY: Allowed origins for CORS (production + development)
const ALLOWED_ORIGINS = [
  'https://guyxczavhtemwlrknqpm.supabase.co', // Supabase project URL
  'http://localhost:8081', // Expo development (web)
  'http://localhost:19006', // Expo development (alternative port)
  'https://app.rork.com', // Production web app
  'https://rork.com', // Production website
  'capacitor://localhost', // Capacitor mobile apps (if used)
  'ionic://localhost', // Ionic mobile apps (if used)
  // NOTE: Expo Go uses custom schemes (exp://) which don't trigger CORS
  // NOTE: Production mobile apps (iOS/Android) make native requests that bypass CORS
];

/**
 * Get CORS headers with origin validation
 * SECURITY: Only allows whitelisted origins to prevent unauthorized access
 */
function getCorsHeaders(origin: string | null): HeadersInit {
  // SECURITY: Validate origin against whitelist
  const allowedOrigin = origin && ALLOWED_ORIGINS.includes(origin)
    ? origin
    : ALLOWED_ORIGINS[0]; // Default to Supabase URL

  return {
    'Access-Control-Allow-Origin': allowedOrigin,
    'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
    'Access-Control-Allow-Methods': 'POST, OPTIONS',
    'Access-Control-Max-Age': '86400', // Cache preflight for 24h
  };
}

const textEncoder = new TextEncoder();

/**
 * Detect potential prompt injection patterns
 */
function containsPromptInjection(text: string): boolean {
  const injectionPatterns = [
    /ignore\s+(previous|all)\s+instructions/i,
    /you\s+are\s+now/i,
    /system\s*prompt/i,
    /new\s+instructions/i,
    /<\|im_start\|>/i,
    /<\|im_end\|>/i,
    /\/reset/i,
    /\/debug/i,
    /disregard/i,
  ];

  return injectionPatterns.some(pattern => pattern.test(text));
}

/**
 * Validate and sanitize appContext before processing
 * Returns null if validation fails
 */
function validateAppContext(ctx: any): AppContext | null {
  if (!ctx || typeof ctx !== 'object') return null;

  // Validate screen type
  const validScreens = ['formula_step_5', 'main_chat'];
  if (!validScreens.includes(ctx.screen)) {
    console.warn('[Security] Invalid appContext.screen:', ctx.screen);
    return null;
  }

  // Validate formula_step_5 context
  if (ctx.screen === 'formula_step_5' && ctx.currentFormula) {
    const f = ctx.currentFormula;

    // Validate numeric fields
    if (f.naturalBase !== undefined && (typeof f.naturalBase !== 'number' || f.naturalBase < 1 || f.naturalBase > 10)) {
      console.warn('[Security] Invalid naturalBase:', f.naturalBase);
      return null;
    }

    if (f.grayPercentage !== undefined && (typeof f.grayPercentage !== 'number' || f.grayPercentage < 0 || f.grayPercentage > 100)) {
      console.warn('[Security] Invalid grayPercentage:', f.grayPercentage);
      return null;
    }

    // Validate string fields (prevent injection)
    const stringFields = ['currentTone', 'desiredLevel', 'desiredTone', 'selectedBrand', 'productLine', 'technique'] as const;
    for (const field of stringFields) {
      if (f[field] !== undefined) {
        if (typeof f[field] !== 'string') {
          console.warn(`[Security] Invalid ${field}:`, f[field]);
          return null;
        }

        // Prevent excessively long strings (DoS)
        if (f[field].length > 100) {
          console.warn(`[Security] ${field} too long:`, f[field].length);
          return null;
        }
      }
    }

    // Validate generatedFormula (most critical - could contain prompt injection)
    if (f.generatedFormula !== undefined) {
      if (typeof f.generatedFormula !== 'string') {
        console.warn('[Security] Invalid generatedFormula type');
        return null;
      }

      // Limit length to prevent DoS
      if (f.generatedFormula.length > 20000) {
        console.warn('[Security] generatedFormula too long:', f.generatedFormula.length);
        return null;
      }

      // Sanitize for prompt injection
      if (containsPromptInjection(f.generatedFormula)) {
        console.warn('[Security] Potential prompt injection in generatedFormula');
        return null;
      }
    }

    // Validate chemicalHistory array
    if (f.chemicalHistory !== undefined) {
      if (!Array.isArray(f.chemicalHistory)) {
        console.warn('[Security] Invalid chemicalHistory:', f.chemicalHistory);
        return null;
      }

      // Limit array size
      if (f.chemicalHistory.length > 20) {
        console.warn('[Security] Too many chemical history items:', f.chemicalHistory.length);
        return null;
      }

      // Validate each item is a string
      for (const item of f.chemicalHistory) {
        if (typeof item !== 'string' || item.length > 100) {
          console.warn('[Security] Invalid chemical history item');
          return null;
        }
      }
    }
  }

  // Validate main_chat context
  if (ctx.screen === 'main_chat' && ctx.userPreferences) {
    if (!ctx.userPreferences.brands || !Array.isArray(ctx.userPreferences.brands)) {
      console.warn('[Security] Invalid userPreferences.brands');
      return null;
    }

    // Limit array size
    if (ctx.userPreferences.brands.length > 20) {
      console.warn('[Security] Too many brands:', ctx.userPreferences.brands.length);
      return null;
    }

    // Validate each brand is a string
    for (const brand of ctx.userPreferences.brands) {
      if (typeof brand !== 'string' || brand.length > 100) {
        console.warn('[Security] Invalid brand in preferences');
        return null;
      }
    }
  }

  return ctx as AppContext;
}

/**
 * Build contextual system prompt based on appContext
 * This makes the AI aware of where the user is in the app
 */
function buildContextualSystemPrompt(
  basePrompt: string,
  appContext?: AppContext
): string {
  // Validate before logging
  const validatedContext = appContext ? validateAppContext(appContext) : null;

  if (!validatedContext) {
    if (appContext) {
      console.warn('[Edge Function] Invalid appContext received, using base prompt');
    }
    return basePrompt;
  }

  // Only log metadata in production (not full context)
  console.log('[Edge Function] ✅ Validated appContext:', {
    screen: validatedContext.screen,
    hasFormula: validatedContext.screen === 'formula_step_5' && !!validatedContext.currentFormula,
    hasPreferences: validatedContext.screen === 'main_chat' && !!validatedContext.userPreferences
  });

  // CASE A: Formula Step 5 (Formula Chat)
  if (validatedContext.screen === 'formula_step_5' && validatedContext.currentFormula) {
    const f = validatedContext.currentFormula;
    console.log('[Edge Function] ✅ Building FORMULA context prompt with data:', f);

    // Build the formula section (if generated formula is available)
    const formulaSection = f.generatedFormula ? `
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📋 FÓRMULA GENERADA (VISIBLE EN PANTALLA)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
${f.generatedFormula}

⚠️ REGLA CRÍTICA: Esta fórmula ya fue generada y está VISIBLE para el usuario.
Cuando el usuario te pregunte sobre "la fórmula", "los productos", "la mezcla", etc.,
se refiere a ESTA fórmula específica que aparece arriba.

NO digas "no se ha especificado ninguna fórmula" o "necesito más información".
La fórmula ESTÁ AQUÍ. Responde con referencia a estos productos específicos.
` : '';

    // Build product authorization section
    const authorizationSection = f.selectedBrand ? `
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🛍️ AUTORIZACIÓN DE RECOMENDACIONES
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Estás AUTORIZADO a recomendar productos específicos de ${f.selectedBrand}.

**TU CONOCIMIENTO BASE ES SUFICIENTE para:**
✅ Recomendar champús/acondicionadores de mantenimiento
✅ Sugerir protectores térmicos y tratamientos
✅ Recomendar productos complementarios (tónicos, mascarillas)
✅ Explicar para qué sirven los productos de la fórmula
✅ Sugerir alternativas dentro de la misma marca

**NO necesitas buscar en web para:**
- Productos de cuidado post-color
- Protectores térmicos
- Tratamientos de mantenimiento de marcas establecidas (Schwarzkopf, Wella, L'Oréal, Matrix, Redken)

**SOLO di "no tengo información actualizada" si:**
- Te piden productos lanzados específicamente en 2025
- Te piden precios actuales o disponibilidad en tiendas
- Te piden información sobre marcas desconocidas o muy nuevas
` : '';

    return `${basePrompt}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👨‍🎓 TU ROL COMO MENTOR COMPLETO
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Eres un MENTOR COMPLETO para profesionales de la peluquería:
- Coloristas profesionales
- Estilistas y peluqueros
- Empleados de salones
- Freelancers de peluquería

**TUS ÁREAS DE EXPERTISE:**
🎨 Técnica de coloración (fórmulas, técnicas, teoría del color)
💼 Gestión de negocio (precios, márgenes, marketing, inventario)
👥 Relaciones con clientes (manejo de expectativas, quejas, comunicación)
📈 Desarrollo profesional (especializaciones, carrera, tendencias)
🛍️ Recomendación de productos (mantenimiento, cuidado, tratamientos)

**CUANDO RESPONDAS:**
- Sé específico y práctico (no teórico)
- Da ejemplos concretos
- Considera el contexto del profesional (salón vs freelance)
- Balancea lo técnico con lo comercial

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎨 CONTEXTO DE FORMULACIÓN ACTUAL
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
El usuario está trabajando en esta fórmula específica:

📊 DATOS TÉCNICOS:
• Base natural: ${f.naturalBase || 'no especificada'}
• Canas: ${f.grayPercentage || '0'}%
• Tono actual: ${f.currentTone || 'desconocido'}
• Objetivo nivel: ${f.desiredLevel || 'no especificado'}
• Objetivo tono: ${f.desiredTone || 'desconocido'}
• Marca: ${f.selectedBrand || 'sin seleccionar'}
${f.productLine ? `• Línea: ${f.productLine}` : ''}
${f.technique ? `• Técnica: ${f.technique}` : ''}
${f.chemicalHistory?.length ? `• Historial químico: ${f.chemicalHistory.join(', ')}` : ''}
${formulaSection}${authorizationSection}
⚠️ REGLA CRÍTICA: Tus respuestas DEBEN referirse específicamente a ESTA fórmula.
NO des consejos genéricos. El usuario pregunta sobre ESTA situación concreta.

Ejemplo de respuesta contextual:
❌ MAL: "Para más aclaración, usa 30 vol en general."
✅ BIEN: "Con tu base ${f.naturalBase} y objetivo ${f.desiredLevel}, 20 vol podría quedarse corto. Te recomiendo 30 vol para asegurar el lift necesario con ${f.grayPercentage}% de canas."

Ejemplo de recomendación de productos:
❌ MAL: "No tengo información sobre productos específicos."
✅ BIEN: "Para mantener este ${f.desiredTone}, recomienda a tu clienta el champú ${f.selectedBrand} [línea específica] para cabello teñido. Protege el color y previene el desvanecimiento."`;
  }

  // CASE B: Main Chat (General Chat)
  if (validatedContext.screen === 'main_chat' && validatedContext.userPreferences) {
    const brands = validatedContext.userPreferences.brands;
    const hasBrandPreferences = brands.length > 0;

    const brandSection = hasBrandPreferences ? `
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👤 PREFERENCIAS DEL USUARIO
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Este profesional trabaja con: ${brands.join(', ')}

⚠️ REGLA CRÍTICA: Al recomendar productos, PRIORIZA estas marcas.
Solo sugiere otras si:
1. No existe el producto en estas marcas
2. Hay una razón técnica específica para usar otra

Ejemplo de respuesta contextual:
❌ MAL: "Puedes usar Wella 8/1, L'Oréal 8.1, Revlon 8.1..."
✅ BIEN: "Te recomiendo ${brands[0]} [producto específico]. ${brands[1] ? `Alternativamente, ${brands[1]} [producto].` : ''}"
` : '';

    return `${basePrompt}

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
👨‍🎓 TU ROL COMO MENTOR COMPLETO
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
Eres un MENTOR COMPLETO para profesionales de la peluquería:
- Coloristas profesionales
- Estilistas y peluqueros
- Empleados de salones
- Freelancers de peluquería

**TUS ÁREAS DE EXPERTISE:**
🎨 Técnica de coloración (fórmulas, técnicas, teoría del color)
💼 Gestión de negocio (precios, márgenes, marketing, inventario)
👥 Relaciones con clientes (manejo de expectativas, quejas, comunicación)
📈 Desarrollo profesional (especializaciones, carrera, tendencias)
🛍️ Recomendación de productos (mantenimiento, cuidado, tratamientos)

**EJEMPLOS DE PREGUNTAS QUE PUEDES RESPONDER:**
- Técnicas: "¿Cómo aplicar balayage en pelo corto?"
- Productos: "¿Qué champú recomendar para mantener rubio platino?"
- Negocio: "¿Cómo calcular el precio de un servicio de mechas?"
- Clientes: "¿Qué decirle a una clienta insatisfecha con el tono?"
- Carrera: "¿Cómo especializarme en colorimetría avanzada?"
- Tendencias: "¿Cuáles son las técnicas de color más demandadas ahora?"

**CUANDO RESPONDAS:**
- Sé específico y práctico (no solo teórico)
- Da ejemplos concretos y accionables
- Considera el contexto del profesional (salón vs freelance)
- Balancea lo técnico con lo comercial
- Usa tu conocimiento base extenso antes de buscar en web

**AUTORIZACIÓN DE RECOMENDACIONES:**
✅ PUEDES recomendar productos específicos de marcas establecidas
✅ PUEDES sugerir técnicas y métodos de aplicación
✅ PUEDES dar consejos de negocio y pricing
✅ PUEDES ayudar con manejo de clientes difíciles

**SOLO busca en web si necesitas:**
- Productos lanzados recientemente (2025)
- Precios actuales o disponibilidad
- Tendencias muy recientes
${brandSection}`;
  }

  return basePrompt;
}

function calculateCost(
  model: string,
  usage?: { prompt_tokens?: number; completion_tokens?: number }
) {
  const promptTokens = usage?.prompt_tokens || 0;
  const completionTokens = usage?.completion_tokens || 0;
  const pricing = getModelPricing(model);

  const cost =
    (promptTokens / 1_000_000) * pricing.input +
    (completionTokens / 1_000_000) * pricing.output;

  return {
    cost,
    promptTokens,
    completionTokens,
    totalTokens: promptTokens + completionTokens,
  };
}

function enqueueEvent(controller: ReadableStreamDefaultController<Uint8Array>, event: string, data: unknown) {
  const payload = `event: ${event}\ndata: ${JSON.stringify(data)}\n\n`;
  controller.enqueue(textEncoder.encode(payload));
}

async function logUsage({
  supabase,
  userId,
  useCase,
  imageCount,
  usage,
  cost,
  latency,
  model,
}: {
  supabase: ReturnType<typeof createClient>;
  userId: string;
  useCase: AIRequest['useCase'];
  imageCount: number;
  usage?: { prompt_tokens?: number; completion_tokens?: number };
  cost: number;
  latency: number;
  model: string;
}) {
  try {
    await supabase.from('ai_usage_log').insert({
      user_id: userId,
      provider: 'openai',
      model,
      use_case: useCase,
      prompt_tokens: usage?.prompt_tokens || 0,
      completion_tokens: usage?.completion_tokens || 0,
      total_tokens: (usage?.prompt_tokens || 0) + (usage?.completion_tokens || 0),
      cost_usd: cost,
      image_count: imageCount,
      citations: null,
      latency_ms: latency,
    });
  } catch (error) {
    console.error('[AI Proxy] Failed to log usage:', error);
  }
}

/**
 * SECURITY: Log security-related events for monitoring
 */
async function logSecurityEvent({
  supabase,
  eventType,
  severity,
  userId,
  req,
  details,
}: {
  supabase: ReturnType<typeof createClient>;
  eventType: 'auth_failed' | 'rate_limit_exceeded' | 'idor_attempt' | 'prompt_injection_detected' |
             'xss_attempt' | 'sql_injection_attempt' | 'cors_violation' | 'invalid_api_key' | 'unauthorized_access';
  severity: 'low' | 'medium' | 'high' | 'critical';
  userId?: string;
  req: Request;
  details?: Record<string, any>;
}) {
  try {
    await supabase.rpc('log_security_event', {
      p_event_type: eventType,
      p_severity: severity,
      p_user_id: userId || null,
      p_ip_address: req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown',
      p_user_agent: req.headers.get('user-agent') || 'unknown',
      p_request_path: new URL(req.url).pathname,
      p_request_method: req.method,
      p_request_body: null, // Don't log body for privacy
      p_details: details || null,
    });
  } catch (error) {
    console.error('[AI Proxy][SECURITY] Failed to log security event:', error);
  }
}

async function streamOpenAICompletion({
  messages,
  maxTokens,
  supabase,
  userId,
  useCase,
  imageCount,
  startTime,
  model,
  corsHeaders,
}: {
  messages: any[];
  maxTokens: number;
  supabase: ReturnType<typeof createClient>;
  userId: string;
  useCase: AIRequest['useCase'];
  imageCount: number;
  startTime: number;
  model: string;
  corsHeaders: HeadersInit;
}) {
  const completion = await openai.chat.completions.create({
    model,
    messages,
    max_completion_tokens: maxTokens,
    stream: true,
  });

  let accumulatedText = '';
  let usage: { prompt_tokens?: number; completion_tokens?: number } | undefined;

  const stream = new ReadableStream<Uint8Array>({
    async start(controller) {
      try {
        for await (const part of completion) {
          const delta = part.choices?.[0]?.delta?.content || '';
          if (delta) {
            accumulatedText += delta;
            enqueueEvent(controller, 'token', {
              delta,
              text: accumulatedText,
            });
          }

          if (part.usage) {
            usage = part.usage;
          }
        }

        const latency = Date.now() - startTime;
        const { cost, promptTokens, completionTokens, totalTokens } = calculateCost(model, usage);

        enqueueEvent(controller, 'done', {
          text: accumulatedText,
          usage: {
            prompt_tokens: promptTokens,
            completion_tokens: completionTokens,
            total_tokens: totalTokens,
          },
          cost,
          latency,
        });

        controller.close();

        await logUsage({
          supabase,
          userId,
          useCase,
          imageCount,
          usage,
          cost,
          latency,
          model,
        });
      } catch (error: any) {
        console.error('[AI Proxy] Streaming error:', error);
        enqueueEvent(controller, 'error', {
          message: error?.message || 'Error desconocido durante el streaming',
        });
        controller.close();
      }
    },
    cancel() {
      console.log('[AI Proxy] Streaming cancelled by client');
    },
  });

  return new Response(stream, {
    status: 200,
    headers: {
      ...corsHeaders,
      'Content-Type': 'text/event-stream',
      'Cache-Control': 'no-cache, no-transform',
      Connection: 'keep-alive',
    },
  });
}


/**
 * Detects if OpenAI response is a safety rejection
 */
function isVisionSafetyRejection(responseText: string): boolean {
  const rejectionPatterns = [
    "I'm sorry",
    "Lo siento",
    "I can't help",
    "no puedo ayudar",
    "I'm unable to assist",
    "no puedo asistir",
    "I cannot assist",
    "I apologize",
    "Lo lamento",
    "I'm not able to",
    "no estoy en capacidad",
  ];

  const lowerText = responseText.toLowerCase().trim();
  
  // Check if response is suspiciously short (< 50 chars) AND contains rejection pattern
  const isSuspiciouslyShort = responseText.length < 50;
  const containsRejectionPattern = rejectionPatterns.some(pattern => 
    lowerText.includes(pattern.toLowerCase())
  );

  return isSuspiciouslyShort && containsRejectionPattern;
}

serve(async (req) => {
  // SECURITY: Get origin for CORS validation
  const origin = req.headers.get('Origin');
  const corsHeaders = getCorsHeaders(origin);

  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // 1. Authentication
    const authHeader = req.headers.get('Authorization');
    const supabase = createClient(SUPABASE_PROJECT_URL, SUPABASE_SERVICE);

    if (!authHeader) {
      // SECURITY: Log authentication failure
      await logSecurityEvent({
        supabase,
        eventType: 'auth_failed',
        severity: 'medium',
        req,
        details: { reason: 'missing_auth_header' },
      });

      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');

    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      // SECURITY: Log authentication failure
      await logSecurityEvent({
        supabase,
        eventType: 'auth_failed',
        severity: 'medium',
        req,
        details: { reason: 'invalid_token', error: authError?.message },
      });

      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 2. Rate limiting (optional)
    if (!SHOULD_BYPASS_RATE_LIMIT) {
      const { data: canProceed, error: rateLimitError } = await supabase.rpc('check_rate_limit', {
        p_user_id: user.id,
        p_daily_limit: 500,  // Ajustado para desarrollo y testing
        p_hourly_limit: 100, // Aumentado de 20 a 100 para ser menos restrictivo
      });

      if (rateLimitError) {
        console.error('Rate limit check error:', rateLimitError);
      }

      if (!canProceed) {
        // SECURITY: Log rate limit exceeded
        await logSecurityEvent({
          supabase,
          eventType: 'rate_limit_exceeded',
          severity: 'low',
          userId: user.id,
          req,
          details: { daily_limit: 500, hourly_limit: 100 },
        });

        return new Response(
          JSON.stringify({
            error: 'rate_limit_exceeded',
            message: 'Has excedido el límite de uso. Por favor, intenta más tarde o actualiza tu plan.'
          }),
          { status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }
    } else {
      console.warn(`[AI Proxy] Rate limiting disabled (mode: ${RATE_LIMIT_MODE}) for user ${user.id}`);
    }

    // 3. Parse request body
    const body: AIRequest = await req.json();

    // Validate total request size (DoS protection)
    const requestSize = JSON.stringify(body).length;
    const MAX_REQUEST_SIZE = 500_000; // 500 KB (allows for images in base64)

    if (requestSize > MAX_REQUEST_SIZE) {
      console.warn('[Security] Request size exceeded:', requestSize, 'bytes');
      return new Response(
        JSON.stringify({
          error: 'payload_too_large',
          message: 'Request payload exceeds maximum allowed size',
          maxSize: MAX_REQUEST_SIZE,
          actualSize: requestSize
        }),
        { status: 413, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const {
      useCase,
      prompt,
      imageUrls,
      brand,
      productLine,
      systemPrompt,
      temperature,
      conversationHistory,
      stream = false,
      appContext,
    } = body;

    // Validate appContext size specifically (additional check)
    if (appContext) {
      const contextSize = JSON.stringify(appContext).length;
      const MAX_CONTEXT_SIZE = 50_000; // 50 KB

      if (contextSize > MAX_CONTEXT_SIZE) {
        console.warn('[Security] appContext too large:', contextSize, 'bytes - stripping it out');
        body.appContext = undefined;
      }
    }

    // Debug logging para diagnosticar problema de imágenes (metadata only)
    console.log(`[Request] useCase: ${useCase}, hasImages: ${!!imageUrls}, imageCount: ${imageUrls?.length || 0}`);
    if (imageUrls && imageUrls.length > 0) {
      console.log(`[Request] First image format: ${imageUrls[0].substring(0, 50)}...`);
      console.log(`[Request] First image size: ${Math.round(imageUrls[0].length / 1024)}KB`);
    }

    if (!useCase || !prompt) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: useCase and prompt' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Process based on use case
    let response: string;
    let usage: any = {};
    let cost = 0;
    let citations: any[] = [];
    const startTime = Date.now();
    let modelUsed = getModelForUseCase(useCase); // Initialize with correct model immediately

    if (stream) {
      if (useCase === 'product_search' || useCase === 'vision_analysis') {
        return new Response(
          JSON.stringify({
            error: 'stream_not_supported',
            message: `Streaming no está disponible para el caso de uso ${useCase}`,
          }),
          { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
        );
      }

      if (useCase === 'chat') {
        const contextualPrompt = buildContextualSystemPrompt(
          systemPrompt || 'Eres un asistente experto en coloración capilar.',
          appContext
        );
        const chatMessages: any[] = [
          { role: 'system', content: contextualPrompt },
        ];

        if (conversationHistory && conversationHistory.length > 0) {
          chatMessages.push(...conversationHistory);
        }

        if (imageUrls && imageUrls.length > 0) {
          chatMessages.push({
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              ...imageUrls.map(url => ({
                type: 'image_url',
                image_url: { url, detail: 'high' },
              })),
            ],
          });
        } else {
          chatMessages.push({ role: 'user', content: prompt });
        }

        const imageCount = chatMessages.reduce((count, msg) => {
          if (Array.isArray(msg.content)) {
            return count + msg.content.filter((c: any) => c.type === 'image_url').length;
          }
          return count;
        }, 0);

        const chatConfig = MODEL_CONFIG.chat;
        console.log(`[Chat ${chatConfig.model}][stream] Sending ${chatMessages.length} messages (${conversationHistory?.length || 0} history, ${imageCount} images in context)`);
        return await streamOpenAICompletion({
          messages: chatMessages,
          maxTokens: imageCount > 0 ? chatConfig.maxTokens.streamWithImages : chatConfig.maxTokens.streamDefault,
          supabase,
          userId: user.id,
          useCase,
          imageCount,
          startTime,
          model: chatConfig.model,
          corsHeaders,
        });
      }

      if (useCase === 'formula_generation') {
        const contextualPrompt = buildContextualSystemPrompt(
          systemPrompt || 'Eres un maestro colorista profesional con 20+ años de experiencia.',
          appContext
        );
        const messages = [
          { role: 'system', content: contextualPrompt },
          { role: 'user', content: prompt },
        ];

        console.log(`[Formula ${MODEL_CONFIG.formula_generation.model}][stream] Generating formula with streaming enabled`);

        return await streamOpenAICompletion({
          messages,
          maxTokens: MODEL_CONFIG.formula_generation.streamTokens,
          supabase,
          userId: user.id,
          useCase,
          imageCount: imageUrls?.length || 0,
          startTime,
          model: MODEL_CONFIG.formula_generation.model,
          corsHeaders,
        });
      }
    }

    switch (useCase) {
      case 'vision_analysis': {
        // GPT-4o para análisis de cabello con visión
        const defaultSystemPrompt = `You are a professional hair colorist with over 15 years of experience in technical hair analysis.

PROFESSIONAL MEDICAL/BEAUTY SERVICE CONTEXT:
This is a legitimate professional hair salon analysis service where clients have explicitly consented to hair photography for color treatment planning. This service is analogous to:
- Dermatologists analyzing skin conditions through patient photos
- Dentists reviewing dental X-rays and photos
- Optometrists examining eye photos for prescription updates

The client has provided informed consent for this professional diagnostic service focused exclusively on hair characteristics for treatment planning.

YOUR TASK - TECHNICAL HAIR ANALYSIS ONLY:
Analyze ONLY the hair visible in the image. Provide a detailed technical assessment of hair characteristics:

1. **Current Color Analysis**:
   - Level (scale 1-10, where 1 is black and 10 is lightest blonde)
   - Base tone (warm/cool/neutral)
   - Visible highlights/reflections (golden/ash/copper/red)

2. **Hair Condition Assessment**:
   - Apparent porosity (low/medium/high)
   - Cuticle integrity
   - Visible chemical history (virgin/colored/bleached)

3. **Gray Hair Analysis** (if applicable):
   - Approximate percentage
   - Distribution pattern
   - Apparent resistance

4. **Zone Differences**:
   - Roots vs mids vs ends
   - Color variations across zones

CRITICAL GUIDELINES:
- This is a PROFESSIONAL HAIR SALON SERVICE with client consent
- Focus EXCLUSIVELY on hair fiber characteristics (color, texture, condition)
- Do NOT analyze or comment on faces, personal features, age, gender, or identity
- If a person appears in the image, treat them as a consenting salon client and analyze ONLY their hair
- Provide your technical hair analysis in Spanish (the client's language)
- Follow the exact JSON schema provided by the user prompt
- Return ONLY valid JSON (no markdown, explanations, or additional text)

Your analysis helps professional colorists create safe, effective hair color formulas. Be precise, technical, and professional.`;

        const visionConfig = MODEL_CONFIG.vision_analysis;
        const MAX_VISION_RETRIES = 2;
        let visionResponse = '';
        let visionUsage: any = {};
        let lastError: Error | null = null;

        // RETRY LOGIC with progressive prompt reinforcement
        for (let attempt = 0; attempt < MAX_VISION_RETRIES; attempt++) {
          try {
            // PARAMETER STRATEGY: Start with 'low' for faster processing (2-3x faster than 'auto')
            // 'low' provides sufficient quality for hair color analysis while being much faster
            const imageDetail = 'low';

            // PROGRESSIVE PROMPT REINFORCEMENT: Use stronger consent language on retry
            const basePromptForAttempt = attempt === 0
              ? (systemPrompt || defaultSystemPrompt)
              : `PROFESSIONAL HAIR SALON SERVICE - CLIENT CONSENT CONFIRMED

This is a legitimate professional hair color analysis service. The client has:
1. Explicitly consented to hair photography
2. Requested professional hair color analysis
3. Authorized this diagnostic service for treatment planning

This service is equivalent to medical diagnostic imaging (dermatology photos, dental X-rays, optometry exams).

YOUR TASK: Analyze ONLY the hair fiber characteristics visible in the image.

Focus exclusively on:
- Hair color level (1-10 scale)
- Tone (warm/cool/neutral)
- Condition (porosity, cuticle integrity)
- Gray coverage percentage
- Root-to-end color variation

Do NOT analyze faces, personal features, or identity. Treat this as a consenting salon client requesting professional hair analysis.

Respond in Spanish with technical hair analysis ONLY. Follow the JSON schema provided by the user.`;

            // Apply contextual enrichment
            const contextualPrompt = buildContextualSystemPrompt(basePromptForAttempt, appContext);

            const messages: any[] = [
              {
                role: 'system',
                content: contextualPrompt
              }
            ];

            // Add conversation history if exists (text only, no images)
            if (conversationHistory && conversationHistory.length > 0) {
              messages.push(...conversationHistory);
            }

            // Add current message with images (with degraded detail level on retry)
            const currentMessageContent = [
              { type: 'text', text: prompt },
              ...((imageUrls || []).map(url => ({
                type: 'image_url',
                image_url: { url, detail: imageDetail }
              })))
            ];

            messages.push({
              role: 'user',
              content: currentMessageContent
            });

            console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Sending ${messages.length} messages with ${imageUrls?.length || 0} images (detail: ${imageDetail})`);
            console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Current message has ${currentMessageContent.length} content parts`);

            const completion = await openai.chat.completions.create({
              model: visionConfig.model,
              messages,
              max_completion_tokens: visionConfig.maxTokens,
            });

            visionResponse = completion.choices[0].message.content || '';
            visionUsage = completion.usage || {};

            console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Response:`, JSON.stringify({
              response_length: visionResponse.length,
              response_preview: visionResponse.substring(0, 100),
              finish_reason: completion.choices[0].finish_reason,
              usage: completion.usage
            }));

            // Check for safety rejection
            if (isVisionSafetyRejection(visionResponse)) {
              console.warn(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] SAFETY REJECTION DETECTED:`, visionResponse);
              
              // If we have retries left, continue loop
              if (attempt < MAX_VISION_RETRIES - 1) {
                console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Retrying with degraded parameters...`);
                continue;
              } else {
                // All retries exhausted, throw structured error
                console.error(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] All retries exhausted, returning structured error`);
                return new Response(
                  JSON.stringify({
                    error: 'vision_safety_rejection',
                    message: 'No pudimos analizar las imágenes debido a restricciones de seguridad. Por favor, intenta con fotos que muestren solo el cabello sin rostros visibles, o usa fotos con mejor iluminación.',
                    retryable: true
                  }),
                  { status: 422, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
                );
              }
            }

            // Success! Break out of retry loop
            console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] SUCCESS - Valid response received`);
            break;

          } catch (error: any) {
            console.error(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Error:`, error);
            lastError = error;
            
            // If we have retries left and it's a retryable error, continue
            if (attempt < MAX_VISION_RETRIES - 1) {
              console.log(`[Vision ${visionConfig.model}][Attempt ${attempt + 1}/${MAX_VISION_RETRIES}] Retrying after error...`);
              continue;
            } else {
              // All retries exhausted, throw the last error
              throw lastError;
            }
          }
        }

        // Check for empty response
        if (!visionResponse || visionResponse.length === 0) {
          console.error(`[Vision ${visionConfig.model}] EMPTY RESPONSE after ${MAX_VISION_RETRIES} attempts`, {
            completion_tokens: visionUsage?.completion_tokens,
            usage: visionUsage
          });
        }

        response = visionResponse;
        usage = visionUsage;

        const { cost: computedCost } = calculateCost(visionConfig.model, usage);
        cost = computedCost;
        break;
      }

      case 'formula_generation': {
        // ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        // INTELLIGENT ROUTING + HYBRID EXECUTION + SELF-CORRECTION
        // ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        const formulaConfig = MODEL_CONFIG.formula_generation;

        try {
          // STEP 1: Intelligent Routing (decide OpenAI vs Hybrid)
          const routingDecision = await routeRequest(openai, supabase, {
            prompt,
            hasImages: false, // Formula generation never has images
            useCase: 'formula_generation',
            brand,
            productLine,
          });

          console.log(`[Formula Routing] Decision: ${routingDecision.provider} (confidence: ${routingDecision.confidence}, reason: ${routingDecision.reason})`);

          if (routingDecision.provider === 'hybrid') {
            // STEP 2a: HYBRID MODE - Generate + Verify + Self-Correct
            const contextualPrompt = buildContextualSystemPrompt(
              systemPrompt || 'Eres un maestro colorista profesional con 20+ años de experiencia.',
              appContext
            );
            const hybridResult = await executeHybridFormula(openai, supabase, {
              systemPrompt: contextualPrompt,
              userPrompt: prompt,
              brand: brand!,
              productLine,
              domainFilter: routingDecision.domainFilter,
            });

            console.log(`[Hybrid Executor] Formula generated with ${hybridResult.verifiedProducts.length} products (${hybridResult.verificationMetadata.verifiedCount} verified, ${hybridResult.verificationMetadata.unverifiedCount} unverified)`);

            // STEP 3: Self-Correction (if unverified products exist)
            if (hybridResult.verificationMetadata.unverifiedCount > 0) {
              const correctionResult = await selfCorrectWithRetry(
                openai,
                hybridResult.formula,
                hybridResult.verifiedProducts.map(p => ({
                  product: { name: p.name, code: p.code, source: p.source },
                  exists: p.verified,
                  alternative: p.alternative,
                  citations: [],
                })),
                brand!,
                productLine
              );

              console.log(`[Self-Correction] ${correctionResult.totalCorrections} corrections made in ${correctionResult.attempts} attempts`);

              response = correctionResult.finalFormula;
            } else {
              response = hybridResult.formula;
            }

            // Build usage metadata (hybrid mode tracks verification cost separately)
            usage = {
              prompt_tokens: 0, // Tracked separately in hybrid executor
              completion_tokens: 0,
              total_tokens: 0,
              hybrid_mode: true,
              verified_products: hybridResult.verificationMetadata.verifiedCount,
              unverified_products: hybridResult.verificationMetadata.unverifiedCount,
            };

            citations = hybridResult.citations;

            // Estimate cost (OpenAI + Perplexity verification)
            cost = 0.01; // Placeholder - real cost logged separately

          } else {
            // STEP 2b: OPENAI MODE (Standard GPT-4o generation)
            const contextualPrompt = buildContextualSystemPrompt(
              systemPrompt || 'Eres un maestro colorista profesional con 20+ años de experiencia.',
              appContext
            );
            const completion = await openai.chat.completions.create({
              model: formulaConfig.model,
              messages: [
                { role: 'system', content: contextualPrompt },
                { role: 'user', content: prompt }
              ],
              max_completion_tokens: formulaConfig.maxTokens,
            });

            response = completion.choices[0].message.content || '';
            usage = completion.usage || {};

            const { cost: computedCost } = calculateCost(formulaConfig.model, usage);
            cost = computedCost;
          }

        } catch (error: any) {
          // Handle OpenAI timeout specifically
          if (error?.code === 'ETIMEDOUT' || error?.message?.includes('timeout')) {
            throw new Error('La generación de fórmula tardó demasiado. Intenta simplificar la solicitud o vuelve a intentarlo.');
          }
          throw error;
        }
        break;
      }

      case 'product_search': {
        // Perplexity Sonar Pro para búsqueda de productos actualizada
        // Primero verificar cache
        const cacheKey = `${brand || ''}_${productLine || ''}_${prompt}`.toLowerCase();

        const { data: cached } = await supabase
          .from('product_cache')
          .select('*')
          .eq('brand', brand || '')
          .eq('product_line', productLine || '')
          .eq('query_text', prompt)
          .gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .maybeSingle();

        if (cached) {
          // Cache HIT - ahorrar costo
          await supabase
            .from('product_cache')
            .update({
              access_count: cached.access_count + 1,
              last_accessed_at: new Date().toISOString()
            })
            .eq('id', cached.id);

          response = typeof cached.response_data === 'string'
            ? cached.response_data
            : (cached.response_data.content || JSON.stringify(cached.response_data));
          citations = cached.citations || [];
          cost = 0; // Cache es gratis
          usage = { cached: true, cache_hit: true };
        } else {
          // Cache MISS - llamar a Perplexity
          const perplexityRes = await fetch('https://api.perplexity.ai/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${PERPLEXITY_KEY}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'sonar-pro',
              messages: [
                {
                  role: 'system',
                  content: `Eres un experto en productos profesionales de coloración capilar.
                  Busca información ACTUALIZADA y PRECISA sobre ${brand} ${productLine || ''}.
                  Proporciona: nombres de productos, códigos, volúmenes de oxidante, proporciones de mezcla.
                  IMPORTANTE: SOLO usa fuentes oficiales del fabricante. Incluye SIEMPRE las fuentes.`
                },
                { role: 'user', content: prompt }
              ],
              max_tokens: 1024,
              temperature: 0.2,
              search_domain_filter: brand ? (() => {
                // Sanitize brand name: remove special chars, spaces, normalize
                const sanitizedBrand = brand
                  .toLowerCase()
                  .normalize('NFD').replace(/[\u0300-\u036f]/g, '') // Remove accents
                  .replace(/[^a-z0-9]/g, '') // Remove non-alphanumeric
                  .trim();

                // Only add domain filter if sanitized brand is valid
                if (sanitizedBrand && sanitizedBrand.length > 2) {
                  return [
                    `${sanitizedBrand}.com`,
                    `${sanitizedBrand}.es`,
                    `${sanitizedBrand}.fr`,
                    `${sanitizedBrand}.de`,
                  ];
                }
                return undefined; // No filter if brand name is invalid
              })() : undefined,
              return_citations: true,
            }),
          });

          if (!perplexityRes.ok) {
            throw new Error(`Perplexity API error: ${perplexityRes.status} ${await perplexityRes.text()}`);
          }

          const perplexityData = await perplexityRes.json();
          response = perplexityData.choices[0].message.content;
          citations = perplexityData.citations || [];
          usage = perplexityData.usage || {};

          // Pricing Perplexity: $5/1000 searches + $3 input/$15 output per 1M tokens
          cost = 0.005 + // Base search cost
                 ((usage.prompt_tokens || 0) / 1_000_000) * 3 +
                 ((usage.completion_tokens || 0) / 1_000_000) * 15;

          // Guardar en cache
          await supabase.from('product_cache').insert({
            brand: brand || '',
            product_line: productLine || '',
            query_text: prompt,
            response_data: { content: response },
            citations: citations,
            access_count: 0,
          });
        }
        break;
      }

      case 'chat': {
        // ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        // INTELLIGENT ROUTING FOR CHAT (detect if needs current info)
        // ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
        const hasImages = !!(imageUrls && imageUrls.length > 0);

        // STEP 1: Intelligent Routing (only for text-only queries, images always go to OpenAI)
        if (!hasImages) {
          try {
            const routingDecision = await routeRequest(openai, supabase, {
              prompt,
              hasImages: false,
              useCase: 'chat',
            });

            console.log(`[Chat Routing] Decision: ${routingDecision.provider} (confidence: ${routingDecision.confidence}, reason: ${routingDecision.reason})`);

            // If routing suggests Perplexity, use product_search endpoint
            if (routingDecision.provider === 'perplexity') {
              // Redirigir a Perplexity para queries que necesitan info actualizada
              const perplexityRes = await fetch('https://api.perplexity.ai/chat/completions', {
                method: 'POST',
                headers: {
                  'Authorization': `Bearer ${PERPLEXITY_KEY}`,
                  'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                  model: 'sonar-pro',
                  messages: [
                    {
                      role: 'system',
                      content: `Eres un experto en productos profesionales de coloración capilar.
                      Proporciona información ACTUALIZADA y PRECISA.
                      IMPORTANTE: SOLO usa fuentes oficiales del fabricante. Incluye SIEMPRE las fuentes.`
                    },
                    { role: 'user', content: prompt }
                  ],
                  max_tokens: 1024,
                  temperature: 0.2,
                  return_citations: true,
                }),
              });

              if (!perplexityRes.ok) {
                console.warn('[Chat] Perplexity failed, falling back to OpenAI');
                // Fall through to OpenAI logic below
              } else {
                const perplexityData = await perplexityRes.json();
                response = perplexityData.choices[0].message.content;
                citations = perplexityData.citations || [];
                usage = perplexityData.usage || {};
                modelUsed = 'sonar-pro';

                // Pricing Perplexity
                cost = 0.005 +
                       ((usage.prompt_tokens || 0) / 1_000_000) * 3 +
                       ((usage.completion_tokens || 0) / 1_000_000) * 15;

                break; // Exit switch case, response ready
              }
            }
          } catch (routingError: any) {
            console.warn('[Chat] Routing failed, using OpenAI fallback:', routingError);
            // Fall through to OpenAI logic below
          }
        }

        // STEP 2: OpenAI chat (default or fallback)
        const contextualPrompt = buildContextualSystemPrompt(
          systemPrompt || 'Eres un asistente experto en coloración capilar.',
          appContext
        );
        const chatMessages: any[] = [
          { role: 'system', content: contextualPrompt }
        ];

        // Agregar historial de conversación si existe (puede contener imágenes)
        if (conversationHistory && conversationHistory.length > 0) {
          // Pasar conversationHistory directamente - ya viene con formato correcto de OpenAI
          // (content puede ser string o array con {type: 'text'} y {type: 'image_url'})
          chatMessages.push(...conversationHistory);
        }

        // Agregar mensaje actual del usuario (puede incluir imágenes via imageUrls)
        if (imageUrls && imageUrls.length > 0) {
          // Mensaje con imágenes (multi-modal)
          chatMessages.push({
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              ...imageUrls.map(url => ({
                type: 'image_url',
                image_url: { url, detail: 'high' }
              }))
            ]
          });
        } else {
          // Mensaje solo texto
          chatMessages.push({ role: 'user', content: prompt });
        }

        // Contar imágenes en el contexto completo para logging
        const imageCount = chatMessages.reduce((count, msg) => {
          if (Array.isArray(msg.content)) {
            return count + msg.content.filter((c: any) => c.type === 'image_url').length;
          }
          return count;
        }, 0);

        const chatConfig = MODEL_CONFIG.chat;
        console.log(`[Chat ${chatConfig.model}] Sending ${chatMessages.length} messages (${conversationHistory?.length || 0} history, ${imageCount} images in context)`);

        const completion = await openai.chat.completions.create({
          model: chatConfig.model,
          messages: chatMessages,
          max_completion_tokens: imageCount > 0 ? chatConfig.maxTokens.withImages : chatConfig.maxTokens.default,
        });

        // Debug logging para diagnosticar responses vacías
        console.log('[chat] OpenAI completion:', JSON.stringify({
          choices_length: completion.choices?.length,
          first_choice: completion.choices?.[0] ? {
            message: completion.choices[0].message,
            finish_reason: completion.choices[0].finish_reason
          } : null,
          usage: completion.usage
        }));

        response = completion.choices[0].message.content || '';

        if (!response || response.length === 0) {
          console.error(`[chat] EMPTY RESPONSE from ${chatConfig.model}!`, {
            completion_tokens: completion.usage?.completion_tokens,
            message_content: completion.choices[0].message.content,
            message_keys: Object.keys(completion.choices[0].message || {})
          });
        }

        usage = completion.usage || {};

        const { cost: computedCost } = calculateCost(chatConfig.model, usage);
        cost = computedCost;
        break;
      }

      default:
        throw new Error(`Unknown use case: ${useCase}`);
    }

    const latency = Date.now() - startTime;

    // 5. Log de auditoría
    await supabase.from('ai_usage_log').insert({
      user_id: user.id,
      provider: useCase === 'product_search' ? 'perplexity' : 'openai',
      model: modelUsed,
      use_case: useCase,
      prompt_tokens: usage.prompt_tokens || 0,
      completion_tokens: usage.completion_tokens || 0,
      total_tokens: (usage.prompt_tokens || 0) + (usage.completion_tokens || 0),
      cost_usd: cost,
      image_count: imageUrls?.length || 0,
      citations: citations.length > 0 ? citations : null,
      latency_ms: latency,
    });

    // 6. Response
    const responseBody: AIResponse = {
      text: response,
      usage: {
        prompt_tokens: usage.prompt_tokens,
        completion_tokens: usage.completion_tokens,
        total_tokens: (usage.prompt_tokens || 0) + (usage.completion_tokens || 0),
        cached: usage.cached || false,
      },
      cost,
      latency,
      citations: citations.length > 0 ? citations : undefined,
    };

    return new Response(
      JSON.stringify(responseBody),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error: any) {
    console.error('AI Proxy Error:', error);

    return new Response(
      JSON.stringify({
        error: 'internal_server_error',
        message: 'Error al procesar la solicitud de IA',
        details: error.message || error.toString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
