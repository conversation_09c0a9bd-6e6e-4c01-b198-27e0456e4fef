/**
 * Hybrid Executor - Generates formulas with OpenAI + verifies products with Perplexity
 * Location: supabase/functions/ai-proxy/hybrid-executor.ts
 */

import OpenAI from 'https://esm.sh/openai@4.28.0';
import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';
import { extractProductsFromFormula } from './product-extractor.ts';
import { ProductVerification, ExtractedProduct, VerifiedProduct } from './types.ts';

const PERPLEXITY_API_KEY = Deno.env.get('PERPLEXITY_API_KEY')!;
const MAX_PARALLEL_VERIFICATIONS = 5; // Batch size to avoid rate limits

/**
 * Verify a single product with Perplexity
 */
async function verifyProductWithPerplexity(
  product: ExtractedProduct,
  brand: string,
  productLine?: string,
  domainFilter?: string[]
): Promise<ProductVerification> {
  const productDesc = product.code
    ? `${product.name} (código ${product.code})`
    : product.name;

  const query = `Busca el producto profesional de coloración EXACTO "${productDesc}" de la marca ${brand}${
    productLine ? ` línea ${productLine}` : ''
  }.

IMPORTANTE: SOLO productos que existan actualmente en el catálogo oficial del fabricante.

RESPONDE SOLO CON JSON (sin markdown):
{
  "exists": true/false,
  "official_name": "Nombre exacto del fabricante (si existe)",
  "code": "Código oficial del producto (si aplica)",
  "alternative": "Si no existe, sugiere producto equivalente de la misma marca",
  "url": "URL oficial del fabricante (si está disponible)",
  "reason": "Breve explicación (max 80 caracteres)"
}`;

  try {
    const response = await fetch('https://api.perplexity.ai/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${PERPLEXITY_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'sonar-pro',
        messages: [{ role: 'user', content: query }],
        temperature: 0.0, // Deterministic
        max_tokens: 300,
        search_domain_filter: domainFilter, // Only search official brand domains
        return_citations: true,
      }),
    });

    if (!response.ok) {
      throw new Error(`Perplexity API error: ${response.status}`);
    }

    const data = await response.json();
    const content = data.choices[0].message.content;

    // Parse JSON response (handle markdown code blocks if present)
    try {
      const jsonMatch = content.match(/\{[\s\S]*\}/);
      const result = jsonMatch ? JSON.parse(jsonMatch[0]) : JSON.parse(content);

      return {
        product: {
          name: result.official_name || product.name,
          code: result.code || product.code,
          verified: result.exists || false,
          source: result.url,
        },
        exists: result.exists || false,
        alternative: result.alternative,
        citations: data.citations || [],
      };
    } catch (parseError: any) {
      console.error(`[Perplexity] JSON parse error for "${product.name}":`, parseError);

      // Return unverified (safe fallback)
      return {
        product: {
          name: product.name,
          code: product.code,
          verified: false,
        },
        exists: false,
        alternative: 'No se pudo verificar - respuesta inválida de API',
        citations: [],
      };
    }
  } catch (error: any) {
    console.error(`[Hybrid Executor] Product verification failed for "${product.name}":`, error);

    // Return unverified (don't fail the whole formula)
    return {
      product: {
        name: product.name,
        code: product.code,
        verified: false,
      },
      exists: false,
      alternative: 'No se pudo verificar - consulta con el fabricante',
      citations: [],
    };
  }
}

/**
 * Verify products in batches (avoid rate limits)
 */
async function verifyProductsBatch(
  products: ExtractedProduct[],
  brand: string,
  productLine?: string,
  domainFilter?: string[]
): Promise<ProductVerification[]> {
  const results: ProductVerification[] = [];

  // Process in batches of MAX_PARALLEL_VERIFICATIONS
  for (let i = 0; i < products.length; i += MAX_PARALLEL_VERIFICATIONS) {
    const batch = products.slice(i, i + MAX_PARALLEL_VERIFICATIONS);

    console.log(
      `[Hybrid Executor] Verifying batch ${Math.floor(i / MAX_PARALLEL_VERIFICATIONS) + 1} (${batch.length} products)...`
    );

    const batchResults = await Promise.allSettled(
      batch.map((product) =>
        verifyProductWithPerplexity(product, brand, productLine, domainFilter)
      )
    );

    // Extract fulfilled results
    for (const result of batchResults) {
      if (result.status === 'fulfilled') {
        results.push(result.value);
      } else {
        console.error('[Hybrid Executor] Verification rejected:', result.reason);
        // Add placeholder for failed verification
        results.push({
          product: { name: 'Unknown', verified: false },
          exists: false,
          alternative: 'Verificación fallida',
          citations: [],
        });
      }
    }

    // Small delay between batches to avoid rate limits
    if (i + MAX_PARALLEL_VERIFICATIONS < products.length) {
      await new Promise((resolve) => setTimeout(resolve, 500));
    }
  }

  return results;
}

/**
 * Execute hybrid formula generation
 * 1. OpenAI generates formula (streaming)
 * 2. Extract products from formula
 * 3. Verify each product with Perplexity (parallel batches)
 * 4. Return formula + verification results
 *
 * @param openai - OpenAI client
 * @param supabase - Supabase client
 * @param params - Formula generation parameters
 * @returns Formula text + verification results + warnings + citations
 */
export async function executeHybridFormula(
  openai: OpenAI,
  supabase: SupabaseClient,
  params: {
    systemPrompt: string;
    userPrompt: string;
    brand: string;
    productLine?: string;
    domainFilter?: string[];
    stream?: boolean;
  }
): Promise<{
  formula: string;
  verifiedProducts: VerifiedProduct[];
  warnings: string[];
  citations: any[];
  verificationMetadata: {
    totalProducts: number;
    verifiedCount: number;
    unverifiedCount: number;
    verificationLatency: number;
  };
}> {
  const startTime = Date.now();

  console.log(`[Hybrid Executor] Generating formula for ${params.brand}${params.productLine ? ` ${params.productLine}` : ''}...`);

  // STEP 1: Generate formula with OpenAI (GPT-4o)
  const completion = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      { role: 'system', content: params.systemPrompt },
      { role: 'user', content: params.userPrompt },
    ],
    max_completion_tokens: 2304,
    temperature: 0.7, // Creative but consistent
    stream: false, // Non-streaming for hybrid mode (need full text to extract products)
  });

  const formulaText = completion.choices[0].message.content || '';
  console.log(`[Hybrid Executor] Formula generated (${formulaText.length} chars)`);

  if (!formulaText || formulaText.length < 100) {
    console.warn('[Hybrid Executor] Generated formula is too short, skipping verification');
    return {
      formula: formulaText,
      verifiedProducts: [],
      warnings: ['⚠️ Fórmula generada es demasiado corta, verifica el resultado.'],
      citations: [],
      verificationMetadata: {
        totalProducts: 0,
        verifiedCount: 0,
        unverifiedCount: 0,
        verificationLatency: 0,
      },
    };
  }

  // STEP 2: Extract products from formula
  const extractedProducts = await extractProductsFromFormula(openai, formulaText);

  if (extractedProducts.length === 0) {
    console.warn('[Hybrid Executor] No products found in formula');
    return {
      formula: formulaText,
      verifiedProducts: [],
      warnings: ['⚠️ No se detectaron productos específicos en la fórmula.'],
      citations: [],
      verificationMetadata: {
        totalProducts: 0,
        verifiedCount: 0,
        unverifiedCount: 0,
        verificationLatency: Date.now() - startTime,
      },
    };
  }

  console.log(`[Hybrid Executor] Extracted ${extractedProducts.length} products, verifying...`);

  // STEP 3: Verify products with Perplexity (batched)
  const verificationStartTime = Date.now();
  const verificationResults = await verifyProductsBatch(
    extractedProducts,
    params.brand,
    params.productLine,
    params.domainFilter
  );
  const verificationLatency = Date.now() - verificationStartTime;

  console.log(`[Hybrid Executor] Verification completed in ${verificationLatency}ms`);

  // STEP 4: Consolidate results
  const verifiedProducts: VerifiedProduct[] = [];
  const warnings: string[] = [];
  const allCitations: any[] = [];

  let verifiedCount = 0;
  let unverifiedCount = 0;

  for (let i = 0; i < verificationResults.length; i++) {
    const result = verificationResults[i];
    const originalProduct = extractedProducts[i];

    if (result.exists) {
      verifiedProducts.push({
        name: result.product.name,
        code: result.product.code,
        verified: true,
        source: result.product.source,
      });
      verifiedCount++;
    } else {
      verifiedProducts.push({
        name: originalProduct.name,
        code: originalProduct.code,
        verified: false,
        alternative: result.alternative,
      });
      unverifiedCount++;

      // Add warning for unverified product
      warnings.push(
        `⚠️ "${originalProduct.name}" no encontrado en catálogo oficial. ` +
          `Alternativa sugerida: ${result.alternative || 'Consulta con el fabricante'}`
      );
    }

    // Collect citations
    if (result.citations && result.citations.length > 0) {
      allCitations.push(...result.citations);
    }
  }

  // Remove duplicate citations
  const uniqueCitations = Array.from(
    new Map(allCitations.map((c) => [c.url || c.title, c])).values()
  );

  console.log(
    `[Hybrid Executor] Verification summary: ${verifiedCount} verified, ${unverifiedCount} unverified`
  );

  return {
    formula: formulaText,
    verifiedProducts,
    warnings,
    citations: uniqueCitations,
    verificationMetadata: {
      totalProducts: extractedProducts.length,
      verifiedCount,
      unverifiedCount,
      verificationLatency,
    },
  };
}
