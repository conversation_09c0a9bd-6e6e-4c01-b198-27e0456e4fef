/**
 * Cache Manager - 3-Level Intelligent Cache System
 * Location: supabase/functions/ai-proxy/cache-manager.ts
 *
 * CACHE LEVELS:
 * 1. Product Catalog (90 days TTL) - Brand product lists
 * 2. Mixing Ratios (180 days TTL) - Brand mixing instructions
 * 3. Formulas (30 days TTL) - Complete generated formulas
 */

import { SupabaseClient } from 'https://esm.sh/@supabase/supabase-js@2';

// Cache type definitions
export type CacheType = 'product_catalog' | 'mixing_ratios' | 'formulas';

// TTL configuration (in days)
const CACHE_TTL: Record<CacheType, number> = {
  product_catalog: 90,   // 90 days (products rarely change)
  mixing_ratios: 180,    // 180 days (ratios very stable)
  formulas: 30,          // 30 days (formulas can become outdated)
};

/**
 * Cache entry structure
 */
export interface CacheEntry<T = any> {
  cache_type: CacheType;
  cache_key: string;
  cache_value: T;
  brand?: string;
  product_line?: string;
  expires_at: string;
  created_at: string;
  updated_at?: string;
  hit_count: number;
  last_accessed_at?: string;
}

/**
 * Cache statistics
 */
export interface CacheStats {
  totalEntries: number;
  hitRate: number; // Percentage (0-100)
  entriesByType: Record<CacheType, number>;
  topHits: Array<{ cache_key: string; hit_count: number }>;
}

/**
 * Cache Manager Class
 */
export class CacheManager {
  private supabase: SupabaseClient;

  constructor(supabase: SupabaseClient) {
    this.supabase = supabase;
  }

  /**
   * Sanitize input string for cache key (prevent injection and collision)
   * Uses SHA-256 hash to ensure collision-resistant keys
   */
  private async sanitizeKeyComponent(input: string | undefined): Promise<string> {
    if (!input) return '';

    // Normalize input
    const normalized = input.trim().toLowerCase();

    // Use SHA-256 hash to prevent collisions
    // Example: "L'Oréal" and "L-Oreal" will have different hashes
    const encoder = new TextEncoder();
    const data = encoder.encode(normalized);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');

    // Use first 32 chars (128 bits) - collision probability: ~1 in 10^38
    return hashHex.substring(0, 32);
  }

  /**
   * Generate cache key from parameters (with collision protection)
   */
  private async generateCacheKey(
    type: CacheType,
    params: {
      brand?: string;
      productLine?: string;
      query?: string;
      hash?: string;
    }
  ): Promise<string> {
    // SECURITY: Sanitize all inputs to prevent key collision attacks
    const safeBrand = await this.sanitizeKeyComponent(params.brand);
    const safeProductLine = await this.sanitizeKeyComponent(params.productLine);
    const safeQuery = await this.sanitizeKeyComponent(params.query);
    const safeHash = await this.sanitizeKeyComponent(params.hash);

    // SECURITY: Validate required fields are present
    switch (type) {
      case 'product_catalog':
        if (!safeBrand) {
          throw new Error('Cache key generation failed: brand is required for product_catalog');
        }
        return `${safeBrand}${safeProductLine ? `:${safeProductLine}` : ''}`;

      case 'mixing_ratios':
        if (!safeBrand) {
          throw new Error('Cache key generation failed: brand is required for mixing_ratios');
        }
        return `${safeBrand}`;

      case 'formulas':
        // Use hash of formula parameters (brand + query + client data)
        if (!safeHash && !safeBrand) {
          throw new Error('Cache key generation failed: hash or brand required for formulas');
        }
        return safeHash || `${safeBrand}:${safeQuery}`;

      default:
        throw new Error(`Unknown cache type: ${type}`);
    }
  }

  /**
   * Calculate expiration date based on cache type
   */
  private calculateExpiresAt(type: CacheType): string {
    const ttlDays = CACHE_TTL[type];
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + ttlDays);
    return expiresAt.toISOString();
  }

  /**
   * Get cached value (returns null if not found or expired)
   */
  async get<T = any>(
    type: CacheType,
    params: {
      brand?: string;
      productLine?: string;
      query?: string;
      hash?: string;
    }
  ): Promise<T | null> {
    const cacheKey = await this.generateCacheKey(type, params);

    try {
      // Query cache with expiration check
      const { data, error } = await this.supabase
        .from('ai_cache')
        .select('*')
        .eq('cache_type', type)
        .eq('cache_key', cacheKey)
        .gt('expires_at', new Date().toISOString()) // Only non-expired
        .maybeSingle();

      if (error) {
        console.error('[Cache Manager] Read error:', error);
        return null;
      }

      if (!data) {
        console.log(`[Cache MISS] ${type}:${cacheKey}`);
        return null;
      }

      // Increment hit counter and update last accessed time
      await this.supabase
        .from('ai_cache')
        .update({
          hit_count: data.hit_count + 1,
          last_accessed_at: new Date().toISOString(),
        })
        .eq('cache_type', type)
        .eq('cache_key', cacheKey);

      console.log(`[Cache HIT] ${type}:${cacheKey} (hit_count: ${data.hit_count + 1})`);

      return data.cache_value as T;
    } catch (error: any) {
      console.error('[Cache Manager] Get failed:', error);
      return null; // Safe fallback
    }
  }

  /**
   * Set cached value (upsert)
   */
  async set<T = any>(
    type: CacheType,
    params: {
      brand?: string;
      productLine?: string;
      query?: string;
      hash?: string;
    },
    value: T
  ): Promise<void> {
    const cacheKey = await this.generateCacheKey(type, params);
    const expiresAt = this.calculateExpiresAt(type);

    try {
      const { error } = await this.supabase.from('ai_cache').upsert(
        {
          cache_type: type,
          cache_key: cacheKey,
          cache_value: value,
          brand: params.brand,
          product_line: params.productLine,
          expires_at: expiresAt,
          created_at: new Date().toISOString(),
          hit_count: 0,
          last_accessed_at: new Date().toISOString(),
        },
        {
          onConflict: 'cache_type,cache_key', // Update if exists
        }
      );

      if (error) {
        console.error('[Cache Manager] Write error:', error);
        return;
      }

      console.log(
        `[Cache SET] ${type}:${cacheKey} (expires: ${new Date(expiresAt).toLocaleDateString()})`
      );
    } catch (error: any) {
      console.error('[Cache Manager] Set failed:', error);
      // Non-critical error, continue execution
    }
  }

  /**
   * Invalidate cache entry (delete)
   */
  async invalidate(
    type: CacheType,
    params: {
      brand?: string;
      productLine?: string;
      query?: string;
      hash?: string;
    }
  ): Promise<void> {
    const cacheKey = await this.generateCacheKey(type, params);

    try {
      const { error } = await this.supabase
        .from('ai_cache')
        .delete()
        .eq('cache_type', type)
        .eq('cache_key', cacheKey);

      if (error) {
        console.error('[Cache Manager] Invalidate error:', error);
        return;
      }

      console.log(`[Cache INVALIDATE] ${type}:${cacheKey}`);
    } catch (error: any) {
      console.error('[Cache Manager] Invalidate failed:', error);
    }
  }

  /**
   * Invalidate all cache for a specific brand
   * Useful when brand catalog is updated
   */
  async invalidateBrand(brand: string): Promise<void> {
    try {
      const { error } = await this.supabase
        .from('ai_cache')
        .delete()
        .eq('brand', brand);

      if (error) {
        console.error('[Cache Manager] Invalidate brand error:', error);
        return;
      }

      console.log(`[Cache INVALIDATE BRAND] ${brand}`);
    } catch (error: any) {
      console.error('[Cache Manager] Invalidate brand failed:', error);
    }
  }

  /**
   * Get cache statistics (for monitoring)
   */
  async getStats(): Promise<CacheStats | null> {
    try {
      // Total entries
      const { count: totalEntries, error: countError } = await this.supabase
        .from('ai_cache')
        .select('*', { count: 'exact', head: true });

      if (countError) {
        console.error('[Cache Manager] Stats count error:', countError);
        return null;
      }

      // Entries by type
      const { data: typeData, error: typeError } = await this.supabase
        .from('ai_cache')
        .select('cache_type');

      if (typeError) {
        console.error('[Cache Manager] Stats type error:', typeError);
        return null;
      }

      const entriesByType: Record<CacheType, number> = {
        product_catalog: 0,
        mixing_ratios: 0,
        formulas: 0,
      };

      typeData?.forEach((entry: any) => {
        entriesByType[entry.cache_type as CacheType]++;
      });

      // Top hits (top 10)
      const { data: topHitsData, error: topHitsError } = await this.supabase
        .from('ai_cache')
        .select('cache_key, hit_count')
        .order('hit_count', { ascending: false })
        .limit(10);

      if (topHitsError) {
        console.error('[Cache Manager] Stats top hits error:', topHitsError);
        return null;
      }

      // Calculate hit rate (average hit count)
      const totalHits = topHitsData?.reduce((sum: number, entry: any) => sum + entry.hit_count, 0) || 0;
      const hitRate = totalEntries ? (totalHits / totalEntries) * 100 : 0;

      return {
        totalEntries: totalEntries || 0,
        hitRate: Math.round(hitRate * 100) / 100, // Round to 2 decimals
        entriesByType,
        topHits: topHitsData || [],
      };
    } catch (error: any) {
      console.error('[Cache Manager] Get stats failed:', error);
      return null;
    }
  }

  /**
   * Manual cleanup of expired cache (also runs via cron)
   */
  async cleanup(): Promise<number> {
    try {
      const { data, error } = await this.supabase.rpc('cleanup_expired_ai_cache');

      if (error) {
        console.error('[Cache Manager] Cleanup error:', error);
        return 0;
      }

      console.log(`[Cache CLEANUP] Removed ${data} expired entries`);
      return data || 0;
    } catch (error: any) {
      console.error('[Cache Manager] Cleanup failed:', error);
      return 0;
    }
  }
}

/**
 * Helper: Generate hash for formula cache key
 * Uses Web Crypto API (Deno compatible)
 */
export async function generateFormulaHash(params: {
  brand: string;
  productLine?: string;
  query: string;
  clientData?: any;
}): Promise<string> {
  const normalized = JSON.stringify({
    brand: params.brand.toLowerCase(),
    productLine: params.productLine?.toLowerCase() || '',
    query: params.query.toLowerCase().trim(),
    // Only include relevant client data (not sensitive info)
    clientHair: params.clientData?.current_color || '',
  });

  const encoder = new TextEncoder();
  const data = encoder.encode(normalized);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  const hashHex = hashArray.map((b) => b.toString(16).padStart(2, '0')).join('');

  return hashHex.substring(0, 32); // First 32 chars
}
