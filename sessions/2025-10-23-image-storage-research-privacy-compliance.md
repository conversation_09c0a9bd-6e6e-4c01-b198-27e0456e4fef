# Research: Image Storage Strategy for Global Salon App
## Privacy Compliance & Best Practices Analysis

**Fecha**: 2025-10-23
**Contexto**: Salonier AI - App global de asistencia AI para coloristas profesionales
**Alcance**: Almacenamiento de fotos sensibles de clientes con cumplimiento de normativas internacionales

---

## 📊 Resumen Ejecutivo

**Conclusión Principal**: Para una app global de salón que maneja fotos sensibles de clientes, la estrategia recomendada es:

1. **Storage en la nube con cifrado** (Supabase Storage o AWS S3)
2. **Política de retención temporal** (30-90 días para imágenes de análisis)
3. **Cifrado AES-256 en reposo y TLS 1.2+ en tránsito**
4. **Consentimiento explícito** con opt-in claro
5. **Residencia de datos en UE** (para cumplimiento GDPR)
6. **Auditoría y logs** de acceso a imágenes

---

## 🔍 Análisis de Aplicaciones Similares

### 1. Apps de Gestión de Salones (Boulevard, Fresha, Vagaro)

**Lo que hacen**:
- ✅ Almacenamiento de fotos before/after en perfiles de clientes
- ✅ Adjuntos en notas SOAP (historial de servicios)
- ✅ Tags y búsqueda de clientes por características
- ✅ Integración con calendario y servicios

**Estrategia de almacenamiento**:
- Almacenamiento en la nube (no especifican proveedor público)
- Cifrado de datos sensibles
- Backups automáticos
- GDPR compliance mencionado (pero sin detalles técnicos públicos)

**Gaps encontrados**:
- ❌ No publican políticas específicas de retención de fotos
- ❌ No mencionan cifrado end-to-end
- ❌ Falta transparencia sobre residencia de datos

---

### 2. Apps de Telemedicina (Referencia: Apps HIPAA-Compliant)

**Por qué son relevantes**: Manejan fotos clínicas similares (antes/después, diagnóstico visual)

**Estándares que siguen**:

#### Arquitectura de almacenamiento:
- **Cloud providers**: AWS (con HIPAA BAA) o Azure (HIPAA-compliant tiers)
- **Cifrado**:
  - En reposo: AES-256
  - En tránsito: TLS 1.2+
  - Claves gestionadas por el cliente (CMK) opcionales
- **Segregación**: Imágenes clínicas separadas de fotos personales en dispositivo
- **Backups**: Cifrados y con recuperación ante desastres

#### Controles de acceso:
- **Autenticación multifactor** (MFA) obligatoria para staff
- **Biometría** en apps móviles (Face ID, Touch ID)
- **RBAC** (Role-Based Access Control):
  - Médicos: acceso completo
  - Enfermeras: solo pacientes asignados
  - Admins: solo metadata
- **Automatic logoff**: Después de 5-15 min de inactividad
- **Break-glass accounts**: Para emergencias (con auditoría especial)

#### Auditoría y compliance:
- **Logs de acceso**: Quién, cuándo, qué imagen
- **Retention de logs**: 6+ años (requisito HIPAA)
- **Integrity controls**: Hashes para detectar alteraciones
- **Audit trails**: Exportables para inspecciones regulatorias

#### Retención de datos:
- **Imágenes de consulta temporal**: 30 días (luego se anonimiza o elimina)
- **Imágenes de registro médico**: 7 años (requisito legal)
- **Consentimiento explícito** para cada uso (diagnosis, research, etc.)

**Aplicabilidad a Salonier**:
- ✅ Arquitectura de cifrado (aplicable 100%)
- ✅ RBAC (owners vs estilistas vs recepcionistas)
- ⚠️ Retención ultra-larga (7 años) NO necesaria para salones (es overkill)
- ✅ MFA para cuentas profesionales (importante)

---

### 3. Apps de Chat con IA (ChatGPT, Gemini, Claude)

**Relevante porque**: Salonier combina chat AI + imágenes

**Estrategias que usan**:

#### Almacenamiento de imágenes:
- **Procesamiento temporal**: Imágenes se procesan en memoria y se descartan
- **Retención corta**: 30 días para conversaciones identificables
- **Anonimización**: Después de 30 días, se anonimiza para training (con opt-out)
- **No persistencia**: Las imágenes NO se guardan por defecto a largo plazo

#### Cifrado:
- **HTTPS en tránsito** (estándar)
- **Cifrado en servidores** (Azure AES-256 por defecto)
- **No end-to-end**: El proveedor SÍ puede ver las imágenes (necesario para AI)

#### Problemas de privacidad actuales:
- ⚠️ **Tensión con cifrado**: AI necesita acceso a datos en texto plano (plaintext)
- ⚠️ **Training data**: Riesgo de imágenes usadas para entrenar modelos
- ⚠️ **Third-party processors**: Imágenes procesadas por modelos de OpenAI/Anthropic

**Aplicabilidad a Salonier**:
- ✅ Retención temporal (30 días) es razonable para análisis de cabello
- ❌ Anonimización NO funciona para fotos de clientes (GDPR lo prohíbe sin consentimiento)
- ✅ Separar "imágenes de consulta" vs "imágenes de historial del cliente"

---

## 📜 Normativas Internacionales (2025)

### 1. **GDPR (Unión Europea)** 🇪🇺

**Aplica a**: Cualquier app que procese datos de ciudadanos UE (independientemente de dónde esté el servidor)

**Requisitos clave para fotos de clientes**:

#### Consentimiento (Art. 6 y 7):
- ✅ **Explícito y granular**: "Acepto que Salonier almacene mis fotos de cabello para análisis"
- ✅ **Revocable**: El cliente puede retirar consentimiento en cualquier momento
- ✅ **Separado**: No bundled con otros consentimientos (ej: términos de servicio)
- ✅ **Documentado**: Registrar cuándo/cómo se dio el consentimiento

#### Derechos del usuario:
- **Right to Access**: Ver todas sus fotos almacenadas
- **Right to Rectification**: Corregir datos asociados (ej: fecha incorrecta)
- **Right to Erasure** ("Right to be Forgotten"): Borrar todas las fotos a petición
- **Right to Data Portability**: Exportar fotos en formato estándar (JPEG)
- **Right to Restriction**: Limitar procesamiento sin borrar

#### Minimización de datos (Art. 5):
- Solo almacenar fotos **necesarias** para el servicio
- No pedir más fotos de las necesarias
- Eliminar automáticamente cuando ya no sean necesarias

#### Storage Limitation (Art. 5):
- Definir **periodo de retención claro** (ej: 90 días para análisis temporal)
- Documentar **justificación legal** para retención prolongada
- Eliminar o anonimizar después del periodo

#### Seguridad (Art. 32):
- **Cifrado**: "Estado del arte" (actualmente AES-256)
- **Pseudonimización**: Separar IDs de usuarios de archivos (opcional pero recomendado)
- **Controles de acceso**: Solo personal autorizado
- **Auditoría**: Logs de quién accede a qué foto

#### Data Residency:
- ⚠️ **Preferencia por UE**: Almacenar en servidores UE si es posible
- ⚠️ **Transferencias fuera UE**: Requiere Standard Contractual Clauses (SCC)
- ⚠️ **US-EU Data Privacy Framework**: Reemplaza Privacy Shield (invalidado en 2020)

#### Sanciones:
- **Hasta €20M o 4% de facturación global** (el que sea mayor)
- Multas reales recientes: €1.2B (Meta), €746M (Amazon)

---

### 2. **CCPA/CPRA (California, EE.UU.)** 🇺🇸

**Aplica a**: Apps que procesan datos de residentes de California con >$25M facturación o >100K usuarios

**Diferencias clave con GDPR**:
- ✅ **Opt-out** en lugar de opt-in (más laxo)
- ✅ **"Do Not Sell My Personal Information"**: Clientes pueden prohibir venta de datos
- ✅ **Right to Delete**: Similar a GDPR
- ✅ **Right to Know**: Ver qué categorías de datos se recopilan

**Relevancia para Salonier**:
- Si tienes usuarios en California, debes cumplir
- Menos estricto que GDPR (si cumples GDPR, cumples CCPA)

---

### 3. **AI Act (UE - 2025)** 🤖

**Aplica a**: Aplicaciones que usan IA en la UE

**Clasificación de riesgo**:
- **Salonier AI**: Probablemente **"Limited Risk"** o **"Minimal Risk"**
  - No es high-risk (no hace biometric surveillance, no manipulación)
  - SÍ procesa datos biométricos (fotos de rostro), pero no para identificación

**Requisitos**:
- ✅ **Transparencia**: Usuarios deben saber que están interactuando con IA
- ✅ **Explicabilidad**: Poder explicar cómo la IA llegó a una recomendación
- ✅ **Human oversight**: Estilista debe poder revisar/rechazar sugerencias AI
- ⚠️ **Prohibición de manipulación**: No usar IA para influir en decisiones de compra mediante técnicas subliminal

**Estado actual** (2025):
- Enforcement inicial: Prohibiciones de "unacceptable risk" AI
- Compliance completo: 2027

---

### 4. **Otras Normativas Relevantes**

#### India - Digital Personal Data Protection Act (DPDPA):
- Similar a GDPR
- Consentimiento explícito
- Right to erasure

#### Brasil - LGPD:
- Casi idéntico a GDPR
- Importante si expandes a Latinoamérica

#### UK - UK GDPR:
- Post-Brexit, casi idéntico a EU GDPR
- Pequeñas diferencias en transferencias internacionales

---

## 🏗️ Arquitecturas Técnicas Recomendadas

### **Opción 1: Supabase Storage (Recomendado para MVP/Early Stage)**

#### Arquitectura:
```
[App] --HTTPS--> [Supabase Edge Functions] ---> [Supabase Storage (S3)]
                         |                              |
                         v                              v
                  [PostgreSQL RLS]              [Encrypted at rest]
```

#### Pros:
- ✅ **Integración nativa**: Ya usas Supabase para DB
- ✅ **RLS integrado**: Políticas de acceso en SQL
- ✅ **Pricing simple**: $0.021/GB/mes
- ✅ **DPA disponible**: Supabase ofrece Data Processing Addendum para GDPR
- ✅ **Cifrado incluido**: AES-256 en reposo, TLS en tránsito
- ✅ **Backups automáticos**: Cifrados
- ✅ **EU region disponible**: `eu-west-1` (Irlanda), `eu-west-3` (París)
- ✅ **S3-compatible**: Puedes migrar a S3 directo si creces

#### Contras:
- ⚠️ **Costo a escala**: Más caro que S3 directo en volumes >1TB
- ⚠️ **Edge Functions**: Deno Deploy no garantiza EU region (pueden ejecutar en US)
- ⚠️ **Vendor lock-in**: Moderado (pero API S3-compatible mitiga)

#### Implementación:

**1. Crear bucket con políticas**:
```sql
-- Bucket para fotos de clientes (privado)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'client-photos',
  'client-photos',
  false, -- PRIVADO
  10485760, -- 10MB max por foto
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- RLS: Solo el estilista que creó el cliente puede ver sus fotos
CREATE POLICY "Stylists can upload client photos"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'client-photos'
  AND auth.uid()::text = (storage.foldername(name))[1]
  AND EXISTS (
    SELECT 1 FROM clients
    WHERE id::text = (storage.foldername(name))[2]
    AND created_by = auth.uid()
  )
);

CREATE POLICY "Stylists can view own client photos"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'client-photos'
  AND EXISTS (
    SELECT 1 FROM clients
    WHERE id::text = (storage.foldername(name))[2]
    AND created_by = auth.uid()
  )
);
```

**2. Upload desde app**:
```typescript
// lib/storage.ts
export async function uploadClientPhoto(
  clientId: string,
  imageUri: string
): Promise<string> {
  const userId = supabase.auth.user()?.id;
  const fileName = `${Date.now()}-${Math.random().toString(36)}`;
  const filePath = `${userId}/${clientId}/${fileName}.jpg`;

  // Convertir URI a blob
  const response = await fetch(imageUri);
  const blob = await response.blob();

  // Upload
  const { data, error } = await supabase.storage
    .from('client-photos')
    .upload(filePath, blob, {
      contentType: 'image/jpeg',
      cacheControl: '3600',
      upsert: false
    });

  if (error) throw error;

  // Retornar URL firmada (expires en 1 hora)
  const { data: signedUrl } = await supabase.storage
    .from('client-photos')
    .createSignedUrl(data.path, 3600);

  return signedUrl.signedUrl;
}
```

**3. Política de retención automática**:
```sql
-- Función para limpiar fotos antiguas
CREATE OR REPLACE FUNCTION cleanup_old_client_photos(days_old INT DEFAULT 90)
RETURNS INT AS $$
DECLARE
  deleted_count INT;
BEGIN
  -- Borrar archivos del storage (requiere llamar API desde Edge Function)
  -- Alternativa: Marcar en DB para cleanup manual

  DELETE FROM messages
  WHERE images IS NOT NULL
  AND created_at < now() - (days_old || ' days')::INTERVAL;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ejecutar con pg_cron (extension)
SELECT cron.schedule(
  'cleanup-old-photos',
  '0 2 * * *', -- 2 AM diario
  $$ SELECT cleanup_old_client_photos(90); $$
);
```

#### Costos estimados:

**Escenario 1: Startup (100 clientes/mes)**
- 100 clientes × 2 fotos promedio × 500KB = 100MB/mes
- Storage: 100MB × $0.021/GB = **$0.002/mes** (despreciable)
- Bandwidth: 100MB × $0.09/GB = **$0.009/mes**
- **Total: ~$0.01/mes**

**Escenario 2: Growth (1,000 clientes/mes)**
- 1000 × 2 × 500KB = 1GB/mes (nuevo), 12GB/año acumulado
- Storage: 12GB × $0.021 = **$0.25/mes**
- Bandwidth: 1GB × $0.09 = **$0.09/mes**
- **Total: ~$0.34/mes**

**Escenario 3: Scale (10,000 clientes/mes)**
- Con retención de 90 días: ~30GB storage
- Storage: 30GB × $0.021 = **$0.63/mes**
- Bandwidth: 10GB × $0.09 = **$0.90/mes**
- **Total: ~$1.53/mes** ✅ Muy barato

---

### **Opción 2: AWS S3 Direct (Para Enterprise/Scale)**

#### Pros:
- ✅ **Más barato a escala**: $0.023/GB/mes (vs $0.021 Supabase) pero sin markup
- ✅ **Más control**: Configuración granular de lifecycle, versioning, etc.
- ✅ **Compliance certificado**: SOC 2, ISO 27001, HIPAA BAA disponible
- ✅ **Multi-region replication**: Backups automáticos en múltiples regiones
- ✅ **S3 Intelligent-Tiering**: Ahorro automático moviendo a Glacier

#### Contras:
- ❌ **Complejidad**: Necesitas configurar IAM, presigned URLs, CORS, etc.
- ❌ **No integrado con Supabase**: Debes gestionar permisos tú
- ❌ **Más código**: Custom upload/download logic

#### Cuándo usar:
- Más de 1TB de imágenes
- Necesitas compliance específico (HIPAA)
- Equipo con experiencia en AWS

---

## 📋 Estrategia de Retención Recomendada

### **Para Salonier AI:**

#### 1. **Imágenes de Chat (Análisis Temporal)**
**Tipo**: Fotos enviadas al chat para análisis inmediato ("analiza este cabello")

**Retención**: **30 días**

**Justificación**:
- Cumple GDPR (data minimization)
- Suficiente para que el estilista revise/exporte
- Similar a apps de telemedicina

**Flujo**:
```
Upload → Análisis AI → Guardar en chat 30 días → Auto-delete
         (inmediato)    (con mensaje)              (cron job)
```

**Implementación**:
```typescript
// En ChatContext.tsx - addMessage()
const userMessage: Message = {
  images: uploadedUrls,
  expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 días
};
```

---

#### 2. **Imágenes de Perfil de Cliente (Historial)**
**Tipo**: Fotos before/after guardadas en el perfil del cliente

**Retención**: **Hasta que el cliente lo revoque O 2 años de inactividad**

**Justificación**:
- Valor comercial: Portfolio del estilista
- Evidencia de transformaciones exitosas
- 2 años es razonable para clientes inactivos (GDPR permite retención por "legitimate interest")

**Flujo**:
```
Upload → Guardar en perfil cliente → Mantener mientras:
                                     - Cliente activo (visitó <2 años)
                                     - Consentimiento vigente
                                     → Auto-delete si pasa 2 años sin visita
```

**Implementación**:
```sql
-- Tabla de fotos de cliente
CREATE TABLE client_photos (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  client_id UUID REFERENCES clients(id) ON DELETE CASCADE,
  storage_path TEXT NOT NULL,
  uploaded_at TIMESTAMPTZ DEFAULT now(),
  consent_given_at TIMESTAMPTZ NOT NULL,
  consent_revoked_at TIMESTAMPTZ,
  photo_type TEXT CHECK (photo_type IN ('before', 'after', 'inspiration')),
  notes TEXT,
  CONSTRAINT valid_consent CHECK (
    consent_revoked_at IS NULL OR consent_revoked_at > consent_given_at
  )
);

-- Auto-delete si cliente inactivo 2+ años
CREATE OR REPLACE FUNCTION cleanup_inactive_client_photos()
RETURNS INT AS $$
DECLARE
  deleted_count INT;
BEGIN
  -- Clientes sin visita en 2+ años
  DELETE FROM client_photos
  WHERE client_id IN (
    SELECT id FROM clients
    WHERE last_visit < now() - INTERVAL '2 years'
    OR last_visit IS NULL AND created_at < now() - INTERVAL '2 years'
  )
  AND consent_revoked_at IS NULL; -- Respetar revocación explícita

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;
```

---

#### 3. **Imágenes de Fórmulas**
**Tipo**: Fotos adjuntas a fórmulas de coloración específicas

**Retención**: **Vinculado a la fórmula (típicamente 1 año)**

**Justificación**:
- Fórmulas son documentos de trabajo
- Útil para repetir color exacto
- 1 año es suficiente para consultar fórmulas previas

**Flujo**:
```
Crear fórmula → Adjuntar fotos → Mantener 1 año → Auto-archive/delete
```

---

## 🔐 Arquitectura de Seguridad Recomendada

### **Capas de Seguridad**

```
┌─────────────────────────────────────────────────┐
│ Capa 1: Autenticación (MFA para estilistas)    │
├─────────────────────────────────────────────────┤
│ Capa 2: Autorización (RLS en Supabase)         │
├─────────────────────────────────────────────────┤
│ Capa 3: Cifrado en Tránsito (TLS 1.2+)         │
├─────────────────────────────────────────────────┤
│ Capa 4: Cifrado en Reposo (AES-256)            │
├─────────────────────────────────────────────────┤
│ Capa 5: Auditoría (Logs de acceso)             │
├─────────────────────────────────────────────────┤
│ Capa 6: Backup Cifrado (Point-in-time recovery)│
└─────────────────────────────────────────────────┘
```

### **Implementación**

#### 1. **Cifrado en Tránsito**
```typescript
// Ya implementado con Supabase (HTTPS obligatorio)
// Verificar en app.json:
{
  "expo": {
    "android": {
      "usesCleartextTraffic": false // ← IMPORTANTE
    }
  }
}
```

#### 2. **Signed URLs con Expiración**
```typescript
// NO guardar URLs públicas permanentes
// Generar signed URLs on-demand
async function getClientPhotoUrl(photoId: string): Promise<string> {
  const { data } = await supabase.storage
    .from('client-photos')
    .createSignedUrl(photoPath, 3600); // 1 hora

  return data.signedUrl;
}
```

#### 3. **Auditoría de Accesos**
```sql
-- Tabla de audit logs
CREATE TABLE photo_access_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id),
  photo_id UUID REFERENCES client_photos(id),
  action TEXT CHECK (action IN ('view', 'download', 'upload', 'delete')),
  ip_address INET,
  user_agent TEXT,
  accessed_at TIMESTAMPTZ DEFAULT now()
);

-- Trigger para logging automático
CREATE OR REPLACE FUNCTION log_photo_access()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO photo_access_logs (user_id, photo_id, action)
  VALUES (auth.uid(), NEW.id, TG_ARGV[0]);
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

CREATE TRIGGER log_photo_view
AFTER SELECT ON client_photos
FOR EACH ROW
EXECUTE FUNCTION log_photo_access('view');
```

---

## ✅ Checklist de Compliance GDPR/CCPA

### **Legal**
- [ ] Crear **Privacy Policy** específica para fotos
- [ ] Añadir sección "How We Handle Your Photos" en Terms
- [ ] Preparar **Data Processing Agreement (DPA)** con Supabase
- [ ] Designar **Data Protection Officer** (DPO) si procesáis >250 personas (GDPR Art. 37)
- [ ] Documentar **Legitimate Interest Assessment** para retención de 2 años

### **Técnico**
- [ ] Implementar **upload a Supabase Storage** (en lugar de blob URLs)
- [ ] Configurar **bucket privado** con RLS policies
- [ ] Habilitar **cifrado en reposo** (AES-256, ya incluido en Supabase)
- [ ] Implementar **signed URLs** con expiración corta (1-4 horas)
- [ ] Crear **cron job** para auto-delete después de periodo de retención
- [ ] Implementar **audit logs** de acceso a fotos
- [ ] Configurar **EU region** en Supabase (`eu-west-1` o `eu-west-3`)

### **UX/UI**
- [ ] Añadir **consent checkbox** antes de upload:
  ```
  ☐ Acepto que Salonier almacene mis fotos de cabello de forma segura
    por hasta [30 días / 2 años] para [análisis AI / historial de servicios].
    Puedo revocar este consentimiento en cualquier momento desde Ajustes.
  ```
- [ ] Crear pantalla **"Mis Fotos"** en settings del cliente:
  - Ver todas las fotos almacenadas
  - Botón "Descargar todas" (ZIP)
  - Botón "Eliminar todas"
- [ ] Implementar **"Revocar Consentimiento"** en settings
- [ ] Añadir banner de **"Fotos se eliminarán en X días"** en chat

### **Documentación**
- [ ] Crear **Data Retention Policy** documento interno
- [ ] Documentar **Incident Response Plan** (qué hacer si hay breach)
- [ ] Preparar **Data Subject Access Request (DSAR) workflow**:
  - Proceso para exportar todas las fotos de un cliente
  - Plazo de respuesta: 30 días (GDPR)

### **Monitoring**
- [ ] Configurar alertas de **accesos inusuales** (ej: 100+ fotos descargadas en 1 hora)
- [ ] Dashboard de **métricas de retention**:
  - Cuántas fotos se auto-eliminan cada día
  - Cuántos usuarios revocan consentimiento
- [ ] Revisar **audit logs** mensualmente

---

## 💰 Costos de Implementación

### **Desarrollo** (Estimación)
| Tarea | Tiempo | Complejidad |
|-------|--------|-------------|
| Setup Supabase Storage bucket + RLS | 2h | Baja |
| Implementar upload/download con signed URLs | 4h | Media |
| Migrar chat para usar Storage (en lugar de blob) | 3h | Media |
| Crear UI "Mis Fotos" en settings | 4h | Baja |
| Implementar auto-delete cron job | 2h | Baja |
| Audit logs + dashboard | 4h | Media |
| Testing + QA | 4h | Media |
| **TOTAL** | **23h** | **~3 días** |

### **Operacional** (Mensual)
| Concepto | Startup (100 clientes) | Growth (1K) | Scale (10K) |
|----------|------------------------|-------------|-------------|
| Supabase Storage | $0.01 | $0.34 | $1.53 |
| Backup (incluido) | $0 | $0 | $0 |
| DPA/Legal (one-time) | $500 | $500 | $500 |
| DPO (si >250 usuarios) | N/A | N/A | $1,000/mes |
| **Total mes 1** | **$500** | **$500** | **$500** |
| **Total mes 2+** | **$0.01** | **$0.34** | **$1,001.53** |

**Nota**: DPO solo es obligatorio si procesáis datos de >250 personas de forma regular (GDPR Art. 37). Para startups, puedes ser tu propio DPO hasta escalar.

---

## 🎯 Mi Recomendación Final

### **Implementar AHORA (Fase 1 - MVP):**

1. **Supabase Storage con bucket privado**
   - Cifrado AES-256 incluido
   - RLS policies para acceso
   - EU region (`eu-west-3` París)

2. **Retención de 30 días para chat**
   - Auto-delete con cron job
   - Suficiente para análisis temporal

3. **Consent explícito**
   - Checkbox antes de upload
   - Texto claro y simple

4. **Privacy Policy actualizada**
   - Sección específica para fotos
   - Lenguaje accesible

**Resultado**: GDPR-compliant básico, costo ~$1/mes

---

### **Implementar en 3-6 meses (Fase 2 - Growth):**

1. **Historial de fotos en perfil cliente**
   - Retención 2 años con cleanup automático
   - UI "Mis Fotos" con download/delete

2. **Audit logs**
   - Quién accede a qué foto
   - Dashboard de métricas

3. **DSAR workflow**
   - Proceso automatizado para exportar datos

**Resultado**: GDPR-compliant completo, listo para escalar

---

### **Considerar en 12+ meses (Fase 3 - Enterprise):**

1. **Multi-region storage**
   - US, EU, APAC buckets separados
   - Cumplir data residency local

2. **End-to-end encryption**
   - Claves gestionadas por el cliente
   - Supabase NO puede ver fotos

3. **SOC 2 Type II certification**
   - Auditoría externa anual
   - Requerido para grandes salones enterprise

**Resultado**: Enterprise-ready, cumple normativas más estrictas

---

## 📌 Conclusión

**Para Salonier AI**, la estrategia óptima es:

✅ **Corto plazo**: Supabase Storage + retención 30 días + consent explícito
✅ **Medio plazo**: Historial cliente con retención 2 años + audit logs
✅ **Largo plazo**: Multi-region + E2E encryption si escalan a enterprise

**Costos**:
- MVP: ~$500 one-time + $1/mes
- Growth: ~$2-5/mes (hasta 10K clientes)
- Enterprise: ~$1K/mes (DPO + compliance)

**Compliance**:
- ✅ GDPR (UE)
- ✅ CCPA (California)
- ✅ AI Act (UE, limited risk)
- ✅ DPDPA (India)

**Beneficios**:
- Protección legal ante demandas
- Trust de clientes (diferenciador competitivo)
- Escalabilidad internacional
- Preparado para auditorías

---

## 📚 Referencias

- [GDPR Official Text](https://gdpr-info.eu/)
- [Supabase Storage Docs](https://supabase.com/docs/guides/storage)
- [HIPAA Compliance for Telemedicine](https://www.hipaajournal.com/hipaa-guidelines-on-telemedicine/)
- [EU AI Act Summary](https://artificialintelligenceact.eu/)
- [CCPA/CPRA Guide](https://oag.ca.gov/privacy/ccpa)
