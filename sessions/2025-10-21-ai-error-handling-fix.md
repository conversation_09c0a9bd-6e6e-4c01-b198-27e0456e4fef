# Fix: <PERSON><PERSON><PERSON> de Errores en Llamadas a generateText (Rork SDK)

**Última actualización**: 2025-10-21 19:55

## Contexto

Los usuarios estaban experimentando errores críticos al intentar usar las funcionalidades de IA en la app:

**Errores reportados:**
```
generateText error: SyntaxError: Unexpected token 'I', "Internal S"... is not valid JSON
```

```
Error al obtener respuesta de IA: Error: El servicio de IA no está disponible en este momento.
```

El problema ocurría porque:
1. El servicio backend de Rork ocasionalmente devuelve errores 500 en formato HTML/texto plano
2. El SDK de Rork (`@rork/toolkit-sdk`) intenta parsear todas las respuestas como JSON
3. Cuando recibe "Internal Server Error" en HTML, falla con `SyntaxError: Unexpected token 'I'...`
4. El error se lanzaba ANTES de que el código pudiera manejarlo apropiadamente

## Cambios Realizados

### Archivos Creados

- `lib/ai-client.ts` - **Nuevo módulo central para manejo de IA**
  - `generateTextSafe()` - Wrapper robusto con reintentos automáticos
  - `AIServiceError` - Clase de error personalizada con info de retry
  - `getErrorMessage()` - Helper para mensajes amigables al usuario

### Archivos Modificados

1. **`app/(tabs)/chat.tsx`**
   - ✅ Reemplazado `generateText` por `generateTextSafe`
   - ✅ Simplificado manejo de errores usando `getErrorMessage()`
   - ✅ Eliminadas validaciones redundantes (ahora en `ai-client.ts`)
   - **Líneas:** 23 (import), 134-144 (llamada con imágenes), 154-161 (llamada texto), 173-175 (catch)

2. **`app/formula/step1.tsx`** - Análisis de color actual
   - ✅ Reemplazado `generateText` por `generateTextSafe`
   - ✅ Simplificado catch block
   - **Líneas:** 23 (import), 219-232 (llamada), 242-246 (catch)

3. **`app/formula/step2.tsx`** - Análisis de color deseado
   - ✅ Reemplazado `generateText` por `generateTextSafe`
   - ✅ Simplificado catch block
   - **Líneas:** 22 (import), 232-245 (llamada), 255-259 (catch)

4. **`app/formula/step5.tsx`** - Generación de fórmula + chat
   - ✅ Reemplazadas **2 llamadas** a `generateText`
   - ✅ Simplificados **2 catch blocks**
   - **Primera llamada (generación fórmula):** líneas 187-194, 205-208
   - **Segunda llamada (chat refinamiento):** líneas 250-257, 271-283

## Solución Técnica Detallada

### 1. Wrapper `generateTextSafe()` con Reintentos

```typescript
export async function generateTextSafe(options: GenerateTextOptions): Promise<string> {
  const { messages, maxRetries = 2, retryDelay = 1500 } = options;

  // Loop de reintentos con backoff exponencial
  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      const response = await rorkGenerateText({ messages });

      // Validaciones de respuesta
      // ...

      return response;
    } catch (error) {
      // Detectar errores retriables:
      // - SyntaxError (JSON parsing)
      // - Network errors
      // - Server errors 5xx

      if (shouldRetry && attempt < maxRetries) {
        // Backoff exponencial: 1500ms * (attempt + 1)
        await delay(retryDelay * (attempt + 1));
        continue;
      }

      throw new AIServiceError(/* mensaje apropiado */);
    }
  }
}
```

### 2. Clase de Error Personalizada

```typescript
export class AIServiceError extends Error {
  constructor(
    message: string,
    public readonly originalError?: unknown,
    public readonly isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}
```

Esto permite:
- Distinguir errores de IA de otros errores
- Saber si el error es retriable
- Preservar el error original para debugging

### 3. Detección Inteligente de Errores

El wrapper detecta y clasifica:

**Errores de Parsing JSON:**
- `SyntaxError`
- `error.message.includes('JSON')`
- `error.message.includes('Unexpected token')`

**Errores de Red:**
- `error.message.includes('Network')`
- `error.message.includes('fetch')`
- `error.message.includes('timeout')`
- `ECONNREFUSED`, `ETIMEDOUT`

**Errores de Servidor:**
- Respuesta contiene: "Internal Server Error", "500", "502", "503", "504"
- "Service Unavailable", "Gateway Timeout", "Bad Gateway"

**Respuestas Inválidas:**
- Respuesta vacía o no-string
- Respuesta demasiado corta (< 5 caracteres)

### 4. Reintentos con Backoff Exponencial

- **Intento 1:** Inmediato
- **Intento 2:** Espera 1500ms (1.5s)
- **Intento 3:** Espera 3000ms (3s)

Esto evita saturar un servidor que ya está sobrecargado.

## Problemas Resueltos

### ✅ Error "Unexpected token 'I'"
**Antes:**
```
generateText() → server devuelve "Internal Server Error" HTML
→ SDK intenta JSON.parse() → SyntaxError → crash
```

**Ahora:**
```
generateTextSafe() → catch SyntaxError → reintenta (max 2 veces)
→ si persiste → AIServiceError con mensaje claro
```

### ✅ Mensajes de Error Confusos
**Antes:** "Lo siento, hubo un error al procesar tu mensaje"

**Ahora:**
- Error JSON: "El servicio de IA devolvió una respuesta con formato incorrecto. Esto generalmente ocurre cuando el servidor está sobrecargado..."
- Error red: "Error de conexión con el servicio de IA. Verifica tu conexión a internet..."
- Error servidor: "El servicio de IA no está disponible en este momento. Esto suele ser temporal, por favor intenta en unos minutos."

### ✅ Sin Reintentos Automáticos
**Antes:** Un error temporal causaba fallo inmediato

**Ahora:** Hasta 3 intentos con delays incrementales

## Mejoras de UX

1. **Reintentos transparentes** - El usuario ni siquiera nota si el primer intento falla
2. **Mensajes específicos** - El usuario entiende QUÉ falló y QUÉ hacer
3. **Menos frustración** - Errores temporales se resuelven automáticamente
4. **Debugging mejorado** - Los logs muestran todos los intentos y errores originales

## Testing Manual Sugerido

### Caso 1: Servidor Sobrecargado
1. Abrir app en Rork
2. Ir a Chat y enviar mensaje con imagen
3. **Esperado:**
   - Si Rork está sobrecargado, verás intentos de reintento en logs
   - Mensaje exitoso después de reintentos, O mensaje de error claro

### Caso 2: Sin Conexión
1. Desconectar WiFi/datos
2. Enviar mensaje en chat
3. **Esperado:** "Error de conexión con el servicio de IA. Verifica tu conexión..."

### Caso 3: Generación de Fórmula
1. Ir a workflow de fórmula (step0 → step4)
2. Llegar a step5 (generación)
3. **Esperado:**
   - Fórmula generada exitosamente, O
   - Error claro si el servicio está caído

## Notas Técnicas

### SDK de Rork
- El SDK `@rork/toolkit-sdk` NO está en package.json
- Se inyecta dinámicamente al correr `bunx rork start`
- Por eso el error era difícil de debuggear localmente

### Configuración de Reintentos
Los valores actuales:
```typescript
maxRetries: 2,      // Total 3 intentos
retryDelay: 1500,   // 1.5s base, luego 3s
```

Pueden ajustarse por llamada si se necesita:
```typescript
await generateTextSafe({
  messages: [...],
  maxRetries: 3,      // 4 intentos totales
  retryDelay: 2000,   // 2s, 4s, 6s
});
```

### Impacto en Performance
- **Sin errores:** 0ms overhead (solo un wrapper)
- **Con errores retriables:** +1.5s a +4.5s (pero evita fallo total)
- **Con errores no-retriables:** Falla rápido (sin reintentos innecesarios)

## TODOs / Trabajo Pendiente

- [ ] **Monitorear logs** en producción para ver frecuencia real de errores
- [ ] **Considerar telemetría** - registrar cuántos reintentos se necesitan típicamente
- [ ] **Circuit breaker** (opcional) - si el servicio está completamente caído, evitar reintentos por X minutos
- [ ] **Caché de respuestas** (futuro) - para consultas idénticas, devolver caché si servidor falla
- [ ] **Testing E2E** - agregar tests cuando se configure suite de tests

## Notas para Futuras Sesiones

### Si ves errores de IA:
1. Primero revisa logs para ver el `originalError` en `AIServiceError`
2. Verifica si los reintentos están ocurriendo (`console.warn` los muestra)
3. Si es un nuevo tipo de error, agrégalo a la detección en `ai-client.ts`

### Si necesitas modificar comportamiento de reintentos:
- Todo está centralizado en `lib/ai-client.ts`
- Cambia `maxRetries` o `retryDelay` en la función o por llamada
- Considera diferentes estrategias (ej: exponential backoff, jitter)

### Si Rork actualiza su SDK:
- Verifica que `generateText()` siga existiendo con la misma firma
- Si cambia, actualiza el import/llamada en `ai-client.ts` línea 4
- Los wrappers deberían seguir funcionando sin cambios

## Referencias

- **Commit anterior:** 43274d5 "Fix: Mejorar manejo de errores en llamadas a generateText"
  - Nota: Ese commit intentó solucionar validando DESPUÉS de la llamada, pero el error ocurría ANTES
- **Issue relacionado:** Errores reportados por usuario en Rork
- **Docs Rork SDK:** (no públicas, SDK inyectado)
