# Configuración de Credenciales y Acceso a Supabase

**Fecha**: 2025-10-21 19:15
**Propósito**: Documentar credenciales y métodos para interactuar con Supabase

---

## 🔑 Credenciales Disponibles

Todas las credenciales están almacenadas en `.env.local` (archivo protegido, NO se commitea a git).

### 1. EXPO_PUBLIC_SUPABASE_URL
```
https://guyxczavhtemwlrknqpm.supabase.co
```
- **Uso**: URL base del proyecto Supabase
- **Seguridad**: Pública, puede exponerse en cliente
- **Para qué sirve**: Todas las llamadas a la API REST de Supabase

### 2. EXPO_PUBLIC_SUPABASE_ANON_KEY
```
Ver .env.local → EXPO_PUBLIC_SUPABASE_ANON_KEY
```
- **Uso**: Cliente JavaScript en el frontend
- **Seguridad**: Pública, puede exponerse en cliente
- **Para qué sirve**: Operaciones CRUD desde la aplicación
- **Limitaciones**: Solo puede hacer lo que permiten las RLS policies

### 3. SUPABASE_SERVICE_ROLE_KEY
```
Ver .env.local → SUPABASE_SERVICE_ROLE_KEY
```
- **Uso**: Operaciones administrativas en servidor
- **Seguridad**: ⚠️ SECRETA - NUNCA exponer en cliente
- **Para qué sirve**:
  - Bypass de RLS policies
  - Operaciones administrativas via API REST
  - Scripts de servidor, migrations helpers
- **NO sirve para**: Conexión directa a PostgreSQL

### 4. SUPABASE_DB_PASSWORD
```
Ver .env.local → SUPABASE_DB_PASSWORD
```
- **Uso**: Conexión directa a PostgreSQL
- **Seguridad**: ⚠️ SECRETA - NUNCA exponer
- **Para qué sirve**:
  - Ejecutar migraciones SQL
  - Conexión directa con psql
  - Operaciones DDL (CREATE TABLE, ALTER, etc.)
- **Ubicación original**: Dashboard → Settings → Database

---

## 📁 Archivo de Credenciales

**Ubicación**: `/Users/<USER>/Salonier-AI/.env.local`

**Protección**:
- ✅ Incluido en `.gitignore`
- ✅ NO se commitea nunca
- ✅ Solo existe en máquina local

**Contenido** (ver archivo real para valores):
```bash
# Supabase Environment Variables
EXPO_PUBLIC_SUPABASE_URL=https://guyxczavhtemwlrknqpm.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJ...
SUPABASE_SERVICE_ROLE_KEY=eyJ...
SUPABASE_DB_PASSWORD=3xiro95Ankwh0DpU
```

---

## 🛠️ Cómo Ejecutar Migraciones SQL

### Método 1: Supabase CLI (RECOMENDADO)

Este es el método que usamos exitosamente.

```bash
# 1. Asegúrate de estar en el directorio del proyecto
cd /Users/<USER>/Salonier-AI

# 2. Crear archivo de migración en supabase/migrations/
# Formato: YYYYMMDDHHMMSS_descripcion.sql

# 3. Ejecutar migración
echo "CONTRASEÑA_AQUI" | supabase db push --password ""

# O de forma interactiva:
supabase db push
# Te pedirá la contraseña: usa SUPABASE_DB_PASSWORD
```

**Ventajas**:
- ✅ Método oficial de Supabase
- ✅ Mantiene historial de migraciones
- ✅ Detecta cambios pendientes
- ✅ Aplica migraciones en orden

**Ejemplo real que funcionó**:
```bash
echo "3xiro95Ankwh0DpU" | supabase db push --password ""
```

### Método 2: Docker + psql

Para conexión directa a PostgreSQL.

```bash
# Connection string format:
# postgresql://postgres.PROJECT_REF:PASSWORD@HOST:PORT/postgres

# Ejemplo (NO USAR - host incorrecto):
docker run --rm -i postgres:15 psql \
  "postgresql://postgres.guyxczavhtemwlrknqpm:<EMAIL>:6543/postgres" \
  < migration.sql
```

**⚠️ IMPORTANTE**:
- El pooler (`aws-0-eu-west-3.pooler.supabase.com`) NO funcionó
- Necesitas el host directo desde el Dashboard
- Preferir Método 1 (supabase CLI)

### Método 3: SQL Editor en Dashboard (Manual)

Para ejecución manual o cuando no tienes acceso al CLI.

1. Abrir: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
2. Pegar el SQL
3. Click "RUN"

**Cuándo usar**:
- Queries SQL ad-hoc
- Debugging
- Verificación rápida
- No tienes acceso a terminal

---

## 📊 Información del Proyecto Supabase

**Project Reference**: `guyxczavhtemwlrknqpm`
**Región**: EU West (Paris) - `eu-west-3`
**Database**: PostgreSQL 15

### URLs Importantes

**Dashboard**:
```
https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm
```

**Table Editor**:
```
https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/editor
```

**SQL Editor**:
```
https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
```

**Database Settings**:
```
https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/database
```

**API Settings**:
```
https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/api
```

---

## 🗄️ Tablas Creadas

### Tabla: `clients`
- Creada previamente
- Almacena información de clientes del salón
- Migración: `20251021170522_create_clients_table.sql`

### Tabla: `conversations`
- Creada: 2025-10-21
- Almacena conversaciones del chat
- Migración: `20251021180000_create_conversations_tables.sql`
- Campos: id, title, is_pinned, created_at, updated_at

### Tabla: `messages`
- Creada: 2025-10-21
- Almacena mensajes de las conversaciones
- Migración: `20251021180000_create_conversations_tables.sql`
- Campos: id, conversation_id (FK), role, content, images, timestamp
- Cascade delete: eliminar conversación elimina sus mensajes

---

## 🔍 Cómo Verificar Conexión

### Desde JavaScript (usando anon key):

```javascript
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.EXPO_PUBLIC_SUPABASE_URL,
  process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY
);

// Test: leer tablas
const { data, error } = await supabase.from('conversations').select('*').limit(1);
console.log('Conexión OK:', !error);
```

### Desde CLI:

```bash
# Verificar proyecto linkeado
supabase status

# Verificar tablas remotas
supabase db pull
```

### Desde SQL Editor:

```sql
-- Verificar tablas existentes
SELECT table_name
FROM information_schema.tables
WHERE table_schema = 'public';

-- Verificar datos
SELECT COUNT(*) FROM conversations;
SELECT COUNT(*) FROM messages;
SELECT COUNT(*) FROM clients;
```

---

## 🚨 Troubleshooting

### Error: "Tenant or user not found"
**Causa**: Connection string incorrecta o host del pooler incorrecto
**Solución**: Usar `supabase db push` en lugar de conexión directa

### Error: "password authentication failed"
**Causa**: Contraseña incorrecta
**Solución**:
1. Ve a Dashboard → Settings → Database
2. Reset database password si es necesario
3. Actualiza `.env.local` con la nueva contraseña

### Error: "Cannot find module '@supabase/supabase-js'"
**Causa**: Ejecutando script fuera del directorio del proyecto
**Solución**: `cd /Users/<USER>/Salonier-AI` primero

### Error: "Could not find the table 'public.rpc'"
**Causa**: Intentando ejecutar SQL via API REST (no soportado)
**Solución**: Usar `supabase db push` o SQL Editor

---

## 📝 Workflow Recomendado para Futuras Migraciones

### 1. Crear archivo de migración

```bash
cd /Users/<USER>/Salonier-AI

# Crear archivo (manualmente o con script)
# Formato: supabase/migrations/YYYYMMDDHHMMSS_descripcion.sql

cat > supabase/migrations/$(date +%Y%m%d%H%M%S)_add_users_table.sql << 'EOF'
CREATE TABLE IF NOT EXISTS users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email TEXT UNIQUE NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW()
);
EOF
```

### 2. Ejecutar migración

```bash
# Opción A: Con contraseña desde .env.local
source .env.local
echo "$SUPABASE_DB_PASSWORD" | supabase db push --password ""

# Opción B: Interactivo
supabase db push
# Pegar contraseña cuando la pida
```

### 3. Verificar en Dashboard

```bash
# Abrir Table Editor
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/editor"
```

### 4. Commitear migración (NO las credenciales!)

```bash
git add supabase/migrations/YYYYMMDDHHMMSS_add_users_table.sql
git commit -m "Add users table migration"
git push
```

---

## ⚠️ Seguridad - MUY IMPORTANTE

### ✅ NUNCA Commitear:
- `.env.local`
- Contraseñas en texto plano
- Service role keys
- Database passwords

### ✅ Verificar antes de commit:
```bash
# Ver qué se va a commitear
git status

# Verificar que .env.local NO aparece
git status | grep env

# Si aparece, agregarlo a .gitignore inmediatamente
```

### ✅ Si accidentalmente commiteaste credenciales:

1. **INMEDIATAMENTE** resetear las credenciales en Supabase Dashboard
2. Eliminar el commit del historial:
   ```bash
   git filter-branch --force --index-filter \
     "git rm --cached --ignore-unmatch .env.local" \
     --prune-empty --tag-name-filter cat -- --all
   ```
3. Force push (solo si es tu repo personal):
   ```bash
   git push origin --force --all
   ```

---

## 📚 Referencias

**Documentación Oficial**:
- Supabase CLI: https://supabase.com/docs/guides/cli
- Database Migrations: https://supabase.com/docs/guides/cli/local-development#database-migrations
- Connection Strings: https://supabase.com/docs/guides/database/connecting-to-postgres

**Archivos del Proyecto**:
- Credenciales: `.env.local`
- Migraciones: `supabase/migrations/`
- Configuración: `supabase/config.toml`
- Cliente JS: `lib/supabase.ts`

---

## 🎯 Resumen Rápido para Claude Code

**Para ejecutar migraciones SQL en Supabase:**

```bash
# 1. Cargar contraseña
source .env.local

# 2. Ejecutar migración
echo "$SUPABASE_DB_PASSWORD" | supabase db push --password ""

# 3. Verificar
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/editor"
```

**Credenciales están en**: `.env.local`

**No funciona**: Conexión directa con psql/docker al pooler

**Sí funciona**: `supabase db push` con la contraseña

---

**Última actualización**: 2025-10-21 19:15
**Estado**: ✅ Todas las credenciales configuradas y funcionando
**Migraciones aplicadas**: 2 (clients, conversations+messages)
