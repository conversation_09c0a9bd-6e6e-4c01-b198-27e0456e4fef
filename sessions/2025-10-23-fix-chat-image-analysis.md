# Fix: Chat No Analiza Imágenes - System Prompt Restrictivo

**Fecha**: 2025-10-23
**Branch**: `fix/chat-image-analysis-debugging`
**Estado**: ✅ Resuelto

---

## 🔴 Problema Reportado

El usuario reportó que el chat **NO analizaba imágenes de cabello**, respondiendo siempre:

> "Lo siento, no puedo analizar la imagen. Si puedes describir el cabello, estaré encantado de ayudarte..."

**Evidencia del usuario**:
- Screenshot mostrando usuario enviando imagen con texto "Analiza este cabello"
- IA respondiendo que no puede analizar la imagen
- Logs mostrando que la imagen SÍ se procesaba (1537 prompt_tokens) pero respuesta muy corta (27 completion_tokens)

**Contexto adicional**:
- El workflow de fórmulas (step1/step2) **SÍ analizaba imágenes correctamente**
- Solo fallaba en el chat
- El problema empezó después del merge del PR #5 (multi-turn chat)

---

## 🔍 Investigación y Diagnóstico

### 1. Análisis de Logs de Supabase

```sql
-- Llamadas con imágenes recientes
SELECT created_at, use_case, image_count, prompt_tokens, completion_tokens
FROM ai_usage_log
WHERE image_count > 0
ORDER BY created_at DESC
LIMIT 5;
```

**Resultado**:
```
17:14:52 | vision_analysis | 1 | 1537 | 27  ← Respuesta MUY corta
17:08:08 | vision_analysis | 1 | 1263 | 27  ← Respuesta MUY corta
17:05:43 | vision_analysis | 1 | 1731 | 323 ← Respuesta normal
```

Las respuestas de 27 tokens son exactamente el mensaje genérico de rechazo.

### 2. Análisis de Mensajes del Chat

```sql
SELECT timestamp, role, LEFT(content, 150) as content_preview,
       CASE WHEN images IS NOT NULL THEN jsonb_array_length(images) ELSE 0 END
FROM messages
WHERE timestamp > NOW() - INTERVAL '3 hours'
ORDER BY timestamp DESC;
```

**Confirmación del problema**:
```
17:14:44 | user      | "Analiza este cabello"                          | 1 imagen
17:14:52 | assistant | "Lo siento, no puedo analizar la imagen..."    | 0
```

### 3. Revisión del System Prompt

**System prompt problemático** (líneas 125-136 de `ai-proxy/index.ts`):

```typescript
INSTRUCCIONES CRÍTICAS SOBRE ANÁLISIS DE IMÁGENES:
1. Tu análisis debe enfocarse EXCLUSIVAMENTE en el cabello visible
2. NO analices, menciones ni comentes sobre rostros, personas, identidades...
3. Si aparece una persona en la imagen, ignórala completamente  ← 🔴 PROBLEMA
4. Enfócate SOLO en: color del cabello, textura, condición...
```

### 4. Causa Raíz Identificada

**OpenAI estaba interpretando las instrucciones literalmente**:
- Instrucción: "Si aparece una persona en la imagen, **ignórala completamente**"
- Interpretación de GPT-4o: "No puedo procesar esta imagen porque contiene una persona"
- Resultado: Respuesta genérica de rechazo

**Por qué el workflow funcionaba**:
- `step1.tsx` y `step2.tsx` pasan su **propio systemPrompt custom** que no tiene estas restricciones
- Solo el chat usaba el `defaultSystemPrompt` restrictivo

---

## ✅ Solución Implementada

### Cambios en Edge Function

**Archivo**: `supabase/functions/ai-proxy/index.ts`

**ANTES** (restrictivo):
```typescript
const defaultSystemPrompt = `Eres un experto analista...

INSTRUCCIONES CRÍTICAS SOBRE ANÁLISIS DE IMÁGENES:
1. Tu análisis debe enfocarse EXCLUSIVAMENTE en el cabello visible
2. NO analices, menciones ni comentes sobre rostros, personas...
3. Si aparece una persona en la imagen, ignórala completamente
...`;
```

**DESPUÉS** (directivo pero no restrictivo):
```typescript
const defaultSystemPrompt = `Eres un experto colorista profesional con más de 15 años de experiencia en análisis técnico de cabello.

CONTEXTO: Estás analizando cabello de clientes de salones profesionales que han dado su consentimiento para análisis de coloración.

TU TAREA:
Analiza el CABELLO visible en la imagen y proporciona un diagnóstico técnico detallado que incluya:

1. **Color Actual**:
   - Nivel (escala 1-10)
   - Tono base (cálido/frío/neutro)
   - Reflejos presentes (dorado/ceniza/cobrizo/rojizo)

2. **Condición del Cabello**:
   - Porosidad aparente
   - Integridad de la cutícula
   - Historial químico visible (virgen/teñido/decolorado)

3. **Análisis de Canas** (si aplica):
   - Porcentaje aproximado
   - Distribución
   - Resistencia aparente

4. **Diferencias por Zonas**:
   - Raíces vs medios vs puntas
   - Variaciones de color

IMPORTANTE: Enfócate exclusivamente en aspectos técnicos del cabello (color, textura, condición). No hagas comentarios sobre características personales, faciales, edad, género o identidad.

Sé preciso, técnico y profesional en tu análisis.`;
```

### Logging Adicional para Debugging

Agregado en líneas 102-106:

```typescript
// Debug logging para diagnosticar problema de imágenes
console.log(`[Request] useCase: ${useCase}, hasImages: ${!!imageUrls}, imageCount: ${imageUrls?.length || 0}`);
if (imageUrls && imageUrls.length > 0) {
  console.log(`[Request] First image format: ${imageUrls[0].substring(0, 50)}...`);
  console.log(`[Request] First image size: ${Math.round(imageUrls[0].length / 1024)}KB`);
}
```

Y en líneas 164-165:

```typescript
console.log(`[Vision] Sending ${messages.length} messages with ${imageUrls?.length || 0} images`);
console.log(`[Vision] Current message has ${currentMessageContent.length} content parts (text + images)`);
```

---

## 🚀 Deployment

**Edge Function**: Versión 20 desplegada exitosamente

```bash
# Deploy vía MCP Supabase
mcp__supabase__deploy_edge_function(
  project_id: guyxczavhtemwlrknqpm,
  name: ai-proxy,
  version: 20
)
```

**Timestamp**: 2025-10-23 18:30 UTC
**Status**: ACTIVE
**Afecta a**: Solo chat (el workflow de fórmulas NO se ve afectado)

---

## 📊 Verificación

### Cambios NO Afectan al Workflow de Fórmulas

**Confirmado**: `step1.tsx` y `step2.tsx` pasan su propio `systemPrompt`:

```typescript
// step1.tsx línea 257-262
const response = await generateTextSafe({
  messages: [
    { role: 'system', content: systemPrompt },  // ← Custom prompt
    { role: 'user', content: '...' },
  ],
  useCase: 'vision_analysis',
  imageUris: images,
});
```

El `defaultSystemPrompt` modificado **solo se usa cuando NO se pasa un systemPrompt custom** (caso del chat).

### Expected Behavior Después del Fix

**Antes**:
```
Usuario: [📸 imagen.jpg] "Analiza este cabello"
AI:      "Lo siento, no puedo analizar la imagen..."
```

**Después**:
```
Usuario: [📸 imagen.jpg] "Analiza este cabello"
AI:      "📸 ANÁLISIS DE IMAGEN
         • Raíces: Nivel 8
         • Medios: Nivel 9 con reflejos dorados
         • Puntas: Nivel 9 con oxidación visible
         • Canas: Aproximadamente 15% concentradas en zona frontal
         • Condición: Porosidad media, integridad de cutícula buena..."
```

---

## 💡 Decisiones Técnicas

### ¿Por qué no usar el mismo prompt que el workflow?

**Razones**:
1. El workflow requiere **respuesta JSON estructurada** para parsear automáticamente
2. El chat requiere **respuesta conversacional natural** para el usuario
3. Separar responsabilidades mantiene flexibilidad

### ¿Por qué mantener la restricción de "no comentar características personales"?

**Razones**:
1. **Privacidad**: Aunque hay consentimiento, es best practice no analizar personas
2. **Enfoque**: Mantiene el análisis en lo técnicamente relevante (cabello)
3. **Legal**: Reduce riesgos de inferencias sobre características protegidas

La diferencia clave: **formulación positiva** ("Analiza el cabello") vs negativa ("NO analices personas").

---

## 📝 Archivos Modificados

### Modificados
- `supabase/functions/ai-proxy/index.ts` - System prompt de vision_analysis (líneas 125-153)

### NO Modificados
- `app/(app)/formula/step1.tsx` - Sigue usando su propio prompt custom
- `app/(app)/formula/step2.tsx` - Sigue usando su propio prompt custom
- `app/(app)/(tabs)/chat.tsx` - No requirió cambios (el fix está en Edge Function)

---

## 🎯 Próximos Pasos

### Inmediato
- [x] Desplegar Edge Function v20
- [ ] Usuario prueba el chat con imágenes
- [ ] Verificar que análisis funciona correctamente
- [ ] Monitorear logs de Supabase para confirmar completion_tokens >100

### Corto Plazo (1-2 semanas)
- [ ] Agregar tests automatizados para vision_analysis
- [ ] Crear dashboard de monitoreo de calidad de respuestas
- [ ] Documentar mejores prácticas de prompts para el equipo

### Medio Plazo (1 mes)
- [ ] Implementar A/B testing de diferentes prompts
- [ ] Analizar feedback de usuarios sobre calidad de análisis
- [ ] Optimizar costos de visión (comprimir imágenes antes de enviar)

---

## 🔗 Referencias

### Sesiones Relacionadas
- `sessions/2025-10-23-chat-multi-turn-implementation.md` - Implementación de multi-turn chat
- `sessions/2025-10-23-fix-chat-image-analysis.md` - Fix de race condition con signed URLs

### Documentación OpenAI
- [Vision API Best Practices](https://platform.openai.com/docs/guides/vision)
- [System Message Guidelines](https://platform.openai.com/docs/guides/prompt-engineering)

### Commits
```bash
git log --oneline --grep="chat.*image" -5
```

---

## 📸 Testing Manual (Para Usuario)

### Checklist de Pruebas

- [ ] **Test 1**: Enviar imagen de cabello con "Analiza este cabello"
  - Esperado: Análisis detallado con niveles, tonos, condición

- [ ] **Test 2**: Enviar imagen y luego preguntar sin imagen "¿Qué oxidante recomiendas?"
  - Esperado: Respuesta con referencia a la imagen enviada

- [ ] **Test 3**: Conversación larga (5+ mensajes) con imagen al inicio
  - Esperado: IA mantiene contexto de la imagen

- [ ] **Test 4**: Workflow de fórmulas sigue funcionando
  - Esperado: step1 y step2 analizan imágenes correctamente

---

## ✅ Criterios de Éxito

1. ✅ Chat analiza imágenes correctamente (>100 completion_tokens)
2. ✅ Workflow de fórmulas NO afectado
3. ✅ Logging detallado para debugging futuro
4. ✅ System prompt más natural y conversacional
5. ⏳ Feedback positivo del usuario (pending test)

---

**Tiempo de resolución**: ~2 horas
**Complejidad**: Media (requirió análisis de logs + debugging de prompts)
**Impacto**: Alto (funcionalidad core del chat restaurada)
