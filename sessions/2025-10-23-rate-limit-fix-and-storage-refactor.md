# Fix Rate Limit 429 + Refactor Storage Architecture

**Fecha**: 2025-10-23
**Branch**: `feature/supabase-storage-photo-consent`
**Duración**: ~2 horas

---

## 🎯 Objetivos de la Sesión

1. ✅ Resolver error 429 (Rate Limit Exceeded) en análisis de imágenes del chat
2. ✅ Eliminar duplicación de bucket `hair-images-temp`
3. ⚠️ Debugging pendiente: Chat no analiza imágenes después del refactor

---

## 🔴 Problema 1: Error 429 - Rate Limit Exceeded

### Síntomas

Usuario reportó que al subir fotos al chat para análisis, recibía error 429 después de 3 reintentos:

```
ERROR [AIClient] Edge Function error: [FunctionsHttpError: Edge Function returned a non-2xx status code]
ERROR [AIClient] Status: 429
ERROR [AIClient] Attempt 1 failed
ERROR [AIClient] Attempt 2 failed
ERROR [AIClient] Attempt 3 failed
ERROR Error al obtener respuesta de IA
```

### Análisis

**Logs de Supabase Edge Function:**
- Version 17: 3 requests con status 429 (09:45:03, 09:45:08, 09:45:14)
- Version 16: Todas las requests anteriores con status 200

**Tabla `rate_limits`:**
```sql
{
  "user_id": "6bc0edd1-5142-4028-a48f-1b818b7dc7db",
  "requests_today": 35,
  "requests_this_hour": 20,  ← Exactamente en el límite
  "hourly_reset_at": "2025-10-23 10:00:00+00"
}
```

**Causa raíz:**
Edge Function v17 tenía configurado `p_hourly_limit: 20`, y el usuario había alcanzado exactamente 20 requests en la última hora. Cada intento de análisis consumía 1 request, causando el rechazo inmediato.

### Solución

**1. Reset inmediato del contador** (temporal):
```sql
UPDATE rate_limits
SET
  requests_this_hour = 0,
  hourly_reset_at = now() + interval '1 hour'
WHERE user_id = '6bc0edd1-5142-4028-a48f-1b818b7dc7db';
```

**2. Aumentar límites** (permanente):
```typescript
// supabase/functions/ai-proxy/index.ts:77-80
const { data: canProceed } = await supabase.rpc('check_rate_limit', {
  p_user_id: user.id,
  p_daily_limit: 500,  // 100 → 500
  p_hourly_limit: 100, // 20 → 100
});
```

**3. Deploy Edge Function v18**:
```bash
mcp__supabase__deploy_edge_function(ai-proxy, v18)
```

**Resultado:** ✅ Usuario puede hacer hasta 100 requests/hora ahora

---

## 🔍 Problema 2: Duplicación de Storage (hair-images-temp)

### Descubrimiento

Durante el análisis del error 429, descubrimos que las imágenes se subían **2 veces**:

**Buckets existentes:**
```
hair-photos:        12 archivos, 3.0 MB
hair-images-temp:   23 archivos, 4.4 MB (huérfanos)
```

**Flujo con duplicación:**
```
Usuario → ChatContext.uploadConsultationPhoto() → hair-photos ✅
       ↓
chat.tsx → pasa URIs locales (file://)
       ↓
ai-client.ts → processMultipleImages()
            → upload a hair-images-temp ❌ (segunda subida)
            → genera signed URLs
            → pasa a Edge Function
            → intenta cleanup (falla con errores)
       ↓
Resultado: 23 archivos huérfanos en hair-images-temp
```

### Análisis Histórico

Revisando commits anteriores (b0f5739), encontramos un **malentendido**:

> **Commit message**: "AI no puede leer signed URLs"

**ESTO ES FALSO** ❌

El verdadero problema era el **system prompt** (resuelto en db85896):
- OpenAI **SÍ puede leer signed URLs** perfectamente
- El problema era que rechazaba imágenes con rostros
- Se solucionó agregando: *"enfócate EXCLUSIVAMENTE en el cabello, ignora rostros"*

Por lo tanto, `hair-images-temp` era innecesario desde el principio.

### Refactor Implementado

#### 1. Modificar `app/(tabs)/chat.tsx`

**ANTES:**
```typescript
const currentImages = [...selectedImages]; // URIs locales

await addMessage(currentConversationId, userMessage);

aiResponseContent = await generateTextSafe({
  imageUris: currentImages, // ← URIs locales
  ...
});
```

**DESPUÉS:**
```typescript
const hasImages = selectedImages.length > 0;

await addMessage(currentConversationId, userMessage);

// Esperar a que React actualice el estado
await new Promise(resolve => setTimeout(resolve, 100));

// Obtener signed URLs de la DB
const updatedConversation = conversations.find(...);
const signedImageUrls = updatedConversation?.messages[last].images || [];

aiResponseContent = await generateTextSafe({
  imageUrls: signedImageUrls, // ← Signed URLs de hair-photos
  ...
});
```

#### 2. Simplificar `lib/ai-client.ts`

**Eliminado (91 líneas):**
- Upload a `hair-images-temp`
- Generación de signed URLs temporales
- Cleanup de archivos temporales

**Agregado:**
```typescript
let finalImageUrls: string[] | undefined;

// Si vienen imageUrls (signed URLs), usarlas directamente
if (imageUrls && imageUrls.length > 0) {
  finalImageUrls = imageUrls;
  console.log(`[AIClient] Using ${imageUrls.length} pre-uploaded images`);
}
// Si vienen imageUris (workflow), convertir a data URLs
else if (imageUris && imageUris.length > 0) {
  const processedUris = await processMultipleImages(imageUris);
  finalImageUrls = processedUris.map(uri => {
    const base64 = await readImageAsBase64(uri);
    return `data:image/jpeg;base64,${base64}`;
  });
}
```

#### 3. Eliminar bucket `hair-images-temp`

```sql
-- Eliminar 23 archivos huérfanos
DELETE FROM storage.objects WHERE bucket_id = 'hair-images-temp';

-- Eliminar bucket
DELETE FROM storage.buckets WHERE id = 'hair-images-temp';
```

```bash
# Eliminar migración obsoleta
rm supabase/migrations/20251021210000_create_storage_bucket.sql
```

### Beneficios del Refactor

| Métrica | Antes | Después | Mejora |
|---------|-------|---------|--------|
| Buckets | 2 | 1 | -50% |
| Uploads por imagen | 2 | 1 | -50% |
| Archivos huérfanos | 23 | 0 | -100% |
| Líneas de código | 376 | 285 | -91 (-24%) |
| Storage usado | 7.4 MB | 3.0 MB | -59% |

---

## ⚠️ Problema 3: Chat No Analiza Imágenes (Pendiente)

### Estado Actual

Después del refactor, el chat **no analiza imágenes**. El usuario ve:
> "Parece que no puedo ver fotos directamente..."

### Hipótesis Principal

**Race condition en React state:**

```typescript
await addMessage(...);  // Sube + guarda en DB + loadConversations()
await new Promise(resolve => setTimeout(resolve, 100)); // ← ¿Suficiente?
const updatedConversation = conversations.find(...);
const signedImageUrls = updatedConversation?.messages[last].images;
// ❌ Puede estar vacío si React no actualizó a tiempo
```

### Debugging Pendiente

Verificar en próxima sesión:

1. **¿Upload funciona?**
   ```typescript
   // ChatContext.tsx:314
   console.log('Uploaded images:', uploadedImageUrls);
   ```

2. **¿DB tiene las URLs?**
   ```sql
   SELECT images FROM messages
   WHERE images IS NOT NULL
   ORDER BY created_at DESC LIMIT 1;
   ```

3. **¿chat.tsx las obtiene?**
   ```typescript
   // chat.tsx:120
   console.log('[Chat] Image URLs from DB:', signedImageUrls);
   ```

4. **¿ai-client.ts las recibe?**
   ```typescript
   // ai-client.ts:115
   console.log('[AIClient] Received imageUrls:', imageUrls);
   ```

**Archivo de debugging**: `NEXT_SESSION_PROMPT.md` contiene plan completo

---

## 📊 Commits Realizados

```bash
9f7133a refactor: Eliminar duplicación de hair-images-temp - usar solo hair-photos
e54ee41 fix: Esperar actualización de estado antes de obtener signed URLs
5c84b4e docs: Crear prompt para debugging de chat image analysis
```

**Archivos modificados:**
- `app/(tabs)/chat.tsx` - Obtener signed URLs de DB
- `lib/ai-client.ts` - Usar signed URLs directamente, eliminar upload temporal
- `supabase/functions/ai-proxy/index.ts` - Aumentar rate limits
- `supabase/migrations/20251021210000_create_storage_bucket.sql` - Eliminado

---

## 🎯 Testing Realizado

### ✅ Pasaron

- Lint sin errores
- Edge Function v18 deployada correctamente
- Rate limit 429 resuelto
- Bucket hair-images-temp eliminado
- Workflow de fórmulas (step1/step2) sigue funcionando

### ⏳ Pendiente

- **Chat image analysis** - Necesita debugging
- Testing manual después de fix

---

## 📚 Lecciones Aprendidas

### 1. Verificar suposiciones con datos reales

El commit b0f5739 decía "AI no puede leer signed URLs", pero esto era falso:
- OpenAI **SÍ puede leer signed URLs**
- El verdadero problema era el system prompt
- Esto llevó a crear `hair-images-temp` innecesariamente

**Aprendizaje:** Siempre verificar claims técnicos con testing real antes de crear workarounds.

### 2. Rate limits deben ser generosos en desarrollo

20 requests/hora era demasiado restrictivo:
- Cada intento de análisis = 1 request
- 3 reintentos = 3 requests por error
- Fácil alcanzar el límite durante testing

**Solución:** 100/hora para dev, ajustar en producción según tier de usuario.

### 3. React state updates no son síncronos

```typescript
await addMessage(...);
const data = currentConversation?.messages; // ❌ Puede estar desactualizado
```

Aunque `addMessage()` llama `loadConversations()` que actualiza el estado, React no garantiza que el componente se re-renderice inmediatamente.

**Solución temporal:** `await new Promise(resolve => setTimeout(resolve, 100))`
**Solución mejor:** Retornar las signed URLs directamente desde `addMessage()`

### 4. Logs son esenciales para debugging

Sin los logs de Supabase Edge Function, habríamos tardado mucho más en identificar el error 429.

**Agregado:**
```typescript
console.log('[AIClient] Using ${imageUrls.length} pre-uploaded images');
console.log('[Chat] Image URLs from DB:', signedImageUrls);
```

---

## 🔧 Configuración Final

### Edge Function v18
```typescript
p_daily_limit: 500
p_hourly_limit: 100
```

### Buckets de Storage
- `hair-photos` (privado, cifrado)
  - `{userId}/consultations/` - Fotos de chat (14 días)
  - `{userId}/formulas/{clientId}/` - Fotos de fórmulas (90 días)

### Arquitectura de Imágenes

**Chat (consultas):**
```
Usuario → uploadConsultationPhoto() → hair-photos/consultations/
       → Retorna signed URL
       → Guarda en messages.images
       → chat.tsx usa signed URL para análisis
```

**Workflow (fórmulas):**
```
step1/step2 → Procesa localmente (blur + resize)
           → Guarda URI local en FormulaContext
           → step5 llama uploadFormulaPhoto() → hair-photos/formulas/
```

---

## 📝 TODOs para Próxima Sesión

1. **🔴 CRÍTICO**: Debugging de chat image analysis
   - Agregar console.logs en todo el flujo
   - Verificar DB directamente
   - Determinar causa raíz (race condition vs upload fallido)

2. **Opciones de fix:**
   - **Opción A**: Retornar signed URLs desde `addMessage()`
   - **Opción B**: Aumentar timeout de 100ms a 500ms
   - **Opción C**: Fallback temporal a URIs locales si signed URLs vacías

3. **Testing completo:**
   - Análisis de imágenes en chat (con signed URLs)
   - Workflow de fórmulas (con URIs locales → data URLs)
   - Edge cases (imágenes grandes, múltiples imágenes, etc.)

---

## 🔗 Referencias

- **NEXT_SESSION_PROMPT.md** - Plan completo de debugging
- **sessions/2025-10-23-supabase-storage-implementation.md** - Implementación original de storage
- **sessions/2025-10-23-image-storage-research-privacy-compliance.md** - Research de GDPR/compliance

---

**Estado final**: 🟡 Refactor completado, debugging pendiente
**Última actualización**: 2025-10-23 12:00
