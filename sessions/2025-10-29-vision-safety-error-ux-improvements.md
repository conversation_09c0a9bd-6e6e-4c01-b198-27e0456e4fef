# Vision Safety Error - UX Improvements
**Última actualización**: 2025-10-29 16:00

## Contexto

### Problema Reportado

Los usuarios experimentaban errores `VisionSafetyError` cuando subían imágenes en:
- `app/(app)/formula/step1.tsx` - Análisis de color actual
- `app/(app)/formula/step2.tsx` - Análisis de color deseado
- `app/(app)/(tabs)/chat.tsx` - Chat con análisis de imágenes

**Error específico**:
```
[VisionSafetyError: No pudimos analizar las imágenes debido a restricciones de seguridad.
Por favor, intenta con fotos que muestren solo el cabello sin rostros visibles, o usa fotos con mejor iluminación.]
```

### Causa Raíz

OpenAI Vision API (usado vía Rork SDK) detecta rostros/personas en las imágenes y rechaza el análisis por políticas de seguridad. Esto ocurre incluso con contexto profesional explícito ("hair salon service", "professional analysis").

## Solución Implementada (Fase 1)

### Enfoque Estratégico: 3 Fases

**Fase 1 (Esta PR - 80% de impacto):**
- ✅ Guía visual preventiva ANTES de subir fotos
- ✅ Mejor error handling con instrucciones específicas
- ✅ Componente reutilizable `<PhotoGuidance />`

**Fase 2 (Futuro - Experimental):**
- 🔄 Face blur automático con expo-image-manipulator
- 🔄 A/B testing para medir mejora

**Fase 3 (Futuro - Si persiste):**
- 🔄 Edge function de pre-procesamiento (blur server-side)
- 🔄 Modelo de visión alternativo si OpenAI es muy restrictivo

## Cambios Realizados

### 1. Nuevo Componente: `components/PhotoGuidance.tsx`

**Props**:
- `compact?: boolean` - Modo compacto para chat (default: false)

**Modos**:
- **Full mode** (step1/step2): Sección expandida con recomendaciones visuales
  - ✅ Recomendaciones (Check icon):
    - Enfoca solo el cabello
    - Iluminación natural
    - Diferentes ángulos
    - Fotos claras
  - ❌ Evita (X icon):
    - Primeros planos de rostros
    - Selfies frontales
    - Fotos oscuras/borrosas
  - 💡 Nota educativa sobre privacidad

- **Compact mode** (chat): Banner colapsable
  - 📸 "Tip: Toma fotos solo del cabello, evita rostros"
  - Long press en botones de cámara para mostrar/ocultar

**Estilo**: Blue/white theme (#EFF6FF, #DBEAFE) para diferenciarlo de alerts

### 2. Actualización de `step1.tsx` (Color Actual)

**Cambios**:
1. Import `PhotoGuidance`:
   ```tsx
   import PhotoGuidance from '@/components/PhotoGuidance';
   ```

2. Reemplazar sección de tips colapsable (líneas 977-1004) con:
   ```tsx
   <PhotoGuidance />
   ```

3. Mejorar Alert de `VisionSafetyError` (líneas 336-355):
   - ⚠️ Título más claro: "Análisis bloqueado por seguridad"
   - ✅ Instrucciones específicas con bullets
   - 💡 Tip: "Puedes fotografiar el cabello desde atrás o desde los costados"
   - 3 botones: "Cambiar fotos" | "Reintentar" | "Entendido"

### 3. Actualización de `step2.tsx` (Color Deseado)

**Cambios**: Idénticos a step1.tsx
1. Import `PhotoGuidance`
2. Reemplazar sección de tips (líneas 733-760) con `<PhotoGuidance />`
3. Mejorar Alert de `VisionSafetyError` (líneas 386-405)

### 4. Actualización de `chat.tsx` (Chat con Visión)

**Cambios**:
1. Import `PhotoGuidance`:
   ```tsx
   import PhotoGuidance from '@/components/PhotoGuidance';
   ```

2. Estado para controlar visibilidad:
   ```tsx
   const [showPhotoGuidance, setShowPhotoGuidance] = useState(false);
   ```

3. Agregar componente compacto en `inputContainer` (antes de `imagesPreviewContainer`):
   ```tsx
   {showPhotoGuidance && (
     <View style={styles.photoGuidanceContainer}>
       <PhotoGuidance compact />
     </View>
   )}
   ```

4. Long press en botones de cámara para mostrar guidance:
   ```tsx
   <TouchableOpacity
     onPress={takePhoto}
     onLongPress={() => setShowPhotoGuidance(!showPhotoGuidance)}
   >
   ```

5. Mejorar Alert de `VisionSafetyError` (líneas 491-524):
   - Mensaje mejorado con bullets
   - Botón "Cambiar fotos" auto-muestra guidance: `setShowPhotoGuidance(true)`
   - Tip: "Mantén presionado el botón de cámara para ver más consejos"

6. Agregar estilo CSS:
   ```tsx
   photoGuidanceContainer: {
     marginBottom: 12,
   },
   ```

## Decisiones Técnicas

### ¿Por qué NO implementar face blur automático ahora?

**Razones**:
1. **Complejidad**: Requiere librerías de ML (expo-face-detector, ml-kit, o API externa)
2. **Performance**: Procesamiento en cliente puede ser lento, especialmente con múltiples imágenes
3. **Calidad**: Blur puede afectar zonas cercanas del cabello si no se hace correctamente
4. **ROI**: La guía preventiva es más simple y probablemente resuelve 80% de casos

**Ventajas de la guía preventiva**:
- ✅ Implementación inmediata (1 componente reutilizable)
- ✅ Sin dependencias externas
- ✅ Sin overhead de performance
- ✅ Educativo para el usuario (aprenden a tomar mejores fotos)
- ✅ Fácil de A/B test

### ¿Por qué componente separado vs inline?

**Ventajas de `<PhotoGuidance />`**:
- ✅ Reutilizable en 3 lugares (step1, step2, chat)
- ✅ DRY: Single source of truth para instrucciones
- ✅ Fácil de actualizar mensajes en un solo lugar
- ✅ Testeable de forma aislada
- ✅ Modo compacto/full mode desde props

### ¿Por qué long press en chat vs botón explícito?

**Ventajas long press**:
- ✅ No ocupa espacio adicional en UI (chat ya está denso)
- ✅ Descubrible via hint en Alert
- ✅ Usuarios aprenden el pattern rápido
- ✅ Mantiene chat limpio y minimalista

**Alternativa considerada**: Botón "?" o "Tips" → Rechazada por ocupar espacio y cluttering

## Resultados Esperados

### KPIs a Monitorear

1. **Tasa de éxito de visión AI**:
   - Baseline actual: ~70-80% (estimado, muchos VisionSafetyError)
   - Target post-implementación: >90%

2. **Tiempo promedio entre error y retry exitoso**:
   - Baseline actual: ~2-3 minutos (usuario confundido)
   - Target post-implementación: <60 segundos (instrucciones claras)

3. **Tasa de abandono después de VisionSafetyError**:
   - Baseline actual: ~30-40% (estimado)
   - Target post-implementación: <15%

### Métricas de Logging (Opcional)

Si queremos medir impacto:
```typescript
// En analyzeImages() y sendMessage()
console.log('[VisionSafety] Attempt 1');
// Si VisionSafetyError:
console.log('[VisionSafety] Blocked - User shown guidance');
// Si retry exitoso:
console.log('[VisionSafety] Retry success after guidance');
```

Esto permitiría analizar en Supabase `ai_usage_log` tabla para ver:
- % de VisionSafetyError antes vs después
- % de retry exitosos después de ver guidance

## Próximos Pasos (Fase 2 - Opcional)

### Si los errores persisten >10%:

1. **Implementar face blur automático**:
   ```typescript
   // Pseudo-código
   import * as FaceDetector from 'expo-face-detector';
   import * as ImageManipulator from 'expo-image-manipulator';

   async function blurFaces(imageUri: string) {
     // Detectar rostros
     const faces = await FaceDetector.detectFacesAsync(imageUri);

     // Si hay rostros, aplicar blur solo en esas regiones
     if (faces.length > 0) {
       return await ImageManipulator.manipulateAsync(imageUri, [
         { blur: { intensity: 20, region: faces[0].bounds } }
       ]);
     }

     return imageUri; // Sin blur si no hay rostros
   }
   ```

2. **A/B Testing**:
   - 50% usuarios: guidance only (control)
   - 50% usuarios: guidance + auto blur (test)
   - Medir tasa de éxito, abandono, y feedback

3. **Considerar servicios de blur externos**:
   - Cloudflare Workers Image Processing
   - AWS Rekognition → Lambda blur
   - Supabase Edge Function con Sharp.js

### Si nada funciona (Fase 3):

1. **Evaluar modelos alternativos**:
   - Google Cloud Vision API (más permisivo con fotos de cabello?)
   - Azure Computer Vision (permite contexto médico/profesional)
   - Anthropic Claude Vision (más reciente, posible mejor handling)

2. **Implementar modal de consentimiento explícito**:
   - Checkbox: "Autorizo análisis de imágenes para fines profesionales de coloración"
   - Agregar esto al prompt del AI como contexto legal

## Testing Realizado

### ✅ Manual Testing Checklist

- [ ] step1.tsx muestra PhotoGuidance correctamente
- [ ] step2.tsx muestra PhotoGuidance correctamente
- [ ] chat.tsx muestra PhotoGuidance compacto al long press
- [ ] Alert de VisionSafetyError tiene nuevo formato en los 3 lugares
- [ ] Botón "Cambiar fotos" limpia imágenes correctamente
- [ ] Botón "Reintentar" vuelve a intentar análisis
- [ ] chat.tsx: "Cambiar fotos" auto-muestra guidance
- [ ] No hay errores de TypeScript
- [ ] No hay warnings de linter

### ⚠️ Testing Pendiente

- [ ] Test con usuario real: ¿entienden las instrucciones?
- [ ] Test con fotos problemáticas: ¿siguen fallando?
- [ ] Test en diferentes dispositivos (iOS/Android)
- [ ] Test de accesibilidad (screen readers)

## Notas Adicionales

### Alternativas Consideradas y Descartadas

1. **Prompt engineering más agresivo**:
   - ❌ Ya intentamos con múltiples variaciones de prompts
   - ❌ OpenAI Vision tiene filtro de seguridad hard-coded

2. **Disclaimer modal al inicio**:
   - ❌ Demasiado friction para usuarios
   - ❌ No garantiza que tomen fotos correctas

3. **Pre-procesamiento con detección facial básica**:
   - ❌ Requiere librerías pesadas (TensorFlow.js)
   - ❌ Complicado en React Native

4. **Crop automático a solo cabello**:
   - ❌ Difícil de hacer bien sin ML
   - ❌ Podría cortar información importante

### Referencias

- OpenAI Vision Safety Guide: https://platform.openai.com/docs/guides/vision/managing-images
- Expo FaceDetector: https://docs.expo.dev/versions/latest/sdk/facedetector/
- expo-image-manipulator: https://docs.expo.dev/versions/latest/sdk/imagemanipulator/

## TODOs

- [ ] Monitorear tasa de VisionSafetyError en producción (primeras 2 semanas)
- [ ] Recoger feedback de usuarios sobre claridad de instrucciones
- [ ] Decidir si implementar Fase 2 (face blur) basado en métricas
- [ ] Considerar agregar ejemplos visuales (screenshots) de fotos correctas vs incorrectas

---

**Autor**: Claude Code
**Reviewers**: Oscar Cortijo
**Estado**: ✅ Implementado (Fase 1)
**Próxima revisión**: 2025-11-12 (2 semanas)
