# Configuración de Agentes Claude Code

**Fecha**: 2025-10-23
**Estado**: ✅ Completo

## Resumen

Se han configurado 6 agentes especializados de Claude Code para mejorar la colaboración durante el desarrollo de Salonier AI. Estos agentes permiten delegar tareas específicas que requieren expertise especializado.

## ⚠️ IMPORTANTE: Diferencia con Agentes AI de Salonier

**NO confundir**:
- **Agent<PERSON>** (este documento): Herramientas para Claude durante el desarrollo
- **Agentes AI de Salonier** (`lib/agents/`): Sistema multi-agente para la app (Hair Analysis, Safety, Formula Creation, etc.)

Son sistemas completamente separados con propósitos diferentes.

## Formato de Archivos

Los agentes de Claude Code requieren **archivos Markdown (`.md`) con YAML frontmatter**:

```markdown
---
name: agent-name
description: Description of when to use this agent
tools: Read, <PERSON>rep, Glob, Bash
model: sonnet
---

System prompt content here...
```

**Ubicación**: `.claude/agents/` (nivel de proyecto) o `~/.claude/agents/` (global)

## Agentes Creados

### 1. Code Reviewer
**Ubicación**: `.claude/agents/code-reviewer.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob, Bash

**Uso**:
```
@Code-Reviewer revisa los cambios en app/(app)/(tabs)/chat.tsx
```

**Qué hace**:
- Revisa código para bugs, edge cases, errores de runtime
- Valida TypeScript type safety
- Verifica uso correcto de React hooks
- Chequea manejo de errores
- Valida preocupaciones mobile-specific (performance, memoria)
- Verifica accesibilidad (a11y)
- Sugiere optimizaciones de performance
- Valida seguridad (API keys, auth, validación de datos)

**Específico para Salonier AI**:
- Asegura que queries Supabase estén tipadas
- Valida uso de Contexts (Formula/Client/Chat)
- Verifica navegación con typed routes
- Valida que Safety Agent checks no se bypaseen
- Asegura GDPR compliance para fotos

---

### 2. Security Reviewer
**Ubicación**: `.claude/agents/security-reviewer.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob, Bash, WebSearch

**Uso**:
```
@Security-Reviewer analiza vulnerabilidades en lib/agents/specialized/safety-agent.ts
```

**Qué hace**:
- Detecta vulnerabilidades de autenticación/autorización
- Identifica exposición de datos (API keys, secrets, PII)
- Valida input validation y sanitization
- Detecta SQL/NoSQL injection risks
- Revisa XSS y CSRF vulnerabilities
- Analiza almacenamiento inseguro de datos
- Verifica seguridad de red (HTTPS)
- Chequea vulnerabilidades de dependencias
- Asegura GDPR y privacy compliance

**Específico para Salonier AI**:
- Valida RLS policies
- Verifica consentimiento de fotos (GDPR)
- Asegura que AI prompts no filtren info sensible
- Valida que Safety Agent blocks sean enforced
- Chequea expiración de signed URLs
- Verifica cifrado de datos de alergias/salud

**Output**:
- Vulnerabilidades críticas (fix inmediato)
- Issues de alto riesgo (fix antes de producción)
- Issues de riesgo medio
- Mejores prácticas
- CVE references, OWASP Top 10 mappings
- Pasos de remediación con código

---

### 3. Tech Lead
**Ubicación**: `.claude/agents/tech-lead.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob, Bash, WebSearch

**Uso**:
```
@Tech-Lead ¿debería usar Zustand en vez de Context para el estado de fórmulas?
```

**Qué hace**:
- Evalúa decisiones arquitectónicas y trade-offs
- Asegura consistencia con patrones existentes
- Guía decisiones de state management
- Asesora en performance y escalabilidad
- Recomienda librerías y herramientas apropiadas
- Planifica estrategias de refactoring
- Balancea technical debt vs feature velocity
- Asegura mantenibilidad y testabilidad

**Conoce la arquitectura de Salonier AI**:
- Context pattern (@nkzw/create-context-hook)
- Supabase (PostgreSQL + Storage + Edge Functions)
- Expo Router (file-based, typed routes)
- React Query + Contexts (NO Redux/Zustand)
- Multi-agent orchestration (lib/agents/)
- React Native StyleSheet + NativeWind

**Output**:
- 2-3 opciones con pros/cons
- Recomendación con rationale
- Consideraciones: performance, maintainability, time to implement
- Flags de breaking changes
- Enfoques incrementales

**Principios**:
- Preferir patrones establecidos sobre nuevas abstracciones
- Favorecer composition sobre inheritance
- Componentes pequeños y enfocados
- Optimizar para code readability
- Documentar decisiones arquitectónicas

---

### 4. Test Engineer
**Ubicación**: `.claude/agents/test-engineer.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob, Bash, Write, Edit

**Uso**:
```
@Test-Engineer diseña tests para lib/agents/specialized/safety-agent.ts
```

**Qué hace**:
- Diseña estrategias de testing comprehensivas
- Unit testing (Jest, React Testing Library)
- Integration testing (mocking API, Supabase)
- E2E testing (Detox, Maestro, Appium)
- Visual regression testing
- Performance testing
- Accessibility testing
- Generación de test data
- Integración CI/CD

**Específico para Salonier AI**:
- Tests de context hooks
- Tests de AI agents (mock Rork API)
- Tests de integración Supabase
- Validación de cálculos de fórmulas (crítico)
- Tests de Safety Agent (DEBE bloquear escenarios peligrosos)
- Tests de navegación (expo-router)
- Tests de upload/storage (mock signed URLs)

**Output**:
- Test plan outline
- Sample test code
- Mocking strategies
- Recomendaciones CI/CD
- Coverage analysis

**Nota**: Este proyecto NO tiene tests actualmente. Recomienda enfoque incremental:
1. Safety Agent tests (crítico)
2. Formula calculation tests
3. Context hooks tests
4. E2E happy path
5. Expandir coverage progresivamente

---

### 5. Performance Optimizer
**Ubicación**: `.claude/agents/performance-optimizer.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob, Bash, WebSearch

**Uso**:
```
@Performance-Optimizer analiza app/(app)/(tabs)/chat.tsx para optimizaciones
```

**Qué hace**:
- Render performance (useMemo, useCallback, React.memo)
- Bundle size optimization
- Image loading y caching
- Memory leaks (listeners, timers, subscriptions)
- Network performance (caching, batching, compression)
- Database query optimization
- Startup time
- Battery consumption
- Animation performance (60fps target)

**Específico para Salonier AI**:
- Optimizar FlatList en chat (renderItem, keyExtractor)
- Reducir latencia de AI API (parallel execution, caching)
- Optimizar image uploads (compression, background)
- Mejorar performance de queries Supabase (indexes, pagination)
- Minimizar re-renders en formula steps
- Optimizar context providers (memo, dependencies)
- Lazy load screens pesadas
- Reducir bundle size

**Output**:
- Profile del código (hotspots)
- Baseline metrics
- Optimizaciones específicas propuestas
- Estimación de impacto
- Trade-offs (complejidad vs gain)
- Before/after code examples

**Herramientas recomendadas**:
- React DevTools Profiler
- Flipper
- Xcode Instruments / Android Profiler
- why-did-you-render
- Bundle analyzer

**Prioridades**:
1. User-facing performance (perceived speed)
2. Critical paths (chat, formula generation)
3. Battery/memory
4. Bundle size

---

### 6. UX Reviewer
**Ubicación**: `.claude/agents/ux-reviewer.md`
**Modelo**: Sonnet
**Herramientas**: Read, Grep, Glob

**Uso**:
```
@UX-Reviewer revisa app/formula/step3.tsx para mejoras de UX
```

**Qué hace**:
- Mobile UX best practices (thumb zones, tap targets, gestures)
- iOS y Android design guidelines
- Accessibility (a11y) - WCAG 2.1 AA
- User flows y task completion
- Error messaging y feedback
- Loading states y skeleton screens
- Empty states y first-run experience
- Form design y validation
- Navigation clarity
- Visual hierarchy y typography

**Específico para usuarios de Salonier AI** (coloristas profesionales):
- Optimizar para uso con una mano (coloristas sostienen productos)
- Tap targets grandes (manos mojadas/con guantes)
- Feedback visual claro (confirm photo upload, formula saved)
- Terminología profesional (términos de industria)
- Quick access a tareas frecuentes (client lookup, formula duplication)
- Safety warnings IMPOSIBLES de ignorar (red, large, blocking)
- Photo analysis results deben ser scannables (bullet format)
- Formula steps numerados y claros (aplicados en salón)

**Checklist de revisión**:
1. **Accessibility**:
   - Screen reader support
   - Color contrast (4.5:1 minimum)
   - Touch target size (44x44pt minimum)
   - Keyboard navigation (web)
2. **User feedback**:
   - Loading indicators
   - Success/error messages
   - Haptic feedback
3. **Error prevention**:
   - Validation antes de submit
   - Confirmation para acciones destructivas
   - Autosave (formula drafts)
4. **Consistency**:
   - Button styles
   - Spacing y alignment
   - Terminología
5. **Performance perception**:
   - Optimistic UI updates
   - Skeleton screens
   - Progressive loading

**Output**:
- UX issues (categorizados por severidad)
- Recomendaciones específicas con ejemplos
- Referencias a best practices de industria
- Quick wins (high impact, low effort)

**Principio guía**: "¿Entenderá un colorista ocupado esto en 2 segundos?"

---

## Cómo Usar los Agentes

### Sintaxis Básica

```
@Agent-Name <tu petición>
```

### Ejemplos de Uso

#### Code Review de un PR
```
@Code-Reviewer revisa todos los cambios en la rama feature/chat-improvements
```

#### Consulta Arquitectónica
```
@Tech-Lead ¿debería mover la lógica de AI agents a un custom hook o mantenerla en el componente?
```

#### Security Audit
```
@Security-Reviewer haz un security audit completo del sistema de autenticación
```

#### Diseño de Tests
```
@Test-Engineer crea un test plan para el sistema de agentes AI (lib/agents/)
```

#### Performance Analysis
```
@Performance-Optimizer analiza por qué el chat tarda en cargar y propón optimizaciones
```

#### UX Review
```
@UX-Reviewer revisa el flujo completo de creación de fórmulas (steps 0-5) y sugiere mejoras
```

### Multi-Agent Collaboration

Puedes usar múltiples agentes en secuencia para análisis comprehensivo:

```
1. @Tech-Lead evalúa si deberíamos refactorizar ClientContext para usar React Query mutations
2. (Después de recibir respuesta)
3. @Code-Reviewer revisa la implementación propuesta
4. @Performance-Optimizer valida que no haya impacto negativo de performance
5. @Test-Engineer diseña tests para la nueva implementación
```

## Ventajas de los Agentes

### ✅ Expertise Especializado
Cada agente está optimizado para un dominio específico con contexto profundo del proyecto.

### ✅ Consistencia
Los agentes conocen la arquitectura, patrones y convenciones de Salonier AI.

### ✅ Eficiencia
Delegar tareas específicas permite análisis más profundos sin consumir el contexto principal.

### ✅ Calidad
Code reviews, security audits y UX analysis más rigurosos y detallados.

### ✅ Aprendizaje
Los agentes documentan best practices y rationale para decisiones técnicas.

## Cuándo Usar Cada Agente

| Agente | Usar cuando... |
|--------|----------------|
| **Code Reviewer** | Terminaste una feature, antes de PR, después de refactor |
| **Security Reviewer** | Cambios en auth, manejo de datos sensibles, antes de producción |
| **Tech Lead** | Decisiones arquitectónicas, elección de librerías, refactoring grande |
| **Test Engineer** | Implementando feature nueva, antes de merge a main, cero tests actuales |
| **Performance Optimizer** | App lenta, bundle grande, high memory usage, antes de release |
| **UX Reviewer** | Nueva pantalla/flujo, después de diseño, antes de QA/user testing |

## Limitaciones

1. **No reemplazan a Claude Code**: Son delegados, Claude Code sigue coordinando
2. **Context window separado**: Cada agente tiene su propio contexto (no comparten memoria)
3. **No pueden ejecutar tools destructivos**: No pueden hacer commits, push, deploy
4. **Stateless**: Cada invocación es independiente

## Próximos Pasos

- [ ] Probar cada agente con casos reales
- [ ] Ajustar system prompts basándose en resultados
- [ ] Crear más agentes si es necesario (Database Expert, API Designer, etc.)
- [ ] Documentar casos de uso comunes
- [ ] Integrar en workflow de PR (Code Reviewer automático)

---

**Autor**: Claude Code
**Fecha**: 2025-10-23
**Versión**: 1.0.0
