# Fix: Login Redirect y Blob URLs en Chat

**Última actualización**: 2025-10-23 08:15

## Contexto

Usuario reportó dos problemas después de hacer login:
1. Pantalla en blanco que decía "Esta pantalla no existe"
2. Errores de blob URLs en la app móvil

## Problemas Encontrados y Soluciones

### 1. ✅ Pantalla en Blanco Después de Login

**Error**:
Después de autenticarse exitosamente, la app navegaba a `/(tabs)` pero esa ruta no existía, mostrando pantalla "not found".

**Causa raíz**:
- `app/_layout.tsx:32` intentaba navegar a `router.replace('/(tabs)')`
- No existía el archivo `app/(tabs)/index.tsx`
- Expo Router no encontraba la ruta y mostraba error 404

**Solución**:
Cambiar la navegación para ir directamente a una tab específica:

```typescript
// app/_layout.tsx:32
// Antes:
router.replace('/(tabs)');

// Después:
router.replace('/(tabs)/chat');
```

**Archivo afectado**: `app/_layout.tsx:32`

**Bonus fix**: Agregado `router` a las dependencias del useEffect para evitar warnings de linter.

---

### 2. ✅ Error de Blob URLs en Imágenes del Chat

**Error**:
```
No suitable URL request handler found for blob:http://localhost:8081/894686a7-486a-4e7e-af2c-001b8603b493
```

**Causa raíz**:
- Mensajes antiguos en la base de datos contenían URIs en formato `blob:`
- Estas URLs son temporales y solo válidas durante la sesión del navegador
- Al recargar la app, React Native intentaba cargar imágenes con blob URLs que ya no existían
- Las imágenes se guardaban como URIs locales temporales en lugar de persistentes

**Solución aplicada**:

1. **Limpieza de datos existentes** (SQL):
```sql
-- Eliminar blob URLs de mensajes existentes (mantener el texto)
UPDATE messages
SET images = NULL
WHERE images::text LIKE '%blob:%';
```

Resultado: 3 mensajes limpiados en conversaciones:
- `1a682740-49e8-4927-b4ea-7c9d81bfaf9a` (2 mensajes)
- `fa00423f-804a-4732-b25c-8acfae1db6b4` (1 mensaje)

2. **Prevención futura** (código):
Agregado filtro en `app/(tabs)/chat.tsx` para ignorar blob URLs:

```typescript
const renderMessage = ({ item }: { item: Message }) => {
  const messageImages = item.images || (item.image ? [item.image] : []);

  // Filtrar blob URLs que ya no son válidas
  const validImages = messageImages.filter(uri =>
    uri && !uri.startsWith('blob:')
  );

  // Renderizar solo imágenes válidas
  {validImages.length > 0 && (
    <Image
      source={{ uri: imgUri }}
      onError={() => console.warn('Error cargando imagen:', imgUri)}
    />
  )}
}
```

**Archivos modificados**:
- `app/(tabs)/chat.tsx:346-370` - Agregado filtro y manejo de errores

---

## Investigación Realizada

### Verificación de Backend con MCP Supabase

Antes de hacer cambios, confirmé que el backend estaba 100% funcional:

1. **Tabla `profiles` existe** ✅
   - 2 usuarios registrados correctamente
   - Trigger automático `handle_new_user()` funcionando

2. **Políticas RLS correctas** ✅
   ```sql
   -- Usuarios pueden leer su propio perfil
   auth.uid() = id
   ```

3. **Logs de API exitosos** ✅
   - Login: `POST /auth/v1/token` → 200
   - Carga de perfil: `GET /rest/v1/profiles` → 200
   - Todas las peticiones funcionando correctamente

4. **Migraciones ejecutadas** ✅
   - `auth_system_consolidated` aplicada
   - Todas las tablas multi-tenant creadas

**Conclusión**: El problema NO estaba en Supabase, sino en el routing frontend.

---

## Decisiones Técnicas

### ¿Por qué filtrar blob URLs en lugar de solucionarlo completamente?

**Opción A** (implementada): Filtrar blob URLs
- ✅ Rápido de implementar
- ✅ No rompe funcionalidad existente
- ✅ Previene crashes
- ❌ Las imágenes antiguas se pierden (solo en mensajes legacy)

**Opción B** (no implementada): Subir imágenes a Supabase Storage
- ❌ Requiere cambios significativos en ChatContext
- ❌ Costo de storage adicional
- ✅ Imágenes persistentes entre sesiones
- ✅ Funciona en todos los dispositivos

**Decisión**: Opción A es suficiente porque:
1. Solo había 3 mensajes afectados (datos legacy)
2. El texto de los mensajes se preservó
3. Evita crashes futuros
4. Imágenes en chat son principalmente para análisis temporal, no archivo permanente

### ¿Por qué navegar a `/(tabs)/chat` en lugar de crear `/(tabs)/index.tsx`?

**Opción A** (implementada): `router.replace('/(tabs)/chat')`
- ✅ Un solo archivo modificado
- ✅ Comportamiento claro y predecible
- ✅ Chat es la pantalla principal de la app

**Opción B** (no implementada): Crear `app/(tabs)/index.tsx` que redirija
- ❌ Archivo adicional innecesario
- ❌ Doble redirect (index → chat)
- ❌ Más mantenimiento

---

## Testing Manual Realizado

1. ✅ `bun run lint` - Pasa sin errores ni warnings
2. ✅ Verificación de SQL - 3 mensajes limpiados
3. ✅ Logs de Supabase - Confirman backend funcional

---

## TODOs Pendientes

- [ ] Considerar implementar upload a Supabase Storage para imágenes del chat (si se convierte en requisito)
- [ ] Documentar en CLAUDE.md la limitación de blob URLs
- [ ] Agregar mensaje informativo al usuario si detecta imágenes que no se pueden cargar

---

## Notas para Futuras Sesiones

### Problema de Blob URLs en React Native

Las blob URLs (`blob:http://...`) **NO funcionan** en React Native porque:
1. Son referencias en memoria del navegador
2. Solo válidas durante la sesión actual
3. No se pueden serializar/guardar en base de datos
4. React Native no puede resolverlas

**Alternativas válidas**:
- `file://` - Archivos locales (temporal)
- `https://` - URLs públicas
- `data:image/base64,...` - Base64 inline (pequeñas)
- Supabase Storage URLs con signed tokens

### Debugging de Routing en Expo Router

Cuando aparece "Esta pantalla no existe":
1. Verificar que el archivo existe en `app/`
2. Revisar `app/_layout.tsx` para Stack.Screen config
3. Usar `npx expo customize tsconfig.json` para habilitar typed routes
4. Logs en `useSegments()` para ver ruta actual

### Uso de MCP Supabase para Debugging

Herramientas útiles:
- `list_tables` - Ver todas las tablas y columnas
- `list_migrations` - Confirmar migraciones aplicadas
- `execute_sql` - Queries de diagnóstico
- `get_logs` - Ver requests recientes
- `get_advisors` - Alertas de seguridad/performance

**Aprendizaje**: Siempre verificar backend primero antes de asumir que el problema está en migraciones/DB.
