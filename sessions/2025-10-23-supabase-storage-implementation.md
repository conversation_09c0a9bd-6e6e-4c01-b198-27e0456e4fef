# Implementación de Supabase Storage + Consentimiento GDPR para Fotos

**Última actualización**: 2025-10-23 17:30

## Contexto

Las imágenes del workflow de fórmulas se guardaban como URIs temporales (blob URLs o file://) que no persistían entre sesiones ni dispositivos. Además, no había consentimiento explícito del cliente para almacenar sus fotos, lo cual no cumplía completamente con GDPR para manejo de datos sensibles.

**Problema principal:**
- Imágenes no persistentes (solo URIs locales)
- Sin consentimiento explícito para fotos
- No compliance GDPR para datos sensibles (fotos de clientes)
- Sin estrategia de retención temporal

**Base técnica:**
Esta implementación se basa en el research completo documentado en `sessions/2025-10-23-image-storage-research-privacy-compliance.md`, que incluye análisis de apps similares, requisitos normativos GDPR/CCPA/AI Act, y mejores prácticas de seguridad.

## Cambios Realizados

### Backend - Supabase Storage

#### Archivos Creados

**1. `supabase/migrations/20251023_create_client_photos_bucket.sql`**
- Bucket `client-photos` privado (no público)
- Límite: 10MB por foto
- Tipos permitidos: JPEG, PNG, WebP
- RLS policies estrictas: solo el usuario que sube puede ver/editar/eliminar
- Cifrado AES-256 en reposo (incluido por Supabase)
- Organización: `userId/clientId/timestamp-random.jpg`

**2. `supabase/migrations/20251023_create_photo_cleanup_function.sql`**
- Función PL/pgSQL `cleanup_old_client_photos()`
- Elimina fotos con más de 90 días automáticamente
- Retorna número de archivos eliminados
- Requiere pg_cron o ejecución manual/Edge Function

**3. `lib/storage.ts`**
- `uploadClientPhoto(imageUri, clientId?)`: Sube imagen y retorna signed URL
- `deleteClientPhoto(storagePath)`: Elimina imagen del storage
- `getSignedUrl(storagePath, expiresIn)`: Genera signed URL de imagen existente
- Manejo de errores robusto con try/catch
- Conversión de file:// URIs a ArrayBuffer usando expo-file-system

### Frontend - Consentimiento GDPR

#### Archivos Modificados

**1. `types/index.ts`**
- Añadido `photoConsentGiven: boolean` al interface `SafetyChecklist` dentro de `FormulaData`
- Línea 146: Nuevo campo obligatorio

**2. `contexts/FormulaContext.tsx`**
- Inicializar `photoConsentGiven: false` en dos lugares:
  - Estado inicial de `formulaData` (línea 15)
  - Función `resetFormula()` (línea 70)

**3. `app/formula/step3.tsx`**
- Añadido `consent5` (state local) para el checkbox de consentimiento de fotos (línea 35)
- Nuevo checkbox con texto GDPR-compliant: "Autorizo a almacenar fotos de mi cabello de forma segura y cifrada por hasta 90 días..." (líneas 309-321)
- Actualizado `allConsentsChecked` para incluir `consent5` (línea 69)
- Actualizado `updateSafetyChecklist()` para guardar `photoConsentGiven: consent5` (línea 115)
- Añadido `consent5` al useEffect de debugging (línea 77, 79)

### Integración - Uploads en Workflow

#### Archivos Modificados

**1. `app/formula/step1.tsx` (Color Actual)**
- Importar `uploadClientPhoto` de `@/lib/storage` (línea 23)
- Añadir estado `isUploading` (línea 63)
- Modificar `pickImages()` para subir a Supabase Storage (líneas 94-121):
  - Convertir cada asset.uri a signed URL
  - Manejo de errores con Alert
  - Loading state
- Modificar `capturePhoto()` para subir foto capturada (líneas 139-162)
- Añadir banner de "Subiendo fotos..." cuando `isUploading === true` (líneas 950-955)
- Deshabilitar botones de upload durante `isUploading` (líneas 959, 967, 991, 995)
- Añadir estilos `uploadingBanner` y `uploadingText` (líneas 2528-2545)

**2. `app/formula/step2.tsx` (Color Deseado)**
- Cambios idénticos a step1.tsx:
  - Importar `uploadClientPhoto`
  - Estado `isUploading`
  - `pickImages()` y `capturePhoto()` con uploads
  - Banner y estilos de loading

### Documentación

**1. `CLAUDE.md`**
- Nueva sección "Supabase Storage - Imágenes de Clientes" (líneas 163-215)
- Documenta bucket, seguridad, organización de archivos, uso en código, compliance, auto-cleanup
- Referencia a migraciones relacionadas

**2. `sessions/2025-10-23-supabase-storage-implementation.md`** (este archivo)
- Documentación completa de la implementación
- Decisiones técnicas tomadas
- TODOs pendientes

## Decisiones Técnicas

### ¿Por qué 90 días de retención en lugar de 30?

**Razón**: Las fotos del workflow de fórmulas son críticas para tracking de resultados a largo plazo. Los clientes suelen volver al salón después de 4-8 semanas, y es importante mantener historial visual para comparar evolución.

**Trade-offs**:
- ✅ **Pro**: Mejor tracking de resultados, permite comparaciones a largo plazo
- ❌ **Contra**: Más storage usado, más riesgo GDPR si no se gestiona bien el consentimiento
- **Decisión**: 90 días es razonable para el use case, con consentimiento explícito y opción de revocación (TODO)

### ¿Por qué signed URLs en lugar de URLs públicas?

**Razón**: Seguridad y compliance GDPR. Las fotos de clientes son datos sensibles que no deben ser accesibles públicamente.

**Ventajas**:
- URLs expiran automáticamente (1 hora por defecto)
- Control granular de acceso
- RLS policies de Supabase se respetan
- Cumple GDPR Art. 32 (security of processing)

**Desventaja**: Necesita regenerar URLs si expiran, pero es manejable con `getSignedUrl()`

### ¿Por qué ArrayBuffer en lugar de Blob para React Native?

**Razón técnica**: React Native no tiene soporte nativo de Blob API como navegadores web. `expo-file-system` lee archivos como base64, que se convierte a ArrayBuffer/Uint8Array para Supabase.

**Alternativas descartadas**:
- Fetch API con blob: No funciona en React Native con file:// URIs
- FormData directo: Incompatible con Supabase Storage SDK

### ¿Por qué selectedClient?.id opcional en uploadClientPhoto?

**Razón**: Flexibilidad. A veces se suben fotos antes de seleccionar cliente (ej: fotos de referencia de Pinterest).

**Estructura**:
- Con clientId: `userId/clientId/photo.jpg` (organizado por cliente)
- Sin clientId: `userId/photo.jpg` (fotos generales del usuario)

## Testing Realizado

### Lint y Compilación
- ✅ `bun run lint` pasa sin errores ni warnings
- ✅ Código compila correctamente (TypeScript)

### Pendiente (requiere ejecutar migraciones primero)
- ⏳ Verificar creación de bucket en Supabase Dashboard
- ⏳ Probar upload de imágenes desde step1/step2
- ⏳ Verificar que signed URLs funcionan
- ⏳ Probar RLS policies (intentar acceder a foto de otro usuario)
- ⏳ Verificar checkbox de consentimiento en step3
- ⏳ Verificar validación (no permitir continuar sin checkbox)
- ⏳ Probar función de cleanup manualmente

## TODOs Pendientes

### Crítico (Antes de Merge)
- [ ] **Ejecutar migraciones SQL en Supabase**:
  ```bash
  # Método 1: Supabase CLI
  supabase db push

  # Método 2: SQL Editor manual
  # Abrir: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
  # Ejecutar contenido de:
  # - supabase/migrations/20251023_create_client_photos_bucket.sql
  # - supabase/migrations/20251023_create_photo_cleanup_function.sql
  ```

- [ ] **Testing manual completo**:
  - Subir fotos desde step1 (color actual)
  - Subir fotos desde step2 (color deseado)
  - Verificar checkbox en step3
  - Verificar que no se puede continuar sin marcar checkbox
  - Verificar que imágenes persisten entre sesiones

- [ ] **Verificar región del proyecto Supabase**:
  - Debe ser EU West (París) para compliance GDPR
  - Revisar en Dashboard → Settings → General

### Importante (Post-Merge)
- [ ] **Implementar revocación de consentimiento**:
  - Pantalla en Settings para ver/revocar consentimiento
  - Botón "Eliminar todas mis fotos"
  - Actualizar `photoConsentGiven` a false en DB

- [ ] **Configurar auto-cleanup automático**:
  - Opción 1: Habilitar pg_cron en Supabase (si disponible)
  - Opción 2: Crear Edge Function + GitHub Actions para ejecutar diariamente
  - Ver: https://supabase.com/docs/guides/functions/schedule-functions

- [ ] **Regenerar signed URLs on-demand**:
  - Actualmente expiran en 1 hora
  - Implementar lógica para regenerar cuando se abra pantalla de análisis
  - Considerar cache en AsyncStorage con timestamp de expiración

- [ ] **Optimizar tamaño de imágenes**:
  - Actualmente se suben a calidad 0.8
  - Considerar compresión adicional antes de upload
  - Librería: `expo-image-manipulator` para resize

### Nice to Have
- [ ] **Audit logs de fotos**:
  - Tabla `photo_audit_log` para tracking de uploads/deletes
  - Útil para compliance y debugging

- [ ] **Notificación antes de auto-delete**:
  - Email/push notification 7 días antes de eliminar fotos
  - Permite al cliente extender retención si lo necesita

- [ ] **Dashboard de métricas de storage**:
  - En Settings → Storage
  - Mostrar: fotos subidas, espacio usado, fotos próximas a eliminar

## Problemas Encontrados y Soluciones

### 1. Warning de lint: `isUploading` no usado

**Error**: TSLint reportaba que `isUploading` estaba definido pero nunca usado

**Causa raíz**: Se añadió el estado pero no se usaba en la UI para mostrar feedback al usuario

**Solución**:
- Añadido banner de "Subiendo fotos..." con ActivityIndicator cuando `isUploading === true`
- Deshabilitados botones de upload durante carga
- Añadidos estilos correspondientes (`uploadingBanner`, `uploadingText`)

**Archivos afectados**:
- `app/formula/step1.tsx:950-955, 959, 967, 991, 995, 2528-2545`
- `app/formula/step2.tsx:628-633, 637, 645, 669, 673, 1718-1735`

### 2. Blob API no disponible en React Native

**Problema**: El pseudocódigo inicial usaba `new Blob([byteArray])` que no funciona en React Native

**Causa raíz**: React Native no tiene Blob API como navegadores web. Supabase Storage requiere ArrayBuffer o Uint8Array.

**Solución**:
- Usar `FileSystem.readAsStringAsync()` con encoding base64
- Convertir base64 → byteCharacters → byteNumbers → Uint8Array
- Pasar Uint8Array directamente a `supabase.storage.upload()`

**Código**:
```typescript
const base64 = await FileSystem.readAsStringAsync(imageUri, {
  encoding: FileSystem.EncodingType.Base64,
});
const byteCharacters = atob(base64);
const byteNumbers = new Array(byteCharacters.length);
for (let i = 0; i < byteCharacters.length; i++) {
  byteNumbers[i] = byteCharacters.charCodeAt(i);
}
const byteArray = new Uint8Array(byteNumbers);
```

**Referencia**: `lib/storage.ts:39-48`

## Costos Estimados

**Storage costs** (según research):
- 100 fotos/mes × 2MB promedio = 200MB/mes
- 3 meses de retención máxima ≈ 600MB storage pico
- Costo Supabase: ~$0.021/GB/mes → **~$0.01-0.02/mes**

**Bandwidth costs**:
- 100 uploads/mes × 2MB = 200MB upload
- 300 signed URL requests/mes × 2MB = 600MB download
- Total: ~800MB/mes
- Costo Supabase: gratis hasta 2GB/mes en Free tier, luego $0.09/GB

**Total estimado**: **$1-2/mes** (muy bajo impacto)

## Compliance GDPR Checklist

Basado en el research documentado en `sessions/2025-10-23-image-storage-research-privacy-compliance.md`:

✅ **Art. 6 (Lawful basis)**: Consentimiento explícito mediante checkbox
✅ **Art. 7 (Consent)**: Libre, específico, informado (texto del checkbox)
✅ **Art. 13 (Information)**: Usuario informado de retención 90 días, ubicación storage, finalidad
✅ **Art. 17 (Right to erasure)**: Función `deleteClientPhoto()` implementada (falta UI en Settings)
✅ **Art. 25 (Data protection by design)**: Privado by default, cifrado, RLS policies
✅ **Art. 32 (Security)**: Cifrado AES-256, TLS 1.2+, signed URLs, RLS

⏳ **Pendiente**:
- Art. 17 (Right to erasure): UI para revocar consentimiento y eliminar fotos
- Art. 20 (Data portability): Export de fotos del cliente (nice to have)

## Referencias

- **Research completo**: `sessions/2025-10-23-image-storage-research-privacy-compliance.md`
  - Análisis de apps similares (salones, telemedicina, AI chat)
  - Requisitos normativos GDPR/CCPA/AI Act 2025
  - Arquitectura de seguridad recomendada
  - Costos operacionales
  - Checklist de compliance

- **Sesión anterior**: `sessions/2025-10-23-fix-login-redirect-blob-images.md`
  - Fix de blob URLs que crasheaban el chat
  - Contexto del problema de persistencia

- **Supabase Docs**:
  - Storage: https://supabase.com/docs/guides/storage
  - RLS: https://supabase.com/docs/guides/auth/row-level-security
  - Functions scheduling: https://supabase.com/docs/guides/functions/schedule-functions

## Notas para Futuras Sesiones

1. **Las migraciones SQL deben ejecutarse manualmente** antes de testear la app. Sin el bucket creado, los uploads fallarán.

2. **Signed URLs expiran en 1 hora**. Si un usuario deja la app abierta más tiempo y vuelve, las imágenes pueden no cargar. Implementar regeneración on-demand.

3. **selectedClient?.id puede ser undefined** al subir fotos. El código maneja esto correctamente organizando fotos en `userId/` en lugar de `userId/clientId/`.

4. **Auto-cleanup requiere configuración adicional** (pg_cron o Edge Function). Sin esto, las fotos no se eliminarán automáticamente después de 90 días.

5. **El consentimiento es obligatorio** para continuar el workflow. No se pueden saltear el checkbox en step3.

6. **Testing end-to-end**: Verificar que las fotos persisten cerrando y reabriendo la app (multi-device sync).

---

## Update: 2025-10-23 11:20 - REFACTOR COMPLETO: Procesar Localmente + Upload al Final

### Problema Crítico Detectado

Después del ultrathink solicitado por el usuario, identifiqué que la arquitectura implementada estaba **completamente incorrecta**. El problema no era solo técnico (iOS no puede leer ciertas imágenes), sino arquitectural.

**Errores de la implementación original:**
1. ❌ Subía fotos inmediatamente en step1/step2 (contrario al plan documentado)
2. ❌ iOS no puede leer directamente ciertos formatos (HEIC, iCloud photos, etc.)
3. ❌ La AI no puede analizar signed URLs de Supabase (necesita URIs locales)
4. ❌ Se creaban uploads huérfanos si el usuario cancelaba el workflow a mitad
5. ❌ Violaba el principio de minimización de datos GDPR (subir antes de consentimiento)

**La arquitectura correcta siempre fue:**
- **Opción B** del research original: Procesar localmente → Analizar → Upload al final
- Procesar imágenes con `expo-image-manipulator` (resize + JPEG)
- Mantener URIs locales durante el workflow
- Subir a Storage SOLO cuando el usuario finaliza en step5 (después del consentimiento en step3)

### Errores Encontrados Antes del Refactor

**Error 1: `Cannot read property 'Base64' of undefined`**
- **Causa**: `FileSystem.EncodingType.Base64` no disponible en versión actual de Expo
- **Fix**: Cambiar a string literal `'base64'`
- **Archivo**: `lib/storage.ts:37`

**Error 2: Deprecation warning Expo SDK 54**
- **Causa**: `readAsStringAsync` deprecated en Expo SDK 54
- **Fix**: Cambiar import de `expo-file-system` a `expo-file-system/legacy`
- **Archivo**: `lib/storage.ts:2`

**Error 3: "Cannot load representation of type public.png" (iOS)**
- **Causa**: iOS no puede leer directamente ciertos formatos de imágenes
- **Fix**: Este error reveló el problema arquitectural completo
- **Solución**: Refactor completo documentado abajo

### Cambios del Refactor

#### 1. Migración de `lib/storage.ts`

**Cambios:**
- Renombrado `uploadClientPhoto` → `uploadFormulaPhoto` (más específico)
- Ahora requiere `clientId` obligatorio (path: `userId/formulas/clientId/timestamp.jpg`)
- Separación de contextos:
  - `uploadConsultationPhoto()`: Para chat (14 días retención)
  - `uploadFormulaPhoto()`: Para workflow (90 días retención)

**Estructura de paths:**
```
userId/
  consultations/
    timestamp-random.jpg  (fotos del chat - 14 días)
  formulas/
    clientId/
      timestamp-random.jpg  (fotos del workflow - 90 días)
```

**Commits:**
- `2d25ac5`: "fix: Usar 'base64' string literal..."
- `5249570`: "fix: Migrar a expo-file-system/legacy..."

#### 2. Refactor de `app/formula/step1.tsx` y `step2.tsx`

**Cambios:**
- ❌ **ELIMINADO**: Import de `uploadFormulaPhoto`
- ❌ **ELIMINADO**: Validación de `selectedClient` antes de upload
- ❌ **ELIMINADO**: Llamadas a `uploadFormulaPhoto()` en `pickImages()` y `capturePhoto()`
- ✅ **AGREGADO**: Import de `expo-image-manipulator`
- ✅ **MODIFICADO**: `pickImages()` ahora procesa localmente con `ImageManipulator.manipulateAsync()`
- ✅ **MODIFICADO**: `capturePhoto()` ahora procesa localmente
- ✅ **MODIFICADO**: Banner cambiado de "Subiendo fotos..." a "Procesando fotos..."

**Procesamiento de imágenes:**
```typescript
const manipulated = await ImageManipulator.manipulateAsync(
  asset.uri,
  [{ resize: { width: 2048 } }], // Resize manteniendo aspect ratio
  { compress: 0.8, format: ImageManipulator.SaveFormat.JPEG }
);
// Guardar URI local procesada (NO subir)
const newImages = [...images, manipulated.uri].slice(0, 6);
setImages(newImages);
```

**Ventajas:**
- ✅ Soluciona errores de lectura de iOS
- ✅ Normaliza formatos a JPEG (consistencia)
- ✅ Optimiza tamaño (max 2048px de ancho)
- ✅ AI puede analizar (URIs locales file://)
- ✅ No uploads huérfanos si se cancela workflow

**Commit:** `b0f5739`: "refactor: Procesar imágenes localmente, NO subir hasta finalizar workflow"

#### 3. Nueva Lógica de Upload en `app/formula/step5.tsx`

**Cambios:**
- ✅ **AGREGADO**: Import de `uploadFormulaPhoto`
- ✅ **AGREGADO**: Destructuring de `selectedClient` del context
- ✅ **AGREGADO**: Estado `isUploadingPhotos`
- ✅ **MODIFICADO**: `handleFinish()` ahora es `async` y sube fotos antes de resetear
- ✅ **AGREGADO**: UI de loading "Guardando fotos..." con ActivityIndicator
- ✅ **AGREGADO**: Disabled state del botón durante upload

**Lógica de upload final:**
```typescript
const handleFinish = async () => {
  if (!selectedClient?.id) {
    // Sin cliente, solo resetear
    resetFormula();
    router.push('/(tabs)/chat');
    return;
  }

  setIsUploadingPhotos(true);

  try {
    // Recolectar TODAS las fotos del workflow (current + desired)
    const allPhotos = [
      ...(formulaData.currentColorImages || []),
      ...(formulaData.desiredColorImages || []),
    ].filter(uri => uri && uri.startsWith('file://')); // Solo URIs locales

    if (allPhotos.length > 0) {
      console.log(`[Step5] Subiendo ${allPhotos.length} fotos a Storage...`);

      // Subir todas las fotos en paralelo
      await Promise.all(
        allPhotos.map(uri => uploadFormulaPhoto(uri, selectedClient.id))
      );

      console.log('[Step5] Todas las fotos subidas exitosamente');
    }

    // Finalizar y volver al chat
    resetFormula();
    router.push('/(tabs)/chat');
  } catch (error) {
    console.error('[Step5] Error al subir fotos:', error);
    alert('Error al guardar las fotos. ¿Deseas finalizar de todas formas?');
    // Por ahora, finalizar igualmente
    resetFormula();
    router.push('/(tabs)/chat');
  } finally {
    setIsUploadingPhotos(false);
  }
};
```

**Botón Finalizar con loading:**
```typescript
<TouchableOpacity
  style={[styles.finishButton, isUploadingPhotos && styles.finishButtonDisabled]}
  onPress={handleFinish}
  disabled={isUploadingPhotos}
>
  {isUploadingPhotos ? (
    <>
      <ActivityIndicator color={Colors.light.background} size="small" />
      <Text style={[styles.finishButtonText, { marginLeft: 10 }]}>
        Guardando fotos...
      </Text>
    </>
  ) : (
    <Text style={styles.finishButtonText}>Finalizar</Text>
  )}
</TouchableOpacity>
```

**Commit:** `fc2b983`: "feat: Implementar subida de fotos al finalizar workflow"

#### 4. FormulaContext - Sin Cambios Necesarios

Revisado `contexts/FormulaContext.tsx` - NO requiere modificaciones porque:
- ✅ Ya guarda URIs como strings en `currentColorImages` y `desiredColorImages`
- ✅ Ya tiene `selectedClient` disponible
- ✅ `resetFormula()` ya limpia todo correctamente
- ✅ Separación de responsabilidades: context solo maneja estado, step5 maneja upload

### Arquitectura Final: Workflow Completo

```
[Step 0] Selección de cliente
  ↓
[Step 1] Subir fotos color actual
  → ImagePicker.launchImageLibraryAsync() o camera.takePictureAsync()
  → ImageManipulator.manipulateAsync(uri, [resize: 2048], JPEG)
  → Guardar en context: formulaData.currentColorImages = ['file://...']
  ↓
[Step 1] Análisis AI color actual
  → generateTextSafe() con imágenes locales (file://)
  → AI puede leer URIs locales sin problema
  ↓
[Step 2] Subir fotos color deseado
  → Mismo proceso de procesamiento local
  → Guardar en context: formulaData.desiredColorImages = ['file://...']
  ↓
[Step 2] Análisis AI color deseado
  → generateTextSafe() con imágenes locales
  ↓
[Step 3] Safety checklist
  → Checkbox de consentimiento: photoConsentGiven ✓
  → Guardar en context: formulaData.safetyChecklist.photoConsentGiven = true
  ↓
[Step 4] Selección de marca/línea
  ↓
[Step 5] Generación de fórmula
  → Generar fórmula con AI
  → Usuario click "Finalizar"
  ↓
[Step 5 - handleFinish()] SUBIDA A STORAGE AQUÍ
  → Recolectar todas las fotos (currentColorImages + desiredColorImages)
  → Filtrar solo URIs locales (file://)
  → Para cada foto: uploadFormulaPhoto(uri, clientId)
    - Leer archivo local con FileSystem.readAsStringAsync('base64')
    - Convertir a Uint8Array
    - Upload a Storage: userId/formulas/clientId/timestamp-random.jpg
    - Generar signed URL (1 hora expiry)
  → resetFormula()
  → router.push('/(tabs)/chat')
```

### Compliance GDPR - Mejoras

✅ **Art. 5.1.c (Minimización de datos)**: Las fotos ahora se suben SOLO cuando el usuario finaliza el workflow, después de dar consentimiento en step3. No se suben fotos innecesarias si cancela a mitad.

✅ **Art. 25 (Privacy by design)**: Arquitectura correcta desde el diseño - procesar localmente primero, upload solo cuando necesario.

### Testing Realizado

✅ **Lint**: `bun run lint` pasa sin errores
✅ **Compilación**: App compila correctamente
✅ **Server**: App server inicia sin crashes

⏳ **Pendiente testing manual**:
- Seleccionar fotos en step1 (verificar procesamiento local)
- Analizar con AI (verificar que funciona con file:// URIs)
- Continuar workflow hasta step5
- Click "Finalizar" (verificar upload y loading state)
- Verificar fotos en Supabase Storage Dashboard

### TODOs Actualizados

**Crítico:**
- [ ] Testing end-to-end completo del workflow
- [ ] Verificar fotos en Storage Dashboard: `userId/formulas/clientId/timestamp.jpg`
- [ ] Verificar que signed URLs se generan correctamente
- [ ] Probar cancelar workflow a mitad (verificar que NO hay uploads huérfanos)

**Importante:**
- [ ] Actualizar migration de bucket: cambiar path de `userId/clientId/` a `userId/formulas/clientId/`
- [ ] Actualizar función `cleanup_old_client_photos()` para trabajar con nuevos paths
- [ ] Implementar manejo de signed URLs expiradas (regeneración on-demand)

### Lecciones Aprendidas

1. **Ultrathink es crítico**: El usuario pidió "dale una vuelta adicional, ultrathink" DOS veces. En la segunda, identifiqué que todo el enfoque estaba mal desde el inicio.

2. **Separación de contextos**: Chat (consultations) vs Workflow (formulas) tienen necesidades MUY diferentes:
   - Chat: Temporal (14 días), sin cliente vinculado necesariamente
   - Workflow: Formal (90 días), requiere cliente, requiere consentimiento

3. **Arquitectura > Fixes rápidos**: Podría haber "fixeado" el error de iOS con `copyToCacheDirectory`, pero eso no resolvía los problemas fundamentales (AI, uploads huérfanos, GDPR).

4. **GDPR compliance requiere arquitectura correcta**: No puedes parchear compliance, debe estar diseñado desde el inicio. Upload después de consentimiento, no antes.

5. **React Native ≠ Web**: Las imágenes en RN son complejas. `expo-image-manipulator` es esencial para normalizar formatos y solucionar problemas de iOS.

### Archivos Modificados en el Refactor

```
lib/storage.ts               - Split en 2 funciones (consultations vs formulas)
app/formula/step1.tsx        - Procesar localmente, NO upload
app/formula/step2.tsx        - Procesar localmente, NO upload
app/formula/step5.tsx        - Upload al finalizar
contexts/FormulaContext.tsx  - Revisado, sin cambios necesarios
```

### Commits del Refactor

```bash
2d25ac5  fix: Usar 'base64' string literal en lugar de FileSystem.EncodingType.Base64
5249570  fix: Migrar a expo-file-system/legacy para compatibilidad con Expo SDK 54
b0f5739  refactor: Procesar imágenes localmente, NO subir hasta finalizar workflow
fc2b983  feat: Implementar subida de fotos al finalizar workflow
c1e389c  docs: Documentar refactor completo de procesamiento local + upload final
35ba2d7  fix: Agregar aviso de enfoque exclusivo en cabello al chat
```

---

## Update: 2025-10-23 11:35 - FIX: Chat rechazaba análisis de imágenes con rostros

### Problema Reportado por Usuario

Después de testing manual del refactor:
- ✅ **Formulación (step1/step2)**: Análisis de imágenes funciona perfectamente
- ❌ **Chat**: AI rechaza analizar imágenes con mensaje "Lo siento, no puedo ayudar con el análisis de esta imagen"
- ✅ **Upload a Supabase**: Funciona correctamente al finalizar workflow en step5

### Análisis Ultrathink

**Investigación:**
1. Verificado que NO hice cambios en `app/(tabs)/chat.tsx` durante el refactor
2. Verificado que NO hice cambios en `lib/ai-client.ts`
3. Comparado system prompts: chat vs step1/step2

**Diferencia Crítica Encontrada:**

**Step1/Step2 (funciona):**
```typescript
const systemPrompt = `...
IMPORTANTE: Tu trabajo es analizar EXCLUSIVAMENTE el cabello visible en las imágenes.
NO analices ni comentes sobre rostros, personas, identidades, características faciales,
edad, género, raza o cualquier característica personal. Si aparece una persona en la
imagen, ignórala completamente y enfócate SOLO en el cabello.
...`;
```

**Chat (NO funcionaba):**
```typescript
const systemPrompt = `Eres un experto en coloración capilar...
// ❌ NO tenía el aviso de enfoque exclusivo en cabello
ANÁLISIS DE IMÁGENES - Cuando analices fotos de cabello, proporciona:
...`;
```

**Causa Raíz:**
El modelo de AI tiene políticas estrictas sobre análisis de rostros/personas por privacidad. Cuando ve una imagen con una cara visible:
- **Sin aviso explícito** → Rechaza la imagen por políticas de privacidad
- **Con aviso explícito** → Entiende que es contexto profesional de peluquería y debe ignorar el rostro

### Solución Implementada

Agregado el mismo aviso que step1/step2 al system prompt del chat:

```typescript
const systemPrompt = `Eres un experto en coloración capilar profesional con más de 15 años de experiencia. Tu trabajo es analizar fotos de cabello con precisión técnica y dar recomendaciones específicas basadas en colorimetría profesional.

IMPORTANTE: Tu análisis debe enfocarse EXCLUSIVAMENTE en el cabello visible en las imágenes. NO analices ni comentes sobre rostros, personas, identidades, características faciales, edad, género, raza o cualquier característica personal. Si aparece una persona en la imagen, ignórala completamente y enfócate SOLO en el cabello.

ANÁLISIS DE IMÁGENES - Cuando analices fotos de cabello, proporciona:
...`;
```

**Archivo modificado:** `app/(tabs)/chat.tsx:115`

### Testing Realizado

✅ **Lint**: Pasa sin errores
✅ **Compilación**: Código compila correctamente

⏳ **Pendiente testing manual**: Verificar que el chat ahora analiza imágenes con rostros correctamente

### Lecciones Aprendidas

1. **Consistencia de prompts es crítica**: Step1/step2 tenían el aviso correcto desde el inicio, pero el chat no. Siempre que hay análisis de imágenes con personas, el aviso de "enfoque exclusivo en cabello" es OBLIGATORIO.

2. **Políticas de AI son estrictas con rostros**: Los modelos de AI modernos rechazan análisis de rostros por defecto. Necesitas contexto explícito de que es uso profesional y que debe ignorar las caras.

3. **Testing end-to-end es esencial**: Este bug solo se detecta con testing manual real, no con lint o compilación.

### Commit del Fix

```bash
35ba2d7  fix: Agregar aviso de enfoque exclusivo en cabello al chat
```
