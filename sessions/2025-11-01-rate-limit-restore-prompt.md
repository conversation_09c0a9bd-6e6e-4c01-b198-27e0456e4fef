# Sesión: Restaurar límites de IA

## Contexto
- `lib/ai-client.ts` queda sin throttle por defecto (0 ms salvo que `EXPO_PUBLIC_AI_MIN_REQUEST_INTERVAL_MS` se ajuste).
- La edge function `ai-proxy` ignora el rate limiting porque `SHOULD_BYPASS_RATE_LIMIT` está forzado a `true`.
- La función SQL `check_rate_limit` incrementa contadores pero siempre devuelve `true`.

## Prompt para reactivar los límites
```
Necesito volver a activar el rate limiting en Salonier AI. Restaura el intervalo mínimo del cliente en 2000 ms por defecto, habilita otra vez `check_rate_limit` en `supabase/functions/ai-proxy/index.ts` y en la función SQL, y despliega la edge function para que `check_rate_limit` vuelva a ejecutarse. Asegúrate de que las respuestas de la IA vuelven a devolver el error de límite cuando corresponde.
```

## Pasos manuales recomendados
1. Definir `EXPO_PUBLIC_AI_MIN_REQUEST_INTERVAL_MS=2000` en `.env` (o borrar la variable si estaba en 0).
2. Editar `supabase/functions/ai-proxy/index.ts` para que `SHOULD_BYPASS_RATE_LIMIT` dependa de `RATE_LIMIT_MODE`.
3. Restaurar los `if` en `public.check_rate_limit` que devuelven `false` cuando se exceden los límites.
4. Ejecutar `supabase functions deploy ai-proxy --project-ref guyxczavhtemwlrknqpm`.
5. Validar desde la app que se vuelve a mostrar el error `Has excedido el límite de uso`.
