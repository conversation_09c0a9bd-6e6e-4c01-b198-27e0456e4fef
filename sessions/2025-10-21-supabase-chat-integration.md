# Integración de ChatContext con Supabase

**Última actualización**: 2025-10-21 18:55

## Contexto

Refactorización completa de `ChatContext.tsx` para usar Supabase como fuente de verdad en lugar de AsyncStorage, siguiendo el mismo patrón implementado previamente en `ClientContext.tsx`.

**Objetivo**: Preparar la app para sincronización multi-device y colaboración, con persistencia en base de datos PostgreSQL.

## Cambios Realizados

### 1. Nueva Migración de Supabase

**Archivo creado**: `supabase/migrations/20251021180000_create_conversations_tables.sql`

#### Tabla `conversations`
```sql
CREATE TABLE conversations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title TEXT NOT NULL DEFAULT 'Nueva conversación',
  is_pinned BOOLEAN NOT NULL DEFAULT false,
  created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

#### Tabla `messages`
```sql
CREATE TABLE messages (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  conversation_id UUID NOT NULL REFERENCES conversations(id) ON DELETE CASCADE,
  role TEXT NOT NULL CHECK (role IN ('user', 'assistant')),
  content TEXT NOT NULL,
  images JSONB,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW()
);
```

**Características**:
- ✅ Relación FK: `messages.conversation_id → conversations.id`
- ✅ Cascade Delete: Eliminar conversación elimina todos sus mensajes
- ✅ Índices optimizados: `updated_at`, `is_pinned`, `conversation_id`, `timestamp`
- ✅ Row Level Security (RLS) habilitado
- ✅ Trigger automático para `updated_at` en conversaciones
- ✅ Check constraint en `messages.role` (solo 'user' o 'assistant')

### 2. Refactorización de `contexts/ChatContext.tsx`

#### Cambios de Imports
```diff
- import AsyncStorage from '@react-native-async-storage/async-storage';
+ import { supabase } from '@/lib/supabase';
- const STORAGE_KEY = '@salonier_conversations';
```

#### Nuevas Funciones Helper

**`parseMessage(msg: any): Message`**
- Convierte datos de Supabase a formato TypeScript
- Mapea `images` (puede ser null) a `undefined` si no hay imágenes
- Convierte timestamps ISO string a objetos Date

**`createInitialConversation(): Promise<Conversation>` (refactorizado)**
- ANTES: Función síncrona que retornaba objeto Conversation
- AHORA: Función asíncrona que:
  1. Inserta conversación en tabla `conversations`
  2. Inserta mensajes iniciales en tabla `messages`
  3. Retorna objeto Conversation completo

**`loadConversations(): Promise<void>` (nuevo)**
- Reemplaza lógica de `AsyncStorage.getItem()`
- Carga conversaciones desde Supabase (ORDER BY updated_at DESC)
- Para cada conversación, carga sus mensajes (ORDER BY timestamp ASC)
- Si no hay conversaciones, crea una inicial automáticamente
- Manejo robusto de errores con fallback

#### Funciones CRUD Refactorizadas

Todas las funciones CRUD cambiaron de síncronas a asíncronas y siguen el patrón:
1. Operación en Supabase
2. `await loadConversations()` para refrescar estado local

**`startNewConversation()` → `async`**
```typescript
// ANTES (líneas 113-118)
const startNewConversation = useCallback(() => {
  const newConv = createNewConversation();
  setConversations(prev => [newConv, ...prev]);
  setCurrentConversationId(newConv.id);
  return newConv.id;
}, [createNewConversation]);

// AHORA (líneas 175-189)
const startNewConversation = useCallback(async () => {
  try {
    console.log('Creating new conversation in Supabase...');
    const newConv = await createInitialConversation();
    await loadConversations();
    setCurrentConversationId(newConv.id);
    return newConv.id;
  } catch (error) {
    console.error('Error starting new conversation:', error);
    throw error;
  }
}, [createInitialConversation, loadConversations]);
```

**`renameConversation(id, newTitle)` → `async`**
```typescript
// ANTES (líneas 124-132)
const renameConversation = useCallback((id: string, newTitle: string) => {
  setConversations(prev =>
    prev.map(conv =>
      conv.id === id ? { ...conv, title: newTitle, updatedAt: new Date() } : conv
    )
  );
}, []);

// AHORA (líneas 195-216)
const renameConversation = useCallback(async (id: string, newTitle: string) => {
  try {
    console.log('Renaming conversation in Supabase:', id);
    const { error } = await supabase
      .from('conversations')
      .update({ title: newTitle })
      .eq('id', id);

    if (error) throw error;
    await loadConversations();
  } catch (error) {
    console.error('Error renaming conversation:', error);
    throw error;
  }
}, [loadConversations]);
```

**`togglePinConversation(id)` → `async`**
```typescript
// ANTES (líneas 134-147)
const togglePinConversation = useCallback((id: string) => {
  setConversations(prev => {
    const updated = prev.map(conv =>
      conv.id === id ? { ...conv, isPinned: !conv.isPinned, updatedAt: new Date() } : conv
    );
    return updated.sort((a, b) => {
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;
      return b.updatedAt.getTime() - a.updatedAt.getTime();
    });
  });
}, []);

// AHORA (líneas 218-246)
const togglePinConversation = useCallback(async (id: string) => {
  try {
    const conversation = conversations.find(conv => conv.id === id);
    if (!conversation) throw new Error('Conversation not found');

    const { error } = await supabase
      .from('conversations')
      .update({ is_pinned: !conversation.isPinned })
      .eq('id', id);

    if (error) throw error;
    await loadConversations(); // Incluye reordenamiento automático
  } catch (error) {
    console.error('Error toggling pin:', error);
    throw error;
  }
}, [conversations, loadConversations]);
```

**`deleteConversation(id)` → `async`**
```typescript
// ANTES (líneas 149-166)
const deleteConversation = useCallback((id: string) => {
  setConversations(prev => {
    const filtered = prev.filter(conv => conv.id !== id);
    if (currentConversationId === id) {
      if (filtered.length > 0) {
        setCurrentConversationId(filtered[0].id);
      } else {
        const newConv = createNewConversation();
        setConversations([newConv]);
        setCurrentConversationId(newConv.id);
        return [newConv];
      }
    }
    return filtered;
  });
}, [currentConversationId, createNewConversation]);

// AHORA (líneas 248-283)
const deleteConversation = useCallback(async (id: string) => {
  try {
    console.log('Deleting conversation from Supabase:', id);
    const { error } = await supabase
      .from('conversations')
      .delete()
      .eq('id', id); // Cascade delete elimina mensajes automáticamente

    if (error) throw error;
    await loadConversations();

    // Manejar conversación actual
    if (currentConversationId === id) {
      const remaining = conversations.filter(conv => conv.id !== id);
      if (remaining.length > 0) {
        setCurrentConversationId(remaining[0].id);
      } else {
        const newConv = await createInitialConversation();
        await loadConversations();
        setCurrentConversationId(newConv.id);
      }
    }
  } catch (error) {
    console.error('Error deleting conversation:', error);
    throw error;
  }
}, [currentConversationId, conversations, createInitialConversation, loadConversations]);
```

**`addMessage(conversationId, message)` → `async`**
```typescript
// ANTES (líneas 168-187)
const addMessage = useCallback((conversationId: string, message: Message) => {
  setConversations(prev =>
    prev.map(conv => {
      if (conv.id === conversationId) {
        const newMessages = [...conv.messages, message];
        const newTitle = newMessages.length === 2 && message.role === 'user'
          ? message.content.slice(0, 50) + (message.content.length > 50 ? '...' : '')
          : conv.title;

        return {
          ...conv,
          messages: newMessages,
          title: newTitle,
          updatedAt: new Date(),
        };
      }
      return conv;
    })
  );
}, []);

// AHORA (líneas 285-340)
const addMessage = useCallback(async (conversationId: string, message: Message) => {
  try {
    console.log('Adding message to Supabase for conversation:', conversationId);

    // 1. Insertar mensaje
    const { error: messageError } = await supabase
      .from('messages')
      .insert({
        conversation_id: conversationId,
        role: message.role,
        content: message.content,
        images: message.images || null,
        timestamp: message.timestamp.toISOString(),
      });

    if (messageError) throw messageError;

    // 2. Auto-titular conversación si es primer mensaje de usuario
    const conversation = conversations.find(conv => conv.id === conversationId);
    if (conversation && conversation.messages.length === 1 && message.role === 'user') {
      const newTitle = message.content.slice(0, 50) +
        (message.content.length > 50 ? '...' : '');

      await supabase
        .from('conversations')
        .update({ title: newTitle })
        .eq('id', conversationId);
    } else {
      // 3. Solo actualizar timestamp de conversación
      await supabase
        .from('conversations')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', conversationId);
    }

    // 4. Refrescar estado local
    await loadConversations();
  } catch (error) {
    console.error('Error adding message:', error);
    throw error;
  }
}, [conversations, loadConversations]);
```

#### useEffect Actualizado
```diff
- useEffect(() => {
-   let mounted = true;
-   const load = async () => { /* AsyncStorage logic */ };
-   load();
-   return () => { mounted = false; };
- }, []);

+ useEffect(() => {
+   loadConversations();
+ }, [loadConversations]);
```

### 3. Actualización de `app/(tabs)/chat.tsx`

Todos los llamados a funciones del ChatContext ahora usan `await` porque son asíncronas.

#### Función `handleSend()` - YA era async
```diff
  const currentInput = inputText.trim();
  const currentImages = [...selectedImages];

- addMessage(currentConversationId, userMessage);
+ await addMessage(currentConversationId, userMessage);
  setInputText('');
  setSelectedImages([]);
  setIsLoading(true);

  try {
    // ... lógica de AI ...

    const aiResponse: Message = { /* ... */ };
-   addMessage(currentConversationId, aiResponse);
+   await addMessage(currentConversationId, aiResponse);
  } catch (error) {
    const errorResponse: Message = { /* ... */ };
-   addMessage(currentConversationId, errorResponse);
+   await addMessage(currentConversationId, errorResponse);
  }
```

#### Otras funciones actualizadas
```diff
- const handleNewConversation = () => {
+ const handleNewConversation = async () => {
-   startNewConversation();
+   await startNewConversation();
    setShowConversations(false);
  };

- const handleSaveEdit = () => {
+ const handleSaveEdit = async () => {
    if (editingConversationId && editingTitle.trim()) {
-     renameConversation(editingConversationId, editingTitle.trim());
+     await renameConversation(editingConversationId, editingTitle.trim());
    }
    setEditingConversationId(null);
    setEditingTitle('');
  };

  const handleDeleteConversation = (id: string, title: string) => {
    Alert.alert(
      'Eliminar conversación',
      `¿Estás seguro de que quieres eliminar "${title}"?`,
      [
        { text: 'Cancelar', style: 'cancel' },
        {
          text: 'Eliminar',
          style: 'destructive',
-         onPress: () => deleteConversation(id),
+         onPress: async () => await deleteConversation(id),
        },
      ]
    );
  };
```

#### TouchableOpacity actualizado
```diff
  <TouchableOpacity
-   onPress={() => togglePinConversation(item.id)}
+   onPress={async () => await togglePinConversation(item.id)}
    style={styles.actionButton}
  >
```

## Problemas Encontrados y Soluciones

### 1. **Migración no ejecutada**
**Error**: No se pudo ejecutar `supabase db push` por falta de credenciales y Docker no corriendo.

**Solución temporal**:
- Migración SQL creada y lista en `supabase/migrations/20251021180000_create_conversations_tables.sql`
- Usuario necesita ejecutarla manualmente con:
  ```bash
  supabase db push
  # O desde Dashboard: Database > Migrations > Run migration
  ```

### 2. **Bun no disponible en sistema**
**Error**: `bun not found` al intentar ejecutar scripts de package.json

**Solución**:
- Scripts usan `bunx rork start` que es específico de Rork toolkit
- Usuario puede ejecutar localmente con bun instalado
- Código TypeScript verificado manualmente por corrección sintáctica

### 3. **AsyncStorage → Supabase: Datos existentes**
**Problema**: Conversaciones existentes en AsyncStorage no se migrarán automáticamente.

**Solución implementada**:
- Si Supabase no tiene conversaciones, `loadConversations()` crea una inicial
- Comportamiento backward-compatible
- Usuario puede crear script de migración si necesita preservar datos antiguos

## Decisiones Técnicas

### ¿Por qué recargar con `loadConversations()` en cada operación?

**Razón**: Garantizar sincronización perfecta entre estado local y Supabase

**Trade-offs**:
- ✅ **Pro**: Estado siempre consistente, preparado para real-time updates
- ✅ **Pro**: Más simple de mantener (una sola fuente de verdad)
- ⚠️ **Con**: Más llamadas a DB (optimizable con React Query cache)
- ⚠️ **Con**: Latencia perceptible en operaciones (mitigable con optimistic updates)

**Alternativa considerada**: Actualizar estado local + sync en background
- Rechazada porque puede causar inconsistencias si falla el sync

### ¿Por qué UUID en lugar de string numérico?

**Razón**: Supabase usa UUID por defecto, mejor para sistemas distribuidos

**Cambio**:
```diff
- id: Date.now().toString()  // '1729531234567'
+ id: gen_random_uuid()      // 'a0eebc99-9c0b-4ef8-bb6d-6bb9bd380a11'
```

**Beneficios**:
- No hay colisiones entre devices/usuarios
- Compatible con Supabase Realtime subscriptions
- Estándar en PostgreSQL

### ¿Por qué JSONB para `images`?

**Razón**: Flexibilidad y compatibilidad con estructura existente

**Estructura**:
```typescript
images?: string[]  // Array de URIs o base64
// Almacenado como: '["uri1", "uri2"]'
```

**Alternativa considerada**: Tabla separada `message_images`
- Rechazada por simplicidad actual
- Puede refactorizarse después si se necesita metadata por imagen

## TODOs / Trabajo Pendiente

- [ ] **Ejecutar migración en Supabase** (requiere usuario con credenciales)
  ```bash
  supabase db push
  ```

- [ ] **Probar app completa** con `bun run start-web`
  - Verificar creación de conversaciones
  - Verificar envío de mensajes
  - Verificar pin/rename/delete
  - Verificar persistencia entre reloads

- [ ] **Optimizaciones futuras** (no urgente):
  - [ ] Implementar React Query para cache de conversaciones
  - [ ] Agregar optimistic updates en addMessage
  - [ ] Implementar paginación de mensajes para conversaciones largas
  - [ ] Agregar Supabase Realtime subscriptions para multi-device sync

- [ ] **Preparar para multi-usuario** (futuro):
  - [ ] Agregar columna `user_id` a tabla `conversations`
  - [ ] Actualizar RLS policies para filtrar por usuario
  - [ ] Implementar autenticación con Supabase Auth

- [ ] **Migración de datos** (si es necesario):
  - [ ] Crear script para migrar conversaciones de AsyncStorage a Supabase
  - [ ] Ejecutar migración para usuarios existentes

## Notas para Futuras Sesiones

### Patrón de Sincronización

Este patrón se puede aplicar a otros contextos:
```typescript
// 1. Operación en Supabase
const { data, error } = await supabase.from('table').operation();

// 2. Verificar error
if (error) throw error;

// 3. Recargar estado local
await loadData();
```

### Testing Recomendado

Cuando se pruebe la app:
1. Abrir DevTools → Network para ver llamadas a Supabase
2. Verificar logs en consola (`console.log` agregados)
3. Revisar tabla en Supabase Dashboard después de operaciones
4. Probar reload de página (debe persistir conversaciones)

### Gotchas a Tener en Cuenta

1. **IDs cambiaron de string numérico a UUID**: Si hay código que hace `parseInt(id)`, fallará
2. **Funciones ahora async**: Cualquier código que llame a estas funciones DEBE usar `await`
3. **Timestamp es ISO string en DB**: Debe convertirse a Date object al leer
4. **Images puede ser null en DB**: Debe convertirse a undefined en TypeScript

### Archivo de Referencia

Este mismo patrón fue implementado en `ClientContext.tsx` (ver commit previo).
Comparar ambos archivos para mantener consistencia de patrón.

## Archivos Afectados

```
supabase/migrations/20251021180000_create_conversations_tables.sql  [NUEVO]
contexts/ChatContext.tsx                                             [MODIFICADO]
app/(tabs)/chat.tsx                                                  [MODIFICADO]
```

## Estado Final

✅ **Código refactorizado completamente**
✅ **Migración SQL creada**
⏳ **Pendiente**: Ejecutar migración en Supabase
⏳ **Pendiente**: Probar app end-to-end

---

**Siguiente paso recomendado**: Ejecutar `supabase db push` y probar la app con `bun run start-web`
