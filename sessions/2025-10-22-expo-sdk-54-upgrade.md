# Upgrade de Expo SDK 53 a SDK 54

**Última actualización**: 2025-10-22 06:35

## Contexto

El proyecto estaba usando Expo SDK 53, pero la versión de Expo Go instalada en el dispositivo era para SDK 54, causando incompatibilidad. El error mostrado era:

```
ERROR  Project is incompatible with this version of Expo Go
• The installed version of Expo Go is for SDK 54.0.0.
• The project you opened uses SDK 53.
```

## Cambios Realizados

### Archivos Modificados

- `package.json` - Actualizado todas las dependencias de Expo de SDK 53 a SDK 54

### Versiones Actualizadas

**Core Dependencies:**
- `expo`: `^53.0.4` → `~54.0.0`
- `react`: `19.0.0` → `19.1.0`
- `react-dom`: `19.0.0` → `19.1.0`
- `react-native`: `0.79.1` → `0.81.5`
- `react-native-web`: `^0.20.0` → `~0.21.0`

**Expo Packages:**
- `@expo/vector-icons`: `^14.1.0` → `^15.0.2`
- `@react-native-async-storage/async-storage`: `2.1.2` → `2.2.0`
- `expo-blur`: `~14.1.4` → `~15.0.7`
- `expo-camera`: `~16.1.11` → `~17.0.8`
- `expo-constants`: `~17.1.4` → `~18.0.10`
- `expo-font`: `~13.3.0` → `~14.0.9`
- `expo-haptics`: `~14.1.4` → `~15.0.7`
- `expo-image`: `~2.1.6` → `~3.0.10`
- `expo-image-picker`: `~16.1.4` → `~17.0.8`
- `expo-linear-gradient`: `~14.1.4` → `~15.0.7`
- `expo-linking`: `~7.1.4` → `~8.0.8`
- `expo-location`: `~18.1.4` → `~19.0.7`
- `expo-router`: `~5.0.3` → `~6.0.13` ⚠️ **Major version change**
- `expo-splash-screen`: `~0.30.7` → `~31.0.10`
- `expo-status-bar`: `~2.2.3` → `~3.0.8`
- `expo-symbols`: `~0.4.4` → `~1.0.7`
- `expo-system-ui`: `~5.0.6` → `~6.0.8`
- `expo-web-browser`: `^15.0.8` → `~15.0.8`

**React Native Packages:**
- `react-native-gesture-handler`: `~2.24.0` → `~2.28.0`
- `react-native-safe-area-context`: `5.3.0` → `~5.6.0` ⚠️ **Necesario para expo-router 6.x**
- `react-native-svg`: `15.11.2` → `15.12.1`

**Dev Dependencies:**
- `eslint-config-expo`: `9.2.0` → `~10.0.0`

## Problemas Encontrados y Soluciones

### 1. Error de instalación con npm

**Error**:
```
npm error Cannot read properties of null (reading 'matches')
```

**Causa raíz**: Conflictos en node_modules existentes y cache corrupto de npm.

**Solución**: Limpiar completamente e instalar con `--legacy-peer-deps`:
```bash
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

### 2. Conflicto de react-native-safe-area-context

**Error**:
```
peer react-native-safe-area-context@">= 5.4.0" from expo-router@6.0.13
Found: react-native-safe-area-context@5.3.0
```

**Causa raíz**: expo-router 6.x requiere react-native-safe-area-context >= 5.4.0

**Solución**: Actualizar a la versión recomendada por SDK 54:
```json
"react-native-safe-area-context": "~5.6.0"
```

**Archivo afectado**: `package.json:45`

### 3. Comando bun no disponible en PATH

**Problema**: El proyecto está configurado para usar `bun` pero no estaba disponible en la sesión actual.

**Solución temporal**: Usar `npm` con `--legacy-peer-deps` para resolver dependencias.

**Nota para futuro**: Asegurar que `bun` esté en PATH o actualizar scripts en package.json para usar npm/npx como fallback.

## Decisiones Técnicas

### ¿Por qué usar --legacy-peer-deps?

**Razón**: SDK 54 tiene cambios significativos en versiones de paquetes (especialmente expo-router 5→6 y React Native 0.79→0.81) que pueden causar conflictos de peer dependencies con paquetes de terceros que aún no han actualizado sus rangos de versión.

**Trade-offs**:
- ✅ Permite instalar sin bloqueos
- ✅ Las dependencias son correctas según SDK 54
- ⚠️ Ignora advertencias de peer deps (pero en este caso son falsas alarmas)

**Referencia**: [Expo SDK 54 Release Notes](https://expo.dev/changelog/sdk-54)

### ¿Por qué no usar npx expo install?

**Intentado inicialmente** pero falló porque:
1. Ya había una instancia de Expo CLI corriendo (dev server)
2. Comando esperaba `bun` pero no estaba disponible

**Alternativa elegida**: Editar manualmente package.json con versiones exactas del repositorio oficial de Expo (branch sdk-54) y luego instalar con npm.

## Nuevas Features de SDK 54 (Relevantes para el Proyecto)

### Expo Router v6

⚠️ **IMPORTANTE**: Expo Router se actualizó de v5 a v6, que incluye cambios breaking.

**Cambios clave**:
- Nuevas APIs de tabs nativas para iOS/Android
- Mejor rendimiento de navegación
- Cambios en tipos de TypeScript

**Acción requerida**: Revisar la documentación de migración:
https://docs.expo.dev/versions/v54.0.0/sdk/router/

### React Native 0.81

- Precompiled XCFrameworks para iOS → builds limpios más rápidos
- Android 16 target con edge-to-edge habilitado por defecto
- Mejoras de rendimiento generales

### Otros cambios notables

- iOS 26 Liquid Glass icons support
- Mejoras en expo-updates con progress y reload screens
- Mejor soporte para TypeScript

## Update: 2025-10-22 06:36

### Problema: Paquete Faltante

**Error en build**:
```
Unable to resolve "expo-image-manipulator" from "lib/imageProcessor.ts"
```

**Causa**: El paquete `expo-image-manipulator` se usa en el código (`lib/imageProcessor.ts:1`) pero no estaba listado en dependencies.

**Solución**: Agregado a package.json con versión SDK 54:
```json
"expo-image-manipulator": "~14.0.7"
```

**Archivo afectado**: `package.json:29`

### Problema: Supabase API Key Inválida

**Error en runtime**:
```
Invalid API key
Hint: Double check your Supabase `anon` or `service_role` API key.
```

**Causa**: La `EXPO_PUBLIC_SUPABASE_ANON_KEY` en `.env.local` tenía un valor corrupto (terminaba en `.1234567890`).

**Solución**: Actualizado con la clave correcta obtenida desde el proyecto de Supabase:
```
eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd1eXhjemF2aHRlbXdscmtucXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjEwNDg3ODYsImV4cCI6MjA3NjYyNDc4Nn0.fs6bWg2F-o54KuNUEzsCHMkAuE-as_FILYTIqqgrAng
```

**Archivo afectado**: `.env.local:8`

**Acción requerida**: Reiniciar el servidor de desarrollo para que cargue las nuevas variables de entorno.

### Problema: Edge Function Requiere Autenticación

**Error en runtime**:
```
[AIServiceError: Session expired]
```

**Causa**: La Edge Function `ai-proxy` requiere un token de autenticación válido para funcionar (verifica `Authorization` header y valida el usuario). La app no tenía implementado ningún sistema de autenticación.

**Solución**: Implementado autenticación anónima automática:

**Archivos creados:**
- `contexts/AuthContext.tsx` - Contexto que maneja sesión anónima automática

**Archivos modificados:**
- `app/_layout.tsx:7,38` - Agregado `AuthProvider` al árbol de contextos
- `lib/ai-client.ts:80-88,140-141` - Eliminada verificación de sesión para casos sin imágenes

**Cómo funciona:**

1. Al iniciar la app, `AuthContext` intenta obtener sesión existente
2. Si no hay sesión, crea un usuario anónimo con `signInAnonymously()`
3. Si auth anónima falla, crea usuario temporal con email único
4. La sesión persiste en AsyncStorage automáticamente (Supabase)
5. Todos los llamados a Edge Functions ahora incluyen el token de auth

**Beneficios:**
- ✅ No requiere login explícito del usuario
- ✅ Permite usar Edge Functions protegidas
- ✅ Rate limiting por usuario
- ✅ Base para features premium futuras
- ✅ Más seguro que deshabilitar auth en Edge Function

### Problema: Chat no Procesa Imágenes

**Error**: Al enviar una imagen en el chat, la IA responde pidiendo que envíes una imagen, ignorando la que acabas de subir.

**Causa**: El código del chat usaba `useCase: 'chat'` incluso cuando había imágenes adjuntas. El `ai-client.ts` solo procesa imágenes cuando `useCase === 'vision_analysis'`.

**Solución**: Cambiar `useCase` a `'vision_analysis'` cuando hay imágenes en el chat.

**Archivo afectado**: `app/(tabs)/chat.tsx:123`

**Cambio**:
```typescript
// Antes
useCase: 'chat',

// Después
useCase: 'vision_analysis', // Usar vision_analysis cuando hay imágenes
```

---

## Update: 2025-10-22 07:15

### Problema: Edge Function 500 Error con Imágenes

**Error en runtime**:
```
[FunctionsHttpError: Edge Function returned a non-2xx status code] (500)
```

**Causa**: El parámetro `response_format: { type: 'json_object' }` en la llamada a OpenAI estaba causando que la API rechazara requests de vision_analysis. OpenAI requiere instrucciones explícitas en el prompt para devolver JSON cuando se usa `json_object` mode, de lo contrario devuelve error.

**Solución**: Eliminado `response_format` del caso `vision_analysis` en Edge Function:

**Archivo afectado**: `supabase/functions/ai-proxy/index.ts:131-138`

**Cambio**:
```typescript
// ANTES (causaba error 500):
const completion = await openai.chat.completions.create({
  model: 'gpt-4o',
  messages,
  max_tokens: 2048,
  temperature: temperature ?? 0.3,
  response_format: { type: 'json_object' },  // ❌ PROBLEMA
});

// DESPUÉS (funciona correctamente):
const completion = await openai.chat.completions.create({
  model: 'gpt-4o',
  messages,
  max_tokens: 2048,
  temperature: temperature ?? 0.3,
  // Eliminar response_format JSON para vision - causa problemas
  // response_format: { type: 'json_object' },
});
```

**Deployment**: Edge Function desplegada como versión 13 con Supabase CLI.

**Resultado**: ✅ **Análisis de imágenes funcionando correctamente** - Confirmado por el usuario.

### Otros Cambios en Esta Sesión

**Archivo afectado**: `lib/ai-client.ts`

1. **Import de FileSystem** (línea 8):
   - Cambiado de `expo-file-system` a `expo-file-system/legacy` (requerido por SDK 54)

2. **Upload de imágenes** (líneas 100-137):
   - Cambiado de fetch/blob (no disponible en React Native) a FileSystem.readAsStringAsync con Base64
   - Convertir Base64 a ArrayBuffer manualmente para Supabase Storage upload
   - Cambiar de signed URLs a public URLs para acceso desde OpenAI

3. **Headers de Auth** (líneas 145-155):
   - Eliminado header manual de Authorization (Supabase client lo agrega automáticamente)

4. **Error logging mejorado** (líneas 157-180):
   - Agregado logs detallados para debugging de Edge Function errors

**Archivo afectado**: `app/(tabs)/chat.tsx:123`
- Cambiado `useCase: 'chat'` a `useCase: 'vision_analysis'` cuando hay imágenes

**Archivo creado**: `test-vision-analysis.js`
- Script de testing automatizado para diagnosticar problemas con vision_analysis
- Llama directamente a Edge Function con imagen de prueba pública

## TODOs / Trabajo Pendiente

- [x] Actualizar package.json con versiones SDK 54
- [x] Instalar dependencias
- [x] Agregar expo-image-manipulator faltante
- [x] Ejecutar lint
- [x] Implementar autenticación temporal
- [x] Arreglar procesamiento de imágenes en chat
- [x] Deshabilitar confirmación de email en Supabase (manual - usuario lo hizo)
- [x] **Fix Edge Function 500 error con imágenes**
- [x] **Probar análisis de imágenes en chat - ✅ FUNCIONANDO**
- [ ] Revisar migración guide de Expo Router v6 (breaking changes)
- [ ] Probar todas las pantallas críticas:
  - [ ] Formula workflow (steps 0-5)
  - [x] Chat con AI (text + images) - ✅ FUNCIONANDO
  - [ ] Client management
  - [ ] Camera/image picker
- [ ] Probar en iOS Simulator
- [ ] Probar en dispositivo físico con Expo Go 54
- [ ] Verificar que no haya deprecation warnings en consola
- [ ] Actualizar .gitignore si es necesario (package-lock.json se creó con npm)
- [ ] Limpiar archivos de testing temporal (test-vision-analysis.js, test-ai-proxy-complete.js, test-edge-function.ts)

## Notas para Futuras Sesiones

### Compatibilidad con Rork

Este proyecto está integrado con Rork (rork.com) que hace commits automáticos. Los comandos de inicio usan `bunx rork start`, así que:

1. **Si bunx/bun no está en PATH**: Usar npm run start alternativamente
2. **Verificar que Rork sigue siendo compatible con SDK 54**
3. Los scripts de package.json pueden necesitar actualización si hay problemas con Rork CLI

### Monitoreo Post-Upgrade

Observar por:
- Warnings de deprecación en consola
- Cambios en comportamiento de navegación (Expo Router v6)
- Performance issues (aunque debería mejorar con RN 0.81)
- Problemas con expo-camera o expo-image-picker (versiones mayores)

### Testing Recomendado

Priorizar testing de:
1. **Formula creation workflow** - usa image picker y camera
2. **Chat AI** - usa AsyncStorage y context
3. **Client CRUD** - usa Supabase y context
4. **Navigation** - asegurar que tabs/stack funcionan con Router v6

## Comandos Útiles Post-Upgrade

```bash
# Iniciar dev server
npm run start-web

# Verificar versiones instaladas
npm list expo expo-router react-native

# Ver changelog de Expo Router v6
npx expo-doctor

# Si hay problemas, reinstalar limpio
rm -rf node_modules package-lock.json
npm install --legacy-peer-deps
```

## Referencias

- [Expo SDK 54 Release](https://expo.dev/changelog/sdk-54)
- [Expo Router v6 Docs](https://docs.expo.dev/versions/v54.0.0/sdk/router/)
- [React Native 0.81 Release](https://reactnative.dev/blog)
- [SDK 54 bundledNativeModules.json](https://github.com/expo/expo/blob/sdk-54/packages/expo/bundledNativeModules.json)
