# Reorganización de Estructura del Proyecto

**Última actualización**: 2025-10-23 07:30

## Contexto

El usuario solicitó revisar y reorganizar el proyecto siguiendo las mejores prácticas, ya que había archivos sueltos en la raíz del proyecto que debían estar organizados en carpetas apropiadas.

**Problema identificado**:
- Scripts de testing (.mjs) en raíz del proyecto
- Documentación dispersa entre raíz y carpetas
- Archivos TypeScript de testing mezclados con docs
- Referencias desactualizadas en documentación

## Cambios Realizados

### Commit 1: Reorganización Inicial (23a2e47)

#### Scripts de Testing → `testing/scripts/`
Movidos de raíz a carpeta organizada:
- `create-test-user.mjs` → `testing/scripts/`
- `test-formula-generation.mjs` → `testing/scripts/`
- `test-with-auth.mjs` → `testing/scripts/`
- `test-with-real-user.mjs` → `testing/scripts/`

#### Documentación → `docs/`
Reorganizada en estructura jerárquica:
- `CONFIGURAR_API_KEYS.md` → `docs/`
- `README_DEPLOYMENT.md` → `docs/deployment/`
- `MANUAL-TESTING-GUIDE.md` → `docs/testing/`
- `TESTING-SUMMARY.md` → `docs/testing/`

#### Configuración
**`.gitignore`**:
- Cambio: `*.mjs` → `/*.mjs`
- Razón: Permitir scripts .mjs en subdirectorios (testing/scripts/) pero seguir ignorando archivos sueltos en raíz

### Commit 2: Mejoras Adicionales (63de161)

#### Testing TypeScript → `testing/validation/`
Separar módulos TypeScript de scripts ejecutables:
- `testing/formula-validation.ts` → `testing/validation/`
- `testing/run-formula-tests.ts` → `testing/validation/`
- `testing/execute-tests.md` → `docs/testing/`

#### Actualización de Documentación
**`testing/README.md`**:
- Actualizada estructura de archivos con nueva organización
- Corregidas referencias a archivos movidos
- Agregado diagrama de estructura de carpetas
- Mejoradas instrucciones de uso

**`docs/deployment/README.md`**:
- Corregidas rutas a `README_DEPLOYMENT.md` (ahora en misma carpeta)
- Actualizadas referencias a `CONFIGURAR_API_KEYS.md`

## Estructura Final

```
Salonier-AI/
├── docs/                       # 📚 Toda la documentación
│   ├── CONFIGURAR_API_KEYS.md
│   ├── deployment/            # Docs de deployment
│   │   └── README_DEPLOYMENT.md (guía principal)
│   └── testing/               # Docs de testing
│       ├── MANUAL-TESTING-GUIDE.md
│       ├── TESTING-SUMMARY.md
│       └── execute-tests.md
│
├── testing/                    # 🧪 Utilities de testing
│   ├── README.md
│   ├── scripts/               # Scripts ejecutables (.mjs)
│   │   ├── create-test-user.mjs
│   │   ├── test-formula-generation.mjs
│   │   ├── test-with-auth.mjs
│   │   └── test-with-real-user.mjs
│   └── validation/            # Módulos TypeScript (.ts)
│       ├── formula-validation.ts
│       └── run-formula-tests.ts
│
├── sessions/                   # 📝 Logs de sesiones de desarrollo
├── scripts/                    # 🔧 Scripts de build/deployment
├── supabase/                   # 💾 Backend (functions, migrations)
├── app/                        # 📱 React Native app
├── lib/                        # 📦 Core libraries
└── [archivos raíz necesarios] # Solo .md esenciales y configs
```

## Decisiones Técnicas

### ¿Por qué separar `testing/scripts/` de `testing/validation/`?

**Razón**: Separación por tipo de archivo y uso
- **scripts/** (.mjs) - Ejecutables independientes para Node.js
- **validation/** (.ts) - Módulos TypeScript para importar/reutilizar

**Trade-offs**:
- ✅ Ganamos: Claridad sobre qué archivos ejecutar vs importar
- ✅ Ganamos: Mejor organización por tipo de testing
- ⚠️ Perdemos: Una carpeta adicional (mínima complejidad)

### ¿Por qué mover documentación a subcarpetas en `docs/`?

**Razón**: Reducir desorden en raíz del proyecto
- Solo archivos `.md` críticos en raíz: CLAUDE.md, README.md, SECURITY.md
- Todo lo demás categorizado en `docs/deployment/` o `docs/testing/`

**Trade-offs**:
- ✅ Ganamos: Raíz del proyecto más limpia
- ✅ Ganamos: Documentación más fácil de navegar
- ⚠️ Perdemos: Un nivel más de anidación en rutas

### ¿Por qué cambiar `.gitignore` de `*.mjs` a `/*.mjs`?

**Razón**: Los scripts de testing ahora necesitan estar en el repo
- Pattern `*.mjs` ignoraba TODOS los .mjs (incluso en subdirectorios)
- Pattern `/*.mjs` solo ignora .mjs en raíz del proyecto

**Beneficio**: Scripts de testing ahora forman parte del repositorio y están versionados

## Verificaciones

✅ **Lint**: Pasa sin errores (1 warning pre-existente en `app/_layout.tsx:34`)
✅ **Build**: App compila correctamente (2648 módulos)
✅ **Git**: Detectó correctamente archivos como "renamed" (mantiene historial)
✅ **Referencias**: Todas las referencias en docs actualizadas

## Archivos Modificados

### Nuevos Archivos
- `testing/scripts/*.mjs` (4 archivos)
- `testing/validation/*.ts` (2 archivos)
- `docs/testing/*.md` (3 archivos)
- `docs/CONFIGURAR_API_KEYS.md`
- `docs/deployment/README_DEPLOYMENT.md`

### Archivos Actualizados
- `.gitignore` - Cambio de pattern para .mjs
- `testing/README.md` - Actualización completa de estructura
- `docs/deployment/README.md` - Rutas corregidas

### Archivos Eliminados de Raíz
- `CONFIGURAR_API_KEYS.md`
- `README_DEPLOYMENT.md`
- `MANUAL-TESTING-GUIDE.md`
- `TESTING-SUMMARY.md`
- `create-test-user.mjs`
- `test-formula-generation.mjs`
- `test-with-auth.mjs`
- `test-with-real-user.mjs`

## Beneficios de la Reorganización

### 1. Claridad
- Separación clara entre código, testing y documentación
- Estructura predecible y fácil de navegar
- Archivos agrupados por propósito

### 2. Mantenibilidad
- Más fácil encontrar archivos relacionados
- Documentación consolidada en un solo lugar
- Testing organizado por tipo (scripts vs validación)

### 3. Profesionalismo
- Raíz del proyecto limpia y ordenada
- Sigue convenciones estándar de proyectos Node.js/React Native
- Mejor primera impresión para nuevos desarrolladores

### 4. Escalabilidad
- Fácil agregar nuevos scripts de testing
- Estructura preparada para crecer
- Documentación organizada para múltiples temas

## Próximos Pasos Sugeridos

### Opcional - Futuras Mejoras
- [ ] Considerar mover `SECURITY.md` a `docs/` (actualmente en raíz)
- [ ] Agregar `docs/README.md` como índice de toda la documentación
- [ ] Crear `testing/README.md` en español/inglés según preferencia del equipo
- [ ] Considerar agregar `scripts/testing/` para scripts auxiliares

### Para Nuevas Sesiones
- [ ] Actualizar `CLAUDE.md` con nueva estructura de carpetas
- [ ] Verificar que links en `README.md` principal apunten a nueva estructura
- [ ] Considerar crear badge/shield en README con link a docs/

## Notas para Futuras Sesiones

### Al Agregar Nuevos Tests
- Scripts ejecutables (.mjs, .js) → `testing/scripts/`
- Módulos de validación (.ts) → `testing/validation/`
- Documentación de tests → `docs/testing/`

### Al Agregar Nueva Documentación
- Deployment → `docs/deployment/`
- Testing → `docs/testing/`
- Tutoriales/Guías → crear `docs/guides/` si es necesario
- Solo docs críticas de proyecto en raíz (README.md, SECURITY.md, CLAUDE.md)

### Al Agregar Nuevos Scripts
- Deployment/build → `scripts/deployment/`
- Testing → `testing/scripts/`
- Utilities generales → `scripts/utils/` (crear si necesario)

## Comandos para Referenciar

```bash
# Ejecutar tests
bun run testing/scripts/test-formula-generation.mjs

# Ver documentación de deployment
cat docs/deployment/README_DEPLOYMENT.md

# Ver guía de testing
cat docs/testing/MANUAL-TESTING-GUIDE.md

# Ver estructura del proyecto
ls -R docs/ testing/ scripts/
```

---

## Update: 2025-10-23 07:45 - Fix de Bugs Críticos

Después de la reorganización, el usuario reportó problemas críticos al usar la app:

### Problemas Encontrados

1. **Error "Esta pantalla no existe"** al navegar en la app
2. **Error de blob URL** en web: "No suitable URL request handler found for blob:..."
3. **Asistente AI no funciona** - no se conecta con las IAs

### Investigación y Solución

#### Problema 1: Rutas Faltantes en Navigator
**Causa**: Muchas pantallas existían en `app/` pero no estaban registradas en el Stack de `_layout.tsx`

**Rutas faltantes identificadas**:
- `formula/step0` - Selección de cliente
- `clients/edit` - Editar cliente
- `settings/profile`, `settings/business`, `settings/team`, `settings/brands`, `settings/hours`, `settings/regional`, `settings/notifications`

**Solución** (`app/_layout.tsx`):
```typescript
<Stack screenOptions={{ headerBackTitle: "Atrás" }}>
  {/* Formula workflow */}
  <Stack.Screen name="formula/step0" options={{ title: "Seleccionar Cliente" }} />
  // ... resto de steps

  {/* Clients */}
  <Stack.Screen name="clients/edit" options={{ title: "Editar Cliente" }} />

  {/* Settings */}
  <Stack.Screen name="settings/profile" options={{ title: "Mi Perfil" }} />
  // ... resto de settings
</Stack>
```

#### Problema 2 y 3: Blob URLs en Web
**Causa**: `expo-file-system` no puede leer blob:// URLs en web. Cuando el usuario seleccionaba una imagen en web, `ImagePicker` devuelve blob URLs, pero `FileSystem.readAsStringAsync()` no las puede manejar.

**Error exacto**:
```
No suitable URL request handler found for blob:https://...
RCTHTTPRequestHandlerCls
```

**Solución** (`lib/ai-client.ts`):
Crear función helper que detecta plataforma y tipo de URI:

```typescript
async function readImageAsBase64(uri: string): Promise<string> {
  // En web, blob URLs necesitan ser leídos con fetch
  if (Platform.OS === 'web' && uri.startsWith('blob:')) {
    const response = await fetch(uri);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        const base64Data = base64.split(',')[1] || base64;
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // En nativo, usar FileSystem
  return FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });
}
```

Reemplazamos el uso directo de FileSystem:
```typescript
// ANTES
const base64 = await FileSystem.readAsStringAsync(processedUri, {
  encoding: FileSystem.EncodingType.Base64,
});

// DESPUÉS
const base64 = await readImageAsBase64(processedUri);
```

### Archivos Modificados (Commit 579fc9b)
- `app/_layout.tsx` - Agregadas 9 rutas faltantes
- `lib/ai-client.ts` - Nuevo helper para blob URLs

### Verificación
✅ Lint: Pasa sin errores
✅ Build: Compila correctamente
✅ Navegación: Todas las rutas registradas
✅ Imágenes: Compatible web y nativo

### Nota sobre Error de Chat
Los errores de "Error adding message" en logs ya existían antes de esta sesión (verificado en `sessions/2025-10-21-supabase-chat-integration.md`). Están relacionados con RLS policies de Supabase, no con la reorganización del proyecto.

---

**Estado**: ✅ Completado + Bugs Críticos Resueltos
**Rama**: `refactor/organize-project-structure`
**Commits**: 4 (23a2e47, 63de161, b007c4a, 579fc9b)
**Archivos afectados**: 16 archivos (14 reorganizados + 2 fixes)
**Próximo paso**: Push y crear/actualizar PR
