# Sistema de Autenticación Multi-Tenant

**Última actualización**: 2025-10-22 17:15

## Contexto

Implementación completa de sistema de autenticación con arquitectura multi-tenant para Salonier AI. El objetivo era crear una base sólida que soporte tanto estilistas independientes (freelancers) como salones con equipos de trabajo, con un path de monetización claro (Free → Premium).

## Decisiones Arquitectónicas Tomadas

### 1. Modelo Multi-Tenant Híbrido

Después de research extenso sobre SaaS multi-tenant best practices, decidimos implementar un modelo híbrido que soporta:

**Freelancers (Tier Gratuito)**:
- `organization_id = null` en clientes y recursos
- Trabajan de forma independiente
- Sin límites de clientes

**Salones (Tier Premium)**:
- `organization_id` presente en recursos
- Pool de clientes compartido entre equipo
- Roles: owner, admin, stylist, receptionist
- Data ownership: clientes pertenecen al salón

**Justificación**: Apps exitosas del sector (Fresha $219M, GlossGenius $70M) usan este modelo. Permite crecimiento orgánico y path de monetización claro.

### 2. Data Ownership

**Decisión**: Los clientes pertenecen a la organización, no al estilista individual.

**Consecuencias**:
- ✅ Cuando un estilista deja el salón, pierde acceso inmediato
- ✅ Los datos se quedan en el salón
- ✅ Simplifica modelo de negocio para salones

**Alternativas consideradas**: Ownership por estilista (rechazada por ser compleja de manejar)

### 3. RLS (Row Level Security) para Multi-Tenant

Implementamos RLS en PostgreSQL para aislar datos a nivel de base de datos:

```sql
-- Ejemplo: Clientes
CREATE POLICY "Users can read own organization or freelance clients"
  ON public.clients
  FOR SELECT
  USING (
    (created_by = auth.uid() AND organization_id IS NULL) -- Freelance
    OR
    (organization_id IS NOT NULL AND EXISTS ( -- Organization member
      SELECT 1 FROM public.organization_members
      WHERE organization_members.organization_id = clients.organization_id
      AND organization_members.user_id = auth.uid()
    ))
  );
```

**Ventajas**:
- Seguridad a nivel de DB (no depende de lógica de app)
- Queries automáticamente filtrados
- Previene data leaks

## Cambios Realizados

### Base de Datos (6 migraciones)

1. **`profiles`** - Perfil de usuario
   - 1:1 con `auth.users`
   - `is_freelance` boolean para determinar tipo
   - Auto-creado via trigger on signup

2. **`organizations`** - Salones/negocios
   - Migra `BusinessProfile` de settings a DB
   - `plan` enum: free/premium/enterprise
   - Business hours en JSONB

3. **`organization_members`** - Relación user ↔ org
   - `role`: owner/admin/stylist/receptionist
   - `permissions` JSONB con flags granulares
   - Trigger auto-set permissions por role

4. **`clients`** - Actualizada para multi-tenant
   - `organization_id` nullable (null = freelance)
   - `created_by` para tracking
   - RLS policies para aislar data

5. **`conversations`** - Actualizada para multi-tenant
   - `user_id` (dueño de la conversación)
   - `organization_id` opcional (contexto)
   - RLS policies por usuario

6. **Migración consolidada**
   - Todas las tablas + policies en un solo archivo
   - Evita dependencias circulares
   - Aplicada exitosamente vía MCP de Supabase

### Frontend

**AuthContext** (`contexts/AuthContext.tsx`):
- Login/signup con email + password
- Logout
- Password reset (email link)
- Profile management
- Session persistence
- Auth state listener

**Pantallas de Auth** (`app/(auth)/`):
- `login.tsx` - Email + password con validación
- `signup.tsx` - Registro con nombre, email, password (x2)
- `forgot-password.tsx` - Reset con confirmación visual
- `_layout.tsx` - Stack sin header

**Protección de Rutas** (`app/_layout.tsx`):
- Hook `useProtectedRoute()`
- Redirige a `/login` si no autenticado
- Redirige a `/(tabs)` si autenticado y en auth routes
- Loading state mientras verifica sesión

**Contexts Actualizados**:
- `ClientContext`: Filtrado automático por user/org via RLS
- `ChatContext`: Conversaciones vinculadas a `user_id`

### Archivos Creados

```
contexts/AuthContext.tsx
app/(auth)/_layout.tsx
app/(auth)/login.tsx
app/(auth)/signup.tsx
app/(auth)/forgot-password.tsx
supabase/migrations/20251022170227_create_profiles_table.sql
supabase/migrations/20251022170228_create_organizations_table.sql
supabase/migrations/20251022170229_create_organization_members_table.sql
supabase/migrations/20251022170230_update_clients_table_for_multitenant.sql
supabase/migrations/20251022170231_update_conversations_table_for_multitenant.sql
supabase/migrations/20251022170300_auth_system_consolidated.sql
```

### Archivos Modificados

```
app/_layout.tsx (protección de rutas)
contexts/ClientContext.tsx (multi-tenant)
contexts/ChatContext.tsx (multi-tenant)
```

## Problemas Encontrados y Soluciones

### 1. Dependencias Circulares en Migraciones

**Error**:
```
ERROR: 42P01: relation "public.organization_members" does not exist
```

**Causa**: Las policies de `profiles` y `organizations` referenciaban `organization_members` antes de que existiera.

**Solución**: Crear migración consolidada que:
1. Crea todas las tablas SIN policies
2. Habilita RLS
3. Crea policies (ya con todas las tablas existentes)

**Archivo**: `20251022170300_auth_system_consolidated.sql`

### 2. Context Hooks Necesitan Usuario

**Problema**: `ClientContext` y `ChatContext` intentaban cargar datos sin verificar si hay usuario logueado.

**Solución**:
```typescript
const { user } = useAuth();

const loadClients = useCallback(async () => {
  if (!user) {
    setClients([]);
    setIsLoading(false);
    return;
  }
  // ... rest of logic
}, [user]);
```

### 3. RLS Policy Demasiado Restrictiva en Clients (Inicial)

**Policy original**:
```sql
USING (auth.role() = 'authenticated' or auth.role() = 'anon')
```

**Problema**: No filtraba por organización, todos los usuarios veían todos los clientes.

**Solución**: Policy basada en `organization_id` y `created_by`:
```sql
USING (
  (created_by = auth.uid() AND organization_id IS NULL)
  OR
  (organization_id IS NOT NULL AND EXISTS (...))
)
```

## TODOs / Trabajo Pendiente

- [ ] **Habilitar Email/Password en Supabase Dashboard**
  - Auth → Providers → Email → Enable
  - Configurar email templates (opcional)

- [ ] **Configurar OAuth (opcional)**
  - Google: Client ID + Secret
  - Apple: Bundle ID + Certificates
  - Docs: https://supabase.com/docs/guides/auth/social-login

- [ ] **Pantalla de Onboarding**
  - Crear organización (opcional)
  - Invitar primer miembro
  - Skip para freelancers

- [ ] **Sistema de Invitaciones**
  - Flujo completo de invitar → aceptar → unirse
  - Notificaciones por email
  - Deep links para aceptar invitación

- [ ] **Conectar Settings con DB**
  - `settings/profile.tsx` → leer/escribir `profiles`
  - `settings/team.tsx` → leer/escribir `organization_members`
  - `settings/business.tsx` → leer/escribir `organizations`

- [ ] **Ejecutar Lint y Fix**
  - `npx expo lint` (se quedó procesando)
  - Revisar errores TypeScript
  - Fix imports no usados

- [ ] **Testing de Flujos Completos**
  - Signup → Login → Ver clientes (vacío)
  - Crear cliente → Verificar que aparezca
  - Logout → Verificar redirect a login
  - Login → Verificar que vuelva a app

- [ ] **Edge Cases**
  - ¿Qué pasa si usuario pertenece a múltiples orgs? (no soportado ahora)
  - ¿Cómo un freelance crea su primera org? (onboarding)
  - ¿Cómo cambiar entre orgs? (selector en UI)

## Notas para Futuras Sesiones

### Arquitectura Sólida

La arquitectura está lista para escalar:
- ✅ Multi-tenant con aislamiento de datos
- ✅ RLS a nivel de DB
- ✅ Path de monetización claro
- ✅ Roles y permisos granulares

### Siguientes Pasos Recomendados

1. **Testing manual** - Probar signup/login/logout
2. **Habilitar email provider en Supabase**
3. **Implementar onboarding** - Crucial para UX
4. **Conectar settings** - Ya tienes UI, solo falta DB
5. **Sistema de invitaciones** - Feature clave para salones

### Gotchas

- **AsyncStorage**: No se usa actualmente (Supabase maneja sesión)
- **Deep linking**: Configurado para `salonier://` pero no testeado
- **OAuth**: Requiere config externa (Google Console, Apple Developer)
- **Email verification**: Deshabilitada en dev, habilitar en producción

### Performance

- RLS policies pueden ser lentas con muchos JOINs
- Considerar índices adicionales si hay problemas
- Monitorear queries en Supabase Dashboard

### Seguridad

- ✅ RLS habilitado en todas las tablas
- ✅ Policies testeadas lógicamente
- ⚠️ NO testeadas con carga real
- ⚠️ Revisar policies antes de producción

## Commit

```bash
git add -A
git commit -m "feat: Implement multi-tenant authentication system

..."
```

Branch: `feature/authentication-system`
Commit: `89c0b3e`

## Métricas

- **Tiempo**: ~2.5 horas
- **Archivos creados**: 11
- **Archivos modificados**: 3
- **Líneas añadidas**: ~1941
- **Migraciones SQL**: 6 (consolidadas en 1)
- **Tablas creadas**: 3
- **Tablas actualizadas**: 2
