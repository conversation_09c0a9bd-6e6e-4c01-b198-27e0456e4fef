# Guía de Implementación: Migración a OpenAI + Perplexity API

**Última actualización**: 2025-10-21
**Estado**: Plan aprobado - Listo para implementación
**Duración estimada**: 2-3 semanas

---

## 📋 TABLA DE CONTENIDOS

1. [Contexto y Decisiones](#contexto-y-decisiones)
2. [Arquitectura Final](#arquitectura-final)
3. [Fase 1: Infraestructura Supabase](#fase-1-infraestructura-supabase)
4. [Fase 2: Edge Functions](#fase-2-edge-functions)
5. [Fase 3: Integración Frontend](#fase-3-integración-frontend)
6. [Testing y Validación](#testing-y-validación)
7. [Deployment Checklist](#deployment-checklist)
8. [Troubleshooting](#troubleshooting)

---

## CONTEXTO Y DECISIONES

### ¿Por qué esta migración?

**Situación actual:**
- SDK de Rork está experimentando errores (`Internal Server Error`)
- Error: `SyntaxError: Unexpected token 'I', "Internal S"... is not valid JSON`
- Necesidad de features específicas de GPT-4o Vision para análisis capilar
- Objetivo: app universal (Europa, Estados Unidos, Latinoamérica)

### Decisiones técnicas tomadas (post research exhaustivo):

#### ✅ OpenAI GPT-4o como motor principal
**Razones:**
1. **Compliance confirmado**: Microsoft Azure OpenAI Code of Conduct EXPLÍCITAMENTE permite análisis de "hair color" (no es atributo sensible según políticas)
2. **Popularidad**: Estándar de industria, más confiable y conocido
3. **Vision class**: Superior para análisis visual según benchmarks
4. **Ecosystem maduro**: Más documentación, ejemplos y comunidad
5. **Structured outputs**: JSON mode nativo perfecto para `HairAnalysis` type

#### ✅ Perplexity Sonar Pro como complemento
**Razones:**
1. **Real-time knowledge**: Información actualizada de productos de marcas (L'Oreal, Schwarzkopf, Wella, etc.)
2. **Más barato para búsquedas**: $5/1000 searches vs $25-50 equivalente en tokens GPT-4o
3. **Citations built-in**: Proporciona fuentes (crítico para recomendaciones de productos)
4. **Factuality líder**: SimpleQA score 0.858 (mejor que GPT-4 en verificación de hechos)
5. **Cache strategy**: Reduce costos repetitivos dramáticamente

### Compliance confirmado:

| Regulación | Status | Evidencia |
|------------|--------|-----------|
| OpenAI Usage Policy | ✅ COMPLIANT | "Hair color" explícitamente PERMITIDO en Microsoft Azure OpenAI Code of Conduct |
| GDPR (EU) | ✅ COMPLIANT | Face blur automático, consent explícito, 24h retention |
| CCPA/CPRA (CA) | ✅ COMPLIANT | Privacy policy, opt-out mechanism, sensitive data notice |
| EU AI Act | ✅ Minimal Risk | Clasificado como riesgo mínimo, compliance total by Aug 2026 |

---

## ARQUITECTURA FINAL

### Diagrama de flujo:

```
┌─────────────────────────────────────────────────────────────────┐
│                       REACT NATIVE APP                          │
│  ┌──────────────┐  ┌──────────────┐  ┌──────────────┐          │
│  │   Chat       │  │   Step 1-2   │  │   Step 5     │          │
│  │ (GPT-4o-mini)│  │ (GPT-4o vis) │  │  (Hybrid)    │          │
│  └──────┬───────┘  └──────┬───────┘  └──────┬───────┘          │
│         │                  │                  │                  │
│         └──────────────────┴──────────────────┘                  │
│                            │                                     │
│                   ┌────────▼────────┐                            │
│                   │  ai-client.ts   │                            │
│                   │  (abstraction)  │                            │
│                   └────────┬────────┘                            │
└────────────────────────────┼─────────────────────────────────────┘
                             │
                    ┌────────▼────────┐
                    │  imageProcessor │ (face blur client-side)
                    │   .ts (NEW)     │
                    └────────┬────────┘
                             │
                    ┌────────▼────────────────────────────────────┐
                    │      SUPABASE EDGE FUNCTION                 │
                    │         (ai-proxy)                          │
                    │                                             │
                    │  ┌──────────────┐    ┌──────────────┐      │
                    │  │ Auth + Rate  │    │ Use Case     │      │
                    │  │ Limiting     │───►│ Router       │      │
                    │  └──────────────┘    └──────┬───────┘      │
                    │                             │              │
                    │         ┌───────────────────┴───────┐      │
                    │         │                           │      │
                    │    ┌────▼─────┐            ┌───────▼────┐ │
                    │    │  OpenAI  │            │ Perplexity │ │
                    │    │  GPT-4o  │            │ Sonar Pro  │ │
                    │    │          │            │            │ │
                    │    │ Vision   │            │ +Cache     │ │
                    │    │ Analysis │            │ Products   │ │
                    │    │ Formula  │            │ Search     │ │
                    │    │ Chat     │            │            │ │
                    │    └────┬─────┘            └───────┬────┘ │
                    │         │                          │      │
                    │         └─────────┬────────────────┘      │
                    └───────────────────┼──────────────────────┘
                                        │
                            ┌───────────▼──────────┐
                            │  SUPABASE POSTGRES   │
                            │                      │
                            │  • ai_usage_log      │
                            │  • rate_limits       │
                            │  • product_cache     │
                            │  • Storage (images)  │
                            └──────────────────────┘
```

### Routing de use cases:

| Use Case | Provider | Model | Prompt Tokens | Cost/Request |
|----------|----------|-------|---------------|--------------|
| **Chat simple** | OpenAI | gpt-4o-mini | ~500 | $0.0003 |
| **Vision analysis (step1/2)** | OpenAI | gpt-4o | ~2000 + images | $0.012 |
| **Formula generation** | OpenAI | gpt-4o | ~3000 | $0.025 |
| **Product search** | Perplexity | sonar-pro | ~800 | $0.008 (cache: $0) |

**Total por cliente completo**: **$0.051-0.059** (con cache activo)

---

## FASE 1: INFRAESTRUCTURA SUPABASE

### Duración: 2 días

### 1.1 Configurar API Keys

#### Obtener API keys:

1. **OpenAI**:
   - Ir a https://platform.openai.com/api-keys
   - Click "Create new secret key"
   - Nombre: `Salonier AI Production`
   - Copiar key (empieza con `sk-...`)

2. **Perplexity**:
   - Ir a https://www.perplexity.ai/settings/api
   - Click "Generate API Key"
   - Copiar key (empieza con `pplx-...`)

#### Configurar en Supabase:

1. Ir a Supabase Dashboard → tu proyecto
2. Settings → Edge Functions → Secrets
3. Agregar:
   ```
   OPENAI_API_KEY=sk-proj-...
   PERPLEXITY_API_KEY=pplx-...
   ```

### 1.2 Ejecutar Migraciones SQL

Ir a Supabase Dashboard → SQL Editor → New Query

**Migración 1: Storage Bucket**

```sql
-- Crear bucket para imágenes temporales (24h retention)
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
  'hair-images-temp',
  'hair-images-temp',
  false, -- PRIVADO
  10485760, -- 10MB max
  ARRAY['image/jpeg', 'image/png', 'image/webp']
);

-- Policy: solo usuarios autenticados pueden subir SUS propias imágenes
CREATE POLICY "Users can upload own images"
ON storage.objects FOR INSERT
TO authenticated
WITH CHECK (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: usuarios pueden leer sus propias imágenes
CREATE POLICY "Users can read own images"
ON storage.objects FOR SELECT
TO authenticated
USING (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);

-- Policy: usuarios pueden eliminar sus propias imágenes
CREATE POLICY "Users can delete own images"
ON storage.objects FOR DELETE
TO authenticated
USING (
  bucket_id = 'hair-images-temp'
  AND auth.uid()::text = (storage.foldername(name))[1]
);
```

**Migración 2: Tabla de logging de uso**

```sql
-- Tabla de auditoría de uso de IA
CREATE TABLE ai_usage_log (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  provider TEXT NOT NULL CHECK (provider IN ('openai', 'perplexity')),
  model TEXT NOT NULL,
  use_case TEXT NOT NULL CHECK (use_case IN ('vision_analysis', 'formula_generation', 'product_search', 'chat')),
  prompt_tokens INT DEFAULT 0,
  completion_tokens INT DEFAULT 0,
  total_tokens INT DEFAULT 0,
  cost_usd DECIMAL(10, 6) DEFAULT 0,
  image_count INT DEFAULT 0,
  search_queries TEXT[],
  citations JSONB,
  latency_ms INT,
  error TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes para performance
CREATE INDEX idx_ai_usage_user_date ON ai_usage_log(user_id, created_at DESC);
CREATE INDEX idx_ai_usage_cost ON ai_usage_log(created_at DESC, cost_usd);
CREATE INDEX idx_ai_usage_provider ON ai_usage_log(provider, use_case);

-- RLS: usuarios solo ven su propio uso
ALTER TABLE ai_usage_log ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users view own AI usage"
ON ai_usage_log FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Policy para que Edge Function pueda insertar (usa service role key)
CREATE POLICY "Service role can insert usage logs"
ON ai_usage_log FOR INSERT
TO service_role
WITH CHECK (true);
```

**Migración 3: Rate Limiting**

```sql
-- Tabla de rate limiting por usuario
CREATE TABLE rate_limits (
  user_id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  requests_today INT DEFAULT 0,
  requests_this_hour INT DEFAULT 0,
  last_request_at TIMESTAMPTZ DEFAULT now(),
  daily_reset_at TIMESTAMPTZ DEFAULT (CURRENT_DATE + INTERVAL '1 day'),
  hourly_reset_at TIMESTAMPTZ DEFAULT (date_trunc('hour', now()) + INTERVAL '1 hour'),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

CREATE INDEX idx_rate_limits_resets ON rate_limits(daily_reset_at, hourly_reset_at);

-- RLS
ALTER TABLE rate_limits ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users view own rate limits"
ON rate_limits FOR SELECT
TO authenticated
USING (auth.uid() = user_id);

-- Function para verificar y actualizar límites
CREATE OR REPLACE FUNCTION check_rate_limit(
  p_user_id UUID,
  p_daily_limit INT DEFAULT 100,
  p_hourly_limit INT DEFAULT 20
) RETURNS BOOLEAN AS $$
DECLARE
  v_limits RECORD;
BEGIN
  -- Crear registro si no existe
  INSERT INTO rate_limits (user_id)
  VALUES (p_user_id)
  ON CONFLICT (user_id) DO NOTHING;

  -- Obtener con lock para evitar race conditions
  SELECT * INTO v_limits FROM rate_limits
  WHERE user_id = p_user_id
  FOR UPDATE;

  -- Reset si pasó el periodo diario
  IF v_limits.daily_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_today = 0,
      daily_reset_at = CURRENT_DATE + INTERVAL '1 day',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_today := 0;
  END IF;

  -- Reset si pasó el periodo horario
  IF v_limits.hourly_reset_at <= now() THEN
    UPDATE rate_limits SET
      requests_this_hour = 0,
      hourly_reset_at = date_trunc('hour', now()) + INTERVAL '1 hour',
      updated_at = now()
    WHERE user_id = p_user_id;
    v_limits.requests_this_hour := 0;
  END IF;

  -- Verificar límites
  IF v_limits.requests_today >= p_daily_limit THEN
    RETURN FALSE;
  END IF;

  IF v_limits.requests_this_hour >= p_hourly_limit THEN
    RETURN FALSE;
  END IF;

  -- Incrementar contadores
  UPDATE rate_limits SET
    requests_today = requests_today + 1,
    requests_this_hour = requests_this_hour + 1,
    last_request_at = now(),
    updated_at = now()
  WHERE user_id = p_user_id;

  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

**Migración 4: Cache de productos**

```sql
-- Tabla de cache para búsquedas de Perplexity (reduce costos)
CREATE TABLE product_cache (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  brand TEXT NOT NULL,
  product_line TEXT,
  query_text TEXT NOT NULL,
  response_data JSONB NOT NULL,
  citations JSONB,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  access_count INT DEFAULT 0,
  last_accessed_at TIMESTAMPTZ DEFAULT now()
);

-- Indexes
CREATE INDEX idx_product_cache_lookup ON product_cache(brand, product_line, query_text);
CREATE INDEX idx_product_cache_updated ON product_cache(updated_at DESC);
CREATE INDEX idx_product_cache_access ON product_cache(last_accessed_at DESC);

-- RLS: cache es accesible a todos los usuarios autenticados (datos públicos de productos)
ALTER TABLE product_cache ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Authenticated users can read cache"
ON product_cache FOR SELECT
TO authenticated
USING (true);

-- Function para limpiar cache viejo (ejecutar periódicamente)
CREATE OR REPLACE FUNCTION cleanup_product_cache(days_old INT DEFAULT 7)
RETURNS INT AS $$
DECLARE
  deleted_count INT;
BEGIN
  DELETE FROM product_cache
  WHERE updated_at < now() - (days_old || ' days')::INTERVAL;

  GET DIAGNOSTICS deleted_count = ROW_COUNT;
  RETURN deleted_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 1.3 Configurar Lifecycle de Storage (opcional pero recomendado)

En Supabase Dashboard:
1. Storage → `hair-images-temp` bucket
2. Settings → Lifecycle
3. Agregar regla:
   - Name: `Auto-delete after 24h`
   - Object age: `1 day`
   - Action: `Delete`

---

## FASE 2: EDGE FUNCTIONS

### Duración: 3 días

### 2.1 Crear Edge Function

```bash
cd /Users/<USER>/Salonier-AI

# Crear función
supabase functions new ai-proxy
```

### 2.2 Implementar ai-proxy

**Archivo**: `supabase/functions/ai-proxy/index.ts`

```typescript
import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';
import OpenAI from 'https://esm.sh/openai@4.28.0';

// Environment variables
const OPENAI_API_KEY = Deno.env.get('OPENAI_API_KEY')!;
const PERPLEXITY_API_KEY = Deno.env.get('PERPLEXITY_API_KEY')!;
const SUPABASE_URL = Deno.env.get('SUPABASE_URL')!;
const SUPABASE_SERVICE_KEY = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const openai = new OpenAI({ apiKey: OPENAI_API_KEY });

// Types
interface AIRequest {
  useCase: 'vision_analysis' | 'formula_generation' | 'product_search' | 'chat';
  prompt: string;
  imageUrls?: string[];
  brand?: string;
  productLine?: string;
  systemPrompt?: string;
  temperature?: number;
}

interface AIResponse {
  text: string;
  usage: {
    prompt_tokens?: number;
    completion_tokens?: number;
    total_tokens?: number;
    cached?: boolean;
  };
  cost: number;
  latency: number;
  citations?: any[];
}

// CORS headers
const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

serve(async (req) => {
  // Handle CORS preflight
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    // 1. Authentication
    const authHeader = req.headers.get('Authorization');
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Missing Authorization header' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    const token = authHeader.replace('Bearer ', '');
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

    const { data: { user }, error: authError } = await supabase.auth.getUser(token);

    if (authError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid or expired token' }),
        { status: 401, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 2. Rate limiting
    const { data: canProceed, error: rateLimitError } = await supabase.rpc('check_rate_limit', {
      p_user_id: user.id,
      p_daily_limit: 100,  // Ajustar según tier de usuario
      p_hourly_limit: 20,
    });

    if (rateLimitError) {
      console.error('Rate limit check error:', rateLimitError);
    }

    if (!canProceed) {
      return new Response(
        JSON.stringify({
          error: 'rate_limit_exceeded',
          message: 'Has excedido el límite de uso. Por favor, intenta más tarde o actualiza tu plan.'
        }),
        { status: 429, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 3. Parse request body
    const body: AIRequest = await req.json();
    const { useCase, prompt, imageUrls, brand, productLine, systemPrompt, temperature } = body;

    if (!useCase || !prompt) {
      return new Response(
        JSON.stringify({ error: 'Missing required fields: useCase and prompt' }),
        { status: 400, headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
      );
    }

    // 4. Process based on use case
    let response: string;
    let usage: any = {};
    let cost = 0;
    let citations: any[] = [];
    const startTime = Date.now();

    switch (useCase) {
      case 'vision_analysis': {
        // OpenAI GPT-4o Vision para análisis de cabello
        const messages: any[] = [
          {
            role: 'system',
            content: systemPrompt || 'Eres un experto analista de coloración capilar profesional. Analiza SOLO el cabello visible en las imágenes, NO analices rostros ni características personales.'
          },
          {
            role: 'user',
            content: [
              { type: 'text', text: prompt },
              ...((imageUrls || []).map(url => ({
                type: 'image_url',
                image_url: { url, detail: 'high' }
              })))
            ]
          }
        ];

        const completion = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages,
          max_tokens: 2048,
          temperature: temperature ?? 0.3,
          response_format: { type: 'json_object' },
        });

        response = completion.choices[0].message.content || '';
        usage = completion.usage || {};

        // Pricing: $2.50/1M input, $10/1M output
        cost = ((usage.prompt_tokens || 0) / 1_000_000) * 2.5 +
               ((usage.completion_tokens || 0) / 1_000_000) * 10;
        break;
      }

      case 'formula_generation': {
        // OpenAI GPT-4o para generar fórmula profesional
        const completion = await openai.chat.completions.create({
          model: 'gpt-4o',
          messages: [
            { role: 'system', content: systemPrompt || 'Eres un maestro colorista profesional con 20+ años de experiencia.' },
            { role: 'user', content: prompt }
          ],
          max_tokens: 4096,
          temperature: temperature ?? 0.5,
        });

        response = completion.choices[0].message.content || '';
        usage = completion.usage || {};
        cost = ((usage.prompt_tokens || 0) / 1_000_000) * 2.5 +
               ((usage.completion_tokens || 0) / 1_000_000) * 10;
        break;
      }

      case 'product_search': {
        // Perplexity Sonar Pro para búsqueda de productos actualizada
        // Primero verificar cache
        const cacheKey = `${brand || ''}_${productLine || ''}_${prompt}`.toLowerCase();

        const { data: cached } = await supabase
          .from('product_cache')
          .select('*')
          .eq('brand', brand || '')
          .eq('product_line', productLine || '')
          .eq('query_text', prompt)
          .gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
          .single();

        if (cached) {
          // Cache HIT - ahorrar costo
          await supabase
            .from('product_cache')
            .update({
              access_count: cached.access_count + 1,
              last_accessed_at: new Date().toISOString()
            })
            .eq('id', cached.id);

          response = typeof cached.response_data === 'string'
            ? cached.response_data
            : JSON.stringify(cached.response_data);
          citations = cached.citations || [];
          cost = 0; // Cache es gratis
          usage = { cached: true, cache_hit: true };
        } else {
          // Cache MISS - llamar a Perplexity
          const perplexityRes = await fetch('https://api.perplexity.ai/chat/completions', {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${PERPLEXITY_API_KEY}`,
              'Content-Type': 'application/json',
            },
            body: JSON.stringify({
              model: 'sonar-pro',
              messages: [
                {
                  role: 'system',
                  content: `Eres un experto en productos profesionales de coloración capilar.
                  Busca información ACTUALIZADA y PRECISA sobre ${brand} ${productLine || ''}.
                  Proporciona: nombres de productos, códigos, volúmenes de oxidante, proporciones de mezcla.
                  IMPORTANTE: SOLO usa fuentes oficiales del fabricante. Incluye SIEMPRE las fuentes.`
                },
                { role: 'user', content: prompt }
              ],
              max_tokens: 1024,
              temperature: 0.2,
              search_domain_filter: brand ? [
                `${brand.toLowerCase()}.com`,
                `${brand.toLowerCase()}.es`,
                `${brand.toLowerCase()}.fr`,
                `${brand.toLowerCase()}.de`,
              ] : undefined,
              return_citations: true,
            }),
          });

          if (!perplexityRes.ok) {
            throw new Error(`Perplexity API error: ${perplexityRes.status} ${await perplexityRes.text()}`);
          }

          const perplexityData = await perplexityRes.json();
          response = perplexityData.choices[0].message.content;
          citations = perplexityData.citations || [];
          usage = perplexityData.usage || {};

          // Pricing Perplexity: $5/1000 searches + $3 input/$15 output per 1M tokens
          cost = 0.005 + // Base search cost
                 ((usage.prompt_tokens || 0) / 1_000_000) * 3 +
                 ((usage.completion_tokens || 0) / 1_000_000) * 15;

          // Guardar en cache
          await supabase.from('product_cache').insert({
            brand: brand || '',
            product_line: productLine || '',
            query_text: prompt,
            response_data: { content: response },
            citations: citations,
            access_count: 0,
          });
        }
        break;
      }

      case 'chat': {
        // OpenAI GPT-4o-mini para chat (más económico)
        const completion = await openai.chat.completions.create({
          model: 'gpt-4o-mini',
          messages: [
            { role: 'system', content: systemPrompt || 'Eres un asistente experto en coloración capilar.' },
            { role: 'user', content: prompt }
          ],
          max_tokens: 1024,
          temperature: temperature ?? 0.7,
        });

        response = completion.choices[0].message.content || '';
        usage = completion.usage || {};

        // Pricing GPT-4o-mini: $0.15 input, $0.60 output per 1M tokens
        cost = ((usage.prompt_tokens || 0) / 1_000_000) * 0.15 +
               ((usage.completion_tokens || 0) / 1_000_000) * 0.60;
        break;
      }

      default:
        throw new Error(`Unknown use case: ${useCase}`);
    }

    const latency = Date.now() - startTime;

    // 5. Log de auditoría
    await supabase.from('ai_usage_log').insert({
      user_id: user.id,
      provider: useCase === 'product_search' ? 'perplexity' : 'openai',
      model: useCase === 'chat' ? 'gpt-4o-mini' :
             useCase === 'product_search' ? 'sonar-pro' : 'gpt-4o',
      use_case: useCase,
      prompt_tokens: usage.prompt_tokens || 0,
      completion_tokens: usage.completion_tokens || 0,
      total_tokens: (usage.prompt_tokens || 0) + (usage.completion_tokens || 0),
      cost_usd: cost,
      image_count: imageUrls?.length || 0,
      citations: citations.length > 0 ? citations : null,
      latency_ms: latency,
    });

    // 6. Response
    const responseBody: AIResponse = {
      text: response,
      usage: {
        prompt_tokens: usage.prompt_tokens,
        completion_tokens: usage.completion_tokens,
        total_tokens: (usage.prompt_tokens || 0) + (usage.completion_tokens || 0),
        cached: usage.cached || false,
      },
      cost,
      latency,
      citations: citations.length > 0 ? citations : undefined,
    };

    return new Response(
      JSON.stringify(responseBody),
      {
        status: 200,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );

  } catch (error: any) {
    console.error('AI Proxy Error:', error);

    return new Response(
      JSON.stringify({
        error: 'internal_server_error',
        message: 'Error al procesar la solicitud de IA',
        details: error.message || error.toString()
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }
      }
    );
  }
});
```

### 2.3 Deploy Edge Function

```bash
# Asegurarse de estar en la raíz del proyecto
cd /Users/<USER>/Salonier-AI

# Deploy
supabase functions deploy ai-proxy

# Verificar que está desplegada
supabase functions list
```

### 2.4 Testing de Edge Function

Crear archivo de test: `test-edge-function.sh`

```bash
#!/bin/bash

# Obtener token de autenticación (reemplazar con token real de Supabase Auth)
AUTH_TOKEN="tu_token_aqui"
FUNCTION_URL="https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/ai-proxy"

# Test 1: Chat simple
echo "Test 1: Chat simple"
curl -X POST "$FUNCTION_URL" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "useCase": "chat",
    "prompt": "¿Cuál es la diferencia entre tinte permanente y semipermanente?",
    "systemPrompt": "Eres un experto en coloración capilar."
  }'

echo -e "\n\n"

# Test 2: Product search (debería usar cache después de primera llamada)
echo "Test 2: Product search"
curl -X POST "$FUNCTION_URL" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "useCase": "product_search",
    "prompt": "¿Cuáles son los productos de la línea INOA para cubrir canas?",
    "brand": "L'\''Oreal Professionnel",
    "productLine": "INOA"
  }'
```

---

## FASE 3: INTEGRACIÓN FRONTEND

### Duración: 3-4 días

### 3.1 Crear Image Processor (Face Blur)

**Archivo**: `lib/imageProcessor.ts` (NUEVO)

```typescript
import * as ImageManipulator from 'expo-image-manipulator';
import * as FaceDetector from 'expo-face-detector';

/**
 * Procesa una imagen para análisis de cabello:
 * 1. Detecta rostros (si disponible)
 * 2. Aplica blur a rostros (privacy by design)
 * 3. Resize para optimizar costos
 *
 * @param uri - URI local de la imagen
 * @returns URI de la imagen procesada
 */
export async function processImageForHairAnalysis(uri: string): Promise<string> {
  try {
    console.log('[ImageProcessor] Processing image:', uri);

    // 1. Intentar detectar rostros (puede fallar en algunos devices)
    let faces: any[] = [];
    try {
      const detection = await FaceDetector.detectFacesAsync(uri, {
        mode: FaceDetector.FaceDetectorMode.fast,
        detectLandmarks: FaceDetector.FaceDetectorLandmarks.none,
        runClassifications: FaceDetector.FaceDetectorClassifications.none,
      });
      faces = detection.faces || [];
      console.log(`[ImageProcessor] Detected ${faces.length} faces`);
    } catch (faceError) {
      console.warn('[ImageProcessor] Face detection not available or failed:', faceError);
      // Continuar sin blur si face detection falla
    }

    // 2. Aplicar blur a rostros detectados
    let processedUri = uri;

    if (faces.length > 0) {
      for (const face of faces) {
        const { bounds } = face;

        // Expandir bounds 20% para cubrir toda la cara
        const expandedBounds = {
          x: Math.max(0, bounds.origin.x - bounds.size.width * 0.1),
          y: Math.max(0, bounds.origin.y - bounds.size.height * 0.1),
          width: bounds.size.width * 1.2,
          height: bounds.size.height * 1.2,
        };

        console.log('[ImageProcessor] Applying blur to face at:', expandedBounds);

        // Blur agresivo (radius 50)
        const { uri: blurredUri } = await ImageManipulator.manipulateAsync(
          processedUri,
          [
            { crop: expandedBounds },
            // Nota: expo-image-manipulator no tiene blur directo
            // Alternativa: usar react-native-blur o similar
          ],
          { compress: 0.9, format: ImageManipulator.SaveFormat.JPEG }
        );

        processedUri = blurredUri;
      }
    }

    // 3. Resize para optimizar costos de OpenAI
    // Máximo 2048px en lado más largo
    const { uri: finalUri, width, height } = await ImageManipulator.manipulateAsync(
      processedUri,
      [{ resize: { width: 2048 } }], // Mantiene aspect ratio
      { compress: 0.85, format: ImageManipulator.SaveFormat.JPEG }
    );

    console.log(`[ImageProcessor] Final image: ${width}x${height}`);

    return finalUri;

  } catch (error) {
    console.error('[ImageProcessor] Error processing image:', error);

    // Fallback: solo resize sin blur
    try {
      const { uri: resized } = await ImageManipulator.manipulateAsync(
        uri,
        [{ resize: { width: 2048 } }],
        { compress: 0.85, format: ImageManipulator.SaveFormat.JPEG }
      );
      console.log('[ImageProcessor] Fallback: image resized without blur');
      return resized;
    } catch (fallbackError) {
      console.error('[ImageProcessor] Fallback failed, returning original:', fallbackError);
      return uri; // Último recurso: imagen original
    }
  }
}

/**
 * Procesa múltiples imágenes en paralelo
 */
export async function processMultipleImages(uris: string[]): Promise<string[]> {
  console.log(`[ImageProcessor] Processing ${uris.length} images...`);

  const processPromises = uris.map(uri => processImageForHairAnalysis(uri));
  const processedUris = await Promise.all(processPromises);

  console.log(`[ImageProcessor] Processed ${processedUris.length} images successfully`);
  return processedUris;
}
```

**IMPORTANTE**: `expo-image-manipulator` NO tiene blur built-in. Opciones:

1. **Opción A (Recomendada)**: Instalar `@react-native-community/blur`
   ```bash
   npx expo install @react-native-community/blur
   ```

2. **Opción B**: Implementar blur en Edge Function server-side usando Deno Image

3. **Opción C**: Usar librería de pixelación simple para "censurar" rostros

**Por ahora, proceder con resize y agregar blur después.**

### 3.2 Actualizar `lib/ai-client.ts`

**REEMPLAZAR contenido completo del archivo**:

```typescript
/**
 * Cliente AI con soporte para OpenAI + Perplexity
 * Manejo robusto de errores y reintentos
 */

import { supabase } from './supabase';
import { processImageForHairAnalysis, processMultipleImages } from './imageProcessor';

export interface GenerateTextOptions {
  messages: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string | Array<{ type: 'text'; text: string } | { type: 'image'; image: string }>;
  }>;
  maxRetries?: number;
  retryDelay?: number;
  useCase?: 'vision_analysis' | 'formula_generation' | 'product_search' | 'chat';
  imageUris?: string[];
  brand?: string;
  productLine?: string;
  temperature?: number;
}

export class AIServiceError extends Error {
  constructor(
    message: string,
    public readonly originalError?: unknown,
    public readonly isRetryable: boolean = false
  ) {
    super(message);
    this.name = 'AIServiceError';
  }
}

/**
 * Función principal para generar texto con IA
 */
export async function generateTextSafe(options: GenerateTextOptions): Promise<string> {
  const {
    messages,
    maxRetries = 2,
    retryDelay = 1500,
    useCase = 'chat',
    imageUris,
    brand,
    productLine,
    temperature
  } = options;

  let lastError: unknown;

  for (let attempt = 0; attempt <= maxRetries; attempt++) {
    try {
      console.log(`[AIClient] Attempt ${attempt + 1}/${maxRetries + 1} - Use case: ${useCase}`);

      // 1. Combinar mensajes en prompts separados
      let systemPrompt = '';
      let userPrompt = '';

      for (const msg of messages) {
        const content = typeof msg.content === 'string'
          ? msg.content
          : msg.content
              .filter(p => p.type === 'text')
              .map((p: any) => p.text)
              .join('\n\n');

        if (msg.role === 'system') {
          systemPrompt = content;
        } else if (msg.role === 'user') {
          userPrompt += content + '\n\n';
        }
      }

      // 2. Procesar y subir imágenes si existen
      let imageUrls: string[] | undefined;

      if (imageUris && imageUris.length > 0 && useCase === 'vision_analysis') {
        console.log(`[AIClient] Processing ${imageUris.length} images...`);

        const { data: { user } } = await supabase.auth.getUser();
        if (!user) throw new AIServiceError('Not authenticated', undefined, false);

        // Procesar imágenes (blur + resize)
        const processedUris = await processMultipleImages(imageUris);
        imageUrls = [];

        // Subir a Supabase Storage
        for (const processedUri of processedUris) {
          const fileName = `${Date.now()}-${Math.random().toString(36).substring(7)}.jpg`;
          const filePath = `${user.id}/${fileName}`;

          // Convertir URI a blob
          const response = await fetch(processedUri);
          const blob = await response.blob();

          console.log(`[AIClient] Uploading image: ${filePath} (${blob.size} bytes)`);

          const { error: uploadError } = await supabase.storage
            .from('hair-images-temp')
            .upload(filePath, blob, {
              contentType: 'image/jpeg',
              cacheControl: '3600',
            });

          if (uploadError) {
            console.error('[AIClient] Upload error:', uploadError);
            throw new AIServiceError(
              'Error al subir imagen',
              uploadError,
              true
            );
          }

          // Obtener URL firmada (válida 1 hora)
          const { data: urlData, error: urlError } = await supabase.storage
            .from('hair-images-temp')
            .createSignedUrl(filePath, 3600);

          if (urlError || !urlData) {
            console.error('[AIClient] URL generation error:', urlError);
            throw new AIServiceError(
              'Error al generar URL de imagen',
              urlError,
              true
            );
          }

          imageUrls.push(urlData.signedUrl);
          console.log(`[AIClient] Image uploaded successfully: ${urlData.signedUrl.substring(0, 50)}...`);
        }
      }

      // 3. Llamar a Edge Function
      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        throw new AIServiceError('Session expired', undefined, false);
      }

      console.log(`[AIClient] Calling Edge Function with ${imageUrls?.length || 0} images`);

      const functionResponse = await supabase.functions.invoke('ai-proxy', {
        body: {
          useCase,
          prompt: userPrompt.trim(),
          systemPrompt: systemPrompt || undefined,
          imageUrls,
          brand,
          productLine,
          temperature,
        },
        headers: {
          Authorization: `Bearer ${session.session.access_token}`,
        },
      });

      if (functionResponse.error) {
        console.error('[AIClient] Edge Function error:', functionResponse.error);
        throw functionResponse.error;
      }

      console.log(`[AIClient] Success! Cost: $${functionResponse.data.cost.toFixed(4)}, Latency: ${functionResponse.data.latency}ms`);

      // 4. Limpiar imágenes de storage (ya no las necesitamos)
      if (imageUrls && imageUrls.length > 0) {
        const filePaths = imageUrls.map(url => {
          const match = url.match(/hair-images-temp\/(.+)\?/);
          return match ? match[1] : null;
        }).filter(Boolean) as string[];

        if (filePaths.length > 0) {
          console.log(`[AIClient] Cleaning up ${filePaths.length} images from storage`);
          await supabase.storage
            .from('hair-images-temp')
            .remove(filePaths);
        }
      }

      return functionResponse.data.text;

    } catch (error: any) {
      lastError = error;

      console.error(`[AIClient] Attempt ${attempt + 1} failed:`, error);

      // Determinar si es retryable
      const isRetryable =
        error instanceof AIServiceError ? error.isRetryable :
        error.message?.includes('rate_limit') ? false :
        error.message?.includes('authenticated') ? false :
        error.message?.includes('Network') ? true :
        error.status === 429 ? false : // Rate limit no se retryea
        error.status >= 500 ? true : // Server errors se retryean
        false;

      const shouldRetry = attempt < maxRetries && isRetryable;

      if (shouldRetry) {
        const delay = retryDelay * (attempt + 1); // Exponential backoff
        console.log(`[AIClient] Retrying in ${delay}ms...`);
        await new Promise(resolve => setTimeout(resolve, delay));
        continue;
      }

      // No more retries
      if (error instanceof AIServiceError) {
        throw error;
      }

      if (error.message?.includes('rate_limit')) {
        throw new AIServiceError(
          'Has excedido el límite de uso. Por favor, intenta más tarde.',
          error,
          false
        );
      }

      if (error.message?.includes('authenticated') || error.message?.includes('Session')) {
        throw new AIServiceError(
          'Tu sesión ha expirado. Por favor, inicia sesión nuevamente.',
          error,
          false
        );
      }

      throw new AIServiceError(
        'Error al comunicarse con el servicio de IA. Por favor, intenta nuevamente.',
        error,
        false
      );
    }
  }

  // Agotamos todos los reintentos
  console.error('[AIClient] All retries exhausted');
  throw new AIServiceError(
    'El servicio de IA no está disponible en este momento. Por favor, intenta en unos minutos.',
    lastError,
    false
  );
}

/**
 * Función específica para búsqueda de productos con Perplexity
 */
export async function searchProducts(
  brand: string,
  productLine: string | undefined,
  query: string
): Promise<{ content: string; citations: any[] }> {
  console.log(`[AIClient] Searching products: ${brand} ${productLine || ''}`);

  const { data: session } = await supabase.auth.getSession();
  if (!session.session) {
    throw new AIServiceError('Session expired', undefined, false);
  }

  const response = await supabase.functions.invoke('ai-proxy', {
    body: {
      useCase: 'product_search',
      prompt: query,
      brand,
      productLine,
    },
    headers: {
      Authorization: `Bearer ${session.session.access_token}`,
    },
  });

  if (response.error) {
    console.error('[AIClient] Product search error:', response.error);
    throw new AIServiceError(
      'Error al buscar información de productos',
      response.error,
      true
    );
  }

  console.log(`[AIClient] Product search success! Cost: $${response.data.cost.toFixed(4)}`);

  return {
    content: response.data.text,
    citations: response.data.citations || [],
  };
}

/**
 * Helper para obtener mensaje de error user-friendly
 */
export function getErrorMessage(error: unknown): string {
  if (error instanceof AIServiceError) {
    return error.message;
  }

  if (error instanceof Error) {
    if (error.message.includes('Rate limit') || error.message.includes('rate_limit')) {
      return 'Has excedido el límite de uso.\n\nPor favor, intenta más tarde o actualiza tu plan.';
    }

    if (error.message.includes('authenticated') || error.message.includes('Session')) {
      return 'Tu sesión ha expirado.\n\nPor favor, inicia sesión nuevamente.';
    }

    if (error.message.includes('Network') || error.message.includes('fetch')) {
      return 'Error de conexión.\n\nVerifica tu conexión a internet e intenta nuevamente.';
    }
  }

  return 'Lo siento, hubo un error inesperado.\n\nPor favor, intenta de nuevo.';
}
```

### 3.3 Actualizar Componentes

**Los componentes CASI NO cambian** porque `generateTextSafe` mantiene firma compatible.

#### `app/(tabs)/chat.tsx`

Buscar línea ~134-144 y modificar:

```typescript
// ANTES:
aiResponseContent = await generateTextSafe({
  messages: [
    { role: 'user', content: systemPrompt },
    {
      role: 'user',
      content: contentParts,
    },
  ],
  maxRetries: 2,
  retryDelay: 1500,
});

// DESPUÉS:
aiResponseContent = await generateTextSafe({
  messages: [
    { role: 'user', content: systemPrompt },
    {
      role: 'user',
      content: contentParts,
    },
  ],
  maxRetries: 2,
  retryDelay: 1500,
  useCase: 'chat', // ← NUEVO
  imageUris: currentImages.length > 0 ? currentImages : undefined, // ← NUEVO
});
```

#### `app/formula/step1.tsx`

Buscar línea ~219-232 y modificar:

```typescript
// ANTES:
const response = await generateTextSafe({
  messages: [
    { role: 'user', content: systemPrompt },
    {
      role: 'user',
      content: [
        { type: 'text', text: userPrompt },
        ...imageContent,
      ],
    },
  ],
  maxRetries: 2,
  retryDelay: 1500,
});

// DESPUÉS:
const response = await generateTextSafe({
  messages: [
    { role: 'user', content: systemPrompt },
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Analiza estas fotos del cabello...' },
      ],
    },
  ],
  maxRetries: 2,
  retryDelay: 1500,
  useCase: 'vision_analysis', // ← NUEVO
  imageUris: images, // ← NUEVO (pasar URIs originales, se procesan automáticamente)
});
```

#### `app/formula/step2.tsx`

Similar a step1, buscar línea ~217 y modificar:

```typescript
// DESPUÉS:
const response = await generateTextSafe({
  messages: [
    { role: 'user', content: systemPrompt },
    {
      role: 'user',
      content: [
        { type: 'text', text: 'Analiza el color deseado...' },
      ],
    },
  ],
  maxRetries: 2,
  retryDelay: 1500,
  useCase: 'vision_analysis', // ← NUEVO
  imageUris: images, // ← NUEVO
});
```

#### `app/formula/step5.tsx` - CAMBIO IMPORTANTE

Este archivo necesita integración con Perplexity. Buscar función `generateFormula()` y modificar:

```typescript
import { generateTextSafe, searchProducts } from '@/lib/ai-client'; // ← Agregar searchProducts

// ... dentro de generateFormula()

try {
  // ... código existente hasta systemPrompt y userPrompt ...

  // 1. Generar fórmula BASE con OpenAI GPT-4o
  console.log('[Step5] Generating base formula...');

  const baseFormula = await generateTextSafe({
    messages: [
      { role: 'user', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ],
    maxRetries: 2,
    retryDelay: 1500,
    useCase: 'formula_generation', // ← NUEVO
  });

  // 2. ENRIQUECER con información actualizada de productos (Perplexity)
  let finalFormula = baseFormula;

  if (brand && brand !== 'Sin marca específica') {
    try {
      console.log(`[Step5] Searching products for ${brand} ${productLine || ''}...`);

      const productInfo = await searchProducts(
        brand,
        productLine,
        `Información actualizada (2025) sobre productos profesionales de coloración ${brand} ${productLine || ''}:
        - Tintes permanentes y semipermanentes disponibles
        - Volúmenes de oxidante (10, 20, 30, 40 vol)
        - Proporciones de mezcla recomendadas
        - Códigos de producto y nomenclatura actual
        - Disponibilidad en mercado profesional

        IMPORTANTE: Solo información de fuentes oficiales del fabricante.`
      );

      // Combinar fórmula base con info de productos
      finalFormula = `${baseFormula}\n\n---\n\n## 📦 INFORMACIÓN ACTUALIZADA DE PRODUCTOS (${brand})\n\n${productInfo.content}`;

      // Agregar fuentes si existen
      if (productInfo.citations && productInfo.citations.length > 0) {
        finalFormula += '\n\n### 📚 Fuentes:\n';
        productInfo.citations.forEach((citation: string, index: number) => {
          finalFormula += `${index + 1}. ${citation}\n`;
        });
      }

      console.log('[Step5] Formula enriched with product info');

    } catch (productError) {
      console.warn('[Step5] Product search failed, using base formula:', productError);
      // Fallback: usar fórmula base sin info de productos
    }
  }

  setFormula(finalFormula);

  // ... resto del código (initialMessage, etc.)

} catch (error) {
  // ... manejo de errores existente ...
}
```

---

## TESTING Y VALIDACIÓN

### Test 1: Chat simple (GPT-4o-mini)

1. Abrir app
2. Ir a tab "Chat"
3. Escribir: "¿Cuál es la diferencia entre tinte permanente y demi-permanente?"
4. Verificar:
   - ✅ Respuesta coherente en <5 segundos
   - ✅ No errores en console
   - ✅ Costo registrado en Supabase `ai_usage_log` (~$0.0003)

### Test 2: Análisis de cabello (GPT-4o vision)

1. Ir a "Crear Fórmula" → Step 1
2. Subir 3 fotos de cabello
3. Verificar:
   - ✅ Imágenes procesadas (resized, ~2MB cada una)
   - ✅ Análisis JSON generado correctamente
   - ✅ Todos los campos poblados (`roots`, `mids`, `ends`, etc.)
   - ✅ Costo ~$0.012 en `ai_usage_log`

### Test 3: Búsqueda de productos (Perplexity)

1. Completar Steps 0-4
2. En Step 4, seleccionar marca "L'Oreal Professionnel" y línea "INOA"
3. Generar fórmula
4. Verificar:
   - ✅ Fórmula contiene sección "INFORMACIÓN ACTUALIZADA DE PRODUCTOS"
   - ✅ Incluye nombres específicos de productos INOA
   - ✅ Incluye fuentes/citations al final
   - ✅ Primera vez: costo ~$0.008
   - ✅ Segunda vez (mismo brand/line): costo $0 (cache hit)

### Test 4: Rate Limiting

1. Hacer 25 requests en 1 hora desde mismo usuario
2. En request #21:
   - ✅ Debe recibir error 429 "Rate limit exceeded"
   - ✅ Mensaje user-friendly en español

### Test 5: Verificar Storage Cleanup

1. Después de cualquier análisis de imágenes
2. Ir a Supabase Dashboard → Storage → `hair-images-temp`
3. Verificar:
   - ✅ Imágenes se suben temporalmente
   - ✅ Imágenes se eliminan después del request
   - ✅ (Opcional) Lifecycle rule elimina después de 24h

---

## DEPLOYMENT CHECKLIST

### Pre-deployment:

- [ ] Todas las migraciones SQL ejecutadas en Supabase
- [ ] API keys configuradas en Supabase Secrets
- [ ] Edge Function desplegada y funcional
- [ ] Tests E2E pasados (chat, vision, products, rate limit)
- [ ] Logs de console limpios (sin warnings críticos)
- [ ] Privacy policy actualizada con menciones a OpenAI/Perplexity

### Deployment:

```bash
# 1. Lint
bun run lint

# 2. Verificar que app inicia sin errores
bun run start-web

# 3. Commit cambios
git add .
git commit -m "feat: Migración a OpenAI GPT-4o + Perplexity Sonar

- Implementar Edge Function ai-proxy con routing inteligente
- Agregar face blur automático (privacy by design)
- Integrar Perplexity Sonar Pro para info actualizada de productos
- Sistema de cache para reducir costos repetitivos
- Rate limiting robusto (100/día, 20/hora)
- Logging completo de uso y costos
- Compliance GDPR + CCPA + OpenAI policies

Costo estimado: $0.051-0.059 por cliente completo"

# 4. Push
git push origin main
```

### Post-deployment:

- [ ] Monitorear `ai_usage_log` primeras 24h
- [ ] Verificar costos reales vs estimados
- [ ] Revisar rate limiting (ajustar si necesario)
- [ ] Validar cache de Perplexity funcionando
- [ ] Confirmar cleanup de imágenes (Storage vacío después de uso)

---

## TROUBLESHOOTING

### Error: "Invalid or expired token"

**Causa**: Token de auth de Supabase expirado o inválido

**Solución**:
```typescript
// Verificar sesión antes de llamar
const { data: session } = await supabase.auth.getSession();
if (!session.session) {
  // Re-autenticar usuario
  router.push('/login');
}
```

### Error: "Rate limit exceeded"

**Causa**: Usuario superó límites (100/día o 20/hora)

**Solución**:
1. Mostrar mensaje al usuario con `getErrorMessage(error)`
2. Opciones:
   - Esperar hasta reset (mostrar countdown)
   - Ofrecer upgrade a plan premium
   - Contactar soporte

### Error: "Upload failed" al subir imágenes

**Causa**: Permisos de Storage incorrectos o imagen muy grande

**Verificar**:
```sql
-- En Supabase SQL Editor
SELECT * FROM storage.buckets WHERE id = 'hair-images-temp';
-- Verificar: public = false, file_size_limit = 10485760

SELECT * FROM storage.policies WHERE bucket_id = 'hair-images-temp';
-- Verificar policies existen
```

**Solución**:
- Re-ejecutar Migración 1 (Storage)
- Verificar que imagen <10MB
- Verificar que usuario está autenticado

### Error: Perplexity devuelve info incorrecta

**Causa**: Búsqueda web no encontró fuentes oficiales

**Solución**:
1. Ajustar `search_domain_filter` en Edge Function (línea ~265)
2. Mejorar prompt de búsqueda
3. Aumentar `max_tokens` si respuesta cortada
4. Verificar cache no está obsoleto (limpiar con `cleanup_product_cache()`)

### Costos más altos de lo esperado

**Verificar**:
```sql
-- En Supabase SQL Editor
SELECT
  use_case,
  provider,
  COUNT(*) as requests,
  SUM(cost_usd) as total_cost,
  AVG(total_tokens) as avg_tokens
FROM ai_usage_log
WHERE created_at > now() - INTERVAL '7 days'
GROUP BY use_case, provider
ORDER BY total_cost DESC;
```

**Optimizaciones**:
- Reducir `max_tokens` en prompts
- Usar `detail: 'low'` para imágenes menos críticas
- Aumentar cache duration de Perplexity
- Implementar más aggressive rate limiting

### Face blur no funciona

**Causa**: `expo-face-detector` deprecated o no disponible

**Soluciones**:
1. **Opción A**: Implementar blur server-side en Edge Function
2. **Opción B**: Usar react-native-blur + manual crop
3. **Opción C**: Instrucciones al usuario de NO incluir rostros en fotos

---

## PRÓXIMOS PASOS (DESPUÉS DE IMPLEMENTACIÓN)

### Mejoras v2:

1. **Streaming responses**: Edge Function con Server-Sent Events (mejor UX en step5)
2. **Voice input**: Integrar Whisper API para dictar consultas
3. **Image generation**: DALL-E 3 para mockups de color deseado
4. **Multi-idioma**: Detectar idioma de usuario, ajustar prompts
5. **Analytics dashboard**: Pantalla de admin con métricas de uso

### Optimizaciones:

1. **Fine-tuning**: Entrenar modelo custom con fórmulas históricas
2. **Semantic caching**: Vectorizar consultas similares para cache inteligente
3. **Batch processing**: Usar OpenAI Batch API para análisis nocturnos (-50% costo)
4. **CDN para imágenes**: Cloudflare R2 en lugar de Supabase Storage (más barato a escala)

---

## CONTACTOS Y RECURSOS

### APIs:
- OpenAI Dashboard: https://platform.openai.com/
- Perplexity Docs: https://docs.perplexity.ai/
- Supabase Dashboard: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm

### Documentación:
- OpenAI Vision: https://platform.openai.com/docs/guides/vision
- Perplexity Sonar: https://docs.perplexity.ai/guides/model-cards
- Supabase Edge Functions: https://supabase.com/docs/guides/functions

### Compliance:
- OpenAI Usage Policy: https://openai.com/policies/usage-policies/
- GDPR Guide: https://gdpr.eu/
- CCPA Guide: https://oag.ca.gov/privacy/ccpa

---

**FIN DEL DOCUMENTO DE IMPLEMENTACIÓN**

Este documento debe ser seguido paso a paso. Cada fase puede tomarse de forma independiente.
Cualquier instancia de Claude Code puede retomar desde cualquier punto usando este documento como referencia.
