# Step5 Formula Screen - Chat Style Refactor
**Fecha**: 2025-10-27
**Estado**: En progreso
**Objetivo**: Transformar step5 de tarjetas colapsables a experiencia conversacional tipo mentor

---

## 🎯 Contexto

El usuario solicitó cambiar la experiencia de formulación de un formato de "tarjetas estructuradas" a un formato de "chat conversacional con un mentor experto". El objetivo es que el estilista sienta que tiene a un colorista master a su lado que le explica qué hacer, por qué y para qué en cada caso.

### Requerimientos clave:
1. **Productos exactos** con códigos, cantidades y proporciones
2. **Alternativas de mezcla** cuando el tono no existe en catálogo
3. **Cantidades por largo** de cabello
4. **Explicación del "por qué"** de cada decisión
5. **Troubleshooting** anticipado
6. **Comparador de marcas** (regenerar con otra marca)
7. **Historial de fórmulas** por cliente
8. **Fotos del cliente visibles** durante consulta
9. **Notas personales** del estilista
10. **Ajustes rápidos** sin volver atrás
11. **Chat continuo** con soporte de imágenes

---

## 📦 Archivos creados

### 1. Migration de base de datos
**Archivo**: `supabase/migrations/20251027_create_formulas_tables.sql`

Crea dos tablas:
- `formulas`: Historial completo de fórmulas por cliente
- `formula_notes`: Notas personales del estilista

**Estado**: ✅ Creado
**Pendiente**: Aplicar en Supabase Dashboard (usuario debe hacerlo manualmente)

### 2. TypeScript Types
**Archivo**: `types/index.ts` (modificado)

Añadidos:
- `Formula`: Tipo completo que mapea a tabla DB
- `FormulaNotes`: Notas del estilista
- `ProductUsed`: Productos con código y cantidad
- `ServiceType`, `NoteType`: Enums

**Estado**: ✅ Completado

### 3. Persistence Layer
**Archivo**: `lib/supabase-formulas.ts`

Funciones CRUD:
- `saveFormula()`: Guardar fórmula en DB
- `getClientFormulas()`: Obtener historial por cliente
- `getFormula()`: Obtener fórmula específica
- `updateFormula()`: Actualizar fórmula
- `deleteFormula()`: Eliminar fórmula
- `addFormulaNote()`: Añadir nota personal
- `getFormulaNotes()`: Obtener notas de fórmula
- `extractProductsFromText()`: Parser de productos
- `getLatestSessionNumber()`: Obtener número de sesión

**Estado**: ✅ Completado

### 4. AI Prompts
**Archivo**: `lib/formula-prompts.ts`

Prompts mejorados:
- `getFormulaSystemPrompt()`: Prompt de sistema conversacional tipo mentor
- `getFormulaUserPrompt()`: Prompt de usuario con contexto completo
- `getChatSystemPrompt()`: Prompt para preguntas de seguimiento
- `getQuickQuestions()`: Preguntas rápidas contextuales

**Estado**: ✅ Completado

---

## 🔧 Cambios pendientes en step5.tsx

### Fase 1: Integrar nuevos prompts (ACTUAL)
- [ ] Importar `getFormulaSystemPrompt`, `getFormulaUserPrompt` desde `lib/formula-prompts.ts`
- [ ] Reemplazar `systemPrompt` actual con `getFormulaSystemPrompt()`
- [ ] Reemplazar `userPrompt` actual con `getFormulaUserPrompt()`
- [ ] Usar `getChatSystemPrompt()` para chat de seguimiento

### Fase 2: Refactorizar UI
- [ ] **Hero Card** (siempre visible):
  ```tsx
  <View style={styles.heroCard}>
    <Text>🎯 OBJETIVO: {currentLevel} → {targetLevel}</Text>
    <Text>{totalSessions} sesiones • ~{estimatedTime}h</Text>
    <TouchableOpacity onPress={showPhotos}>
      📸 Ver {photoCount} fotos
    </TouchableOpacity>
  </View>
  ```

- [ ] **Formula Chat Message** (reemplazar todas las cards actuales):
  ```tsx
  <View style={styles.formulaChatMessage}>
    <FormattedMessageContent content={formula} />
    {notes.map(note => (
      <View key={note.id} style={styles.personalNote}>
        📝 TU NOTA: {note.text}
      </View>
    ))}
  </View>
  ```

- [ ] **Action Buttons**:
  ```tsx
  <View style={styles.actionButtons}>
    <Button icon="settings" onPress={handleQuickAdjust}>Ajustar</Button>
    <Button icon="repeat" onPress={handleChangeBrand}>Cambiar marca</Button>
    <Button icon="copy" onPress={handleCopyList}>Copiar lista</Button>
    <Button icon="share" onPress={handleShare}>Compartir</Button>
    <Button icon="dollar" onPress={handleShowCosts}>Costos</Button>
    <Button icon="save" onPress={handleSave}>Guardar</Button>
  </View>
  ```

- [ ] **Quick Questions**:
  ```tsx
  <View style={styles.quickQuestions}>
    {quickQuestions.map(q => (
      <TouchableOpacity key={q.id} onPress={() => askQuestion(q.prompt)}>
        <Text>{q.label}</Text>
      </TouchableOpacity>
    ))}
  </View>
  ```

- [ ] **Chat Input** (mantener actual pero mejorar):
  - Añadir botón de cámara para subir fotos
  - Integrar con chat existente

### Fase 3: Funcionalidades nuevas

#### 3.1 Cambiar marca y regenerar
```tsx
const [showBrandPicker, setShowBrandPicker] = useState(false);
const alternativeBrands = ['L\'Oréal INOA', 'Schwarzkopf Igora', 'Redken Shades', 'Matrix SoColor'];

const handleChangeBrand = () => setShowBrandPicker(true);

const handleRegenerateWithBrand = async (newBrand: string) => {
  setIsGenerating(true);
  // Regenerar fórmula con nueva marca pero mismo objetivo
  const newContext = { ...formulaContext, brand: newBrand, productLine: undefined };
  const newSystemPrompt = getFormulaSystemPrompt(newContext);
  const newUserPrompt = getFormulaUserPrompt(newContext);
  // Llamar generateTextSafe con nuevos prompts
  // ...
  setShowBrandPicker(false);
};
```

#### 3.2 Guardar fórmula
```tsx
import { saveFormula, extractProductsFromText } from '@/lib/supabase-formulas';

const handleSave = async () => {
  const { data: userData } = await supabase.auth.getUser();

  await saveFormula({
    clientId: selectedClient.id,
    userId: userData.user!.id,
    sessionNumber: 1, // o getLatestSessionNumber + 1
    serviceType: 'color',
    currentLevel: currentAnalysis.roots.level,
    targetLevel: desiredAnalysis.level,
    targetTone: desiredAnalysis.tone,
    targetReflection: desiredAnalysis.reflection,
    brand: formulaData.brand,
    productLine: formulaData.productLine,
    technique: formulaData.technique,
    formulaText: formula,
    productsUsed: extractProductsFromText(formula),
    currentColorAnalysis: currentAnalysis,
    desiredColorAnalysis: desiredAnalysis,
    safetyChecklist: formulaData.safetyChecklist,
  });

  Alert.alert('Éxito', 'Fórmula guardada en historial del cliente');
};
```

#### 3.3 Preview de fotos
```tsx
const [showPhotos, setShowPhotos] = useState(false);

const allPhotos = [
  ...(formulaData.currentColorImages || []),
  ...(formulaData.desiredColorImages || []),
];

// En el render:
{showPhotos && (
  <Modal visible={showPhotos} onRequestClose={() => setShowPhotos(false)}>
    <ScrollView horizontal>
      {allPhotos.map((uri, idx) => (
        <Image key={idx} source={{ uri }} style={styles.photoPreview} />
      ))}
    </ScrollView>
  </Modal>
)}
```

#### 3.4 Notas personales
```tsx
import { addFormulaNote, getFormulaNotes } from '@/lib/supabase-formulas';

const [notes, setNotes] = useState<FormulaNotes[]>([]);
const [showAddNote, setShowAddNote] = useState(false);
const [noteText, setNoteText] = useState('');

const handleAddNote = async () => {
  if (!savedFormulaId) {
    Alert.alert('Guarda la fórmula primero');
    return;
  }

  const { data: userData } = await supabase.auth.getUser();

  await addFormulaNote({
    formulaId: savedFormulaId,
    userId: userData.user!.id,
    noteText,
    noteType: 'observation',
    sectionReference: 'general',
  });

  // Recargar notas
  const updatedNotes = await getFormulaNotes(savedFormulaId);
  setNotes(updatedNotes);
  setShowAddNote(false);
  setNoteText('');
};
```

#### 3.5 Ajustes rápidos
```tsx
const [showAdjustments, setShowAdjustments] = useState(false);

const handleQuickAdjust = () => setShowAdjustments(true);

// Modal con sliders/pickers:
<Modal visible={showAdjustments}>
  <View>
    <Text>Ajustar nivel objetivo:</Text>
    <Slider
      value={desiredAnalysis.level}
      onValueChange={(val) => {
        // Actualizar y regenerar
      }}
      minimumValue={1}
      maximumValue={10}
    />

    <Button onPress={handleRegenerateWithAdjustments}>
      Regenerar fórmula
    </Button>
  </View>
</Modal>
```

#### 3.6 Compartir
```tsx
import * as Sharing from 'expo-sharing';

const handleShare = async () => {
  // Opción 1: Compartir texto
  await Sharing.shareAsync({
    message: formula,
    title: 'Fórmula de coloración',
  });

  // Opción 2: Generar PDF (futuro)
  // Opción 3: Copiar al portapapeles
};
```

#### 3.7 Calcular costos
```tsx
import { estimateCost } from '@/lib/supabase-formulas';

const [showCosts, setShowCosts] = useState(false);

const handleShowCosts = () => {
  const products = extractProductsFromText(formula);
  const cost = estimateCost(products);

  Alert.alert(
    'Costos estimados',
    `Productos: $${cost}\n\nPrecio sugerido:\n• Básico (x2.5): $${cost * 2.5}\n• Estándar (x3.5): $${cost * 3.5}\n• Premium (x4.5): $${cost * 4.5}`
  );
};
```

#### 3.8 Preguntas rápidas
```tsx
import { getQuickQuestions } from '@/lib/formula-prompts';

const quickQuestions = getQuickQuestions(formulaContext);

const handleQuickQuestion = (prompt: string) => {
  setInputText(prompt);
  handleSendMessage(); // Enviar automáticamente
};
```

---

## 🎨 Cambios de estilo

### Remover:
- Todas las cards colapsables actuales
- Parsing complejo de secciones preliminares
- sessionHighlights, executiveSummary, etc.

### Añadir:
```tsx
const styles = StyleSheet.create({
  // Hero card compacta
  heroCard: {
    backgroundColor: Colors.background.card,
    padding: 16,
    borderRadius: 12,
    marginBottom: 16,
  },

  // Formula como mensaje de chat grande
  formulaChatMessage: {
    backgroundColor: Colors.background.secondary,
    padding: 20,
    borderRadius: 16,
    marginBottom: 16,
  },

  // Notas personales
  personalNote: {
    backgroundColor: Colors.accent.yellow + '20',
    padding: 12,
    borderRadius: 8,
    marginTop: 12,
    borderLeftWidth: 4,
    borderLeftColor: Colors.accent.yellow,
  },

  // Action buttons
  actionButtons: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },

  actionButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.primary,
  },

  // Quick questions
  quickQuestions: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginBottom: 16,
  },

  quickQuestion: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    backgroundColor: Colors.background.tertiary,
    borderWidth: 1,
    borderColor: Colors.border.primary,
  },
});
```

---

## 📋 Checklist de implementación

### Base de datos
- [x] Crear migration formulas y formula_notes
- [ ] Aplicar migration en Supabase Dashboard (MANUAL)
- [ ] Verificar RLS policies
- [ ] Generar tipos TypeScript desde DB

### Código
- [x] Tipos TypeScript
- [x] Layer de persistencia (supabase-formulas.ts)
- [x] Prompts mejorados (formula-prompts.ts)
- [ ] Integrar prompts en step5.tsx
- [ ] Refactorizar UI de step5.tsx
- [ ] Implementar cambio de marca
- [ ] Implementar guardar fórmula
- [ ] Implementar preview de fotos
- [ ] Implementar notas personales
- [ ] Implementar ajustes rápidos
- [ ] Implementar compartir
- [ ] Implementar cálculo de costos
- [ ] Implementar preguntas rápidas
- [ ] Integrar historial en perfil de cliente

### Testing
- [ ] Probar generación de fórmula con nuevos prompts
- [ ] Probar cambio de marca
- [ ] Probar guardado y recuperación
- [ ] Probar notas personales
- [ ] Probar chat con imágenes
- [ ] Probar en iOS
- [ ] Probar en Android
- [ ] Probar en Web

---

## 🐛 Problemas conocidos

1. **Migration no aplicada**: El usuario debe aplicarla manualmente en Supabase Dashboard
2. **Archivo step5.tsx muy grande**: 33568 tokens, difícil de modificar completo
3. **Parser de productos**: El regex en `extractProductsFromText()` es básico, puede necesitar mejoras

---

## 📝 Notas técnicas

### Por qué crear formula-prompts.ts separado
- Facilita testing de prompts
- Permite versionar prompts
- Más fácil de mantener
- Reutilizable en otros contextos (ej: API)

### Por qué no usar MCP directamente
- MCP no está disponible en el entorno de ejecución actual
- Solución: Proveer guía para aplicación manual
- Alternativa futura: Script de Node.js que use Supabase SDK

### Decisiones de arquitectura
- **Separación de concerns**: Prompts, persistencia, UI separados
- **Progressive enhancement**: Funcionalidades se añaden incrementalmente
- **Backwards compatible**: Código antiguo sigue funcionando mientras refactorizamos

---

## 🚀 Próximos pasos

1. **AHORA**: Aplicar migration en Supabase Dashboard
2. **SIGUIENTE**: Modificar step5.tsx para usar nuevos prompts
3. **LUEGO**: Refactorizar UI progresivamente
4. **FINALMENTE**: Integrar historial en perfil de cliente

---

## 📚 Referencias

- Conversación original: Diseño UX conversacional tipo mentor
- CLAUDE.md: Workflow de MCPs y Supabase
- step5.tsx original: Líneas 754-904 (prompts actuales)
