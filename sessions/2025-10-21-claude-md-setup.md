# Creación de CLAUDE.md y Sistema de Documentación

**Última actualización**: 2025-10-21 18:38

## Contexto

Se necesitaba crear un archivo CLAUDE.md para proporcionar contexto y guías a futuras instancias de Claude Code que trabajen en este proyecto. El objetivo era documentar:
- Arquitectura de alto nivel del proyecto
- Comandos de desarrollo necesarios
- Patrones y convenciones del código
- Flujo de trabajo recomendado
- Sistema de documentación de sesiones

## Cambios Realizados

### Archivos Creados

- `CLAUDE.md` - Documentación completa para Claude Code con:
  - Descripción del proyecto (Salonier AI - asistente para coloristas)
  - Tech stack (React Native + Expo + Supabase + Rork SDK)
  - Comandos de desarrollo con Bun
  - Arquitectura detallada (routing, state management, data layer)
  - Workflow de formula creation (6 pasos)
  - Integración con AI (Rork toolkit SDK)
  - Sistema de tipos TypeScript
  - Patrones importantes del proyecto

- `sessions/` - Directorio para documentación de sesiones
- `sessions/2025-10-21-claude-md-setup.md` - Esta sesión

## Estructura del CLAUDE.md

### Secciones principales:

1. **Project Overview** - Qué hace la app y stack tecnológico
2. **Development Commands** - Comandos con Bun para start, lint, testing
3. **Architecture** - Explicación profunda de:
   - File-based routing (Expo Router)
   - State management (3 contexts: Client, Chat, Formula)
   - Data layer (Supabase + AsyncStorage)
   - Formula creation workflow (6 steps)
   - AI integration
   - Type system

4. **Important Patterns** - Patrones clave:
   - Context provider pattern con `@nkzw/create-context-hook`
   - Navigation con expo-router
   - Styling con StyleSheet API
   - Image handling con expo-image-picker

5. **Common Workflows** - Guías prácticas para:
   - Agregar screens
   - Extender formula workflow
   - Trabajar con Supabase
   - Integrar AI chat

6. **Deployment** - Comandos EAS para iOS/Android/Web

7. **Reglas de Desarrollo** - Convenciones y mejores prácticas:
   - Archivos protegidos (node_modules, .git)
   - Pre-commit checks (lint, start-web)
   - Preferencias de código (editar > crear, reutilizar componentes)
   - Guía de uso de TodoWrite (solo para tareas complejas 3+ pasos)

8. **Documentación de Sesiones** - Sistema completo:
   - Propósito y ubicación (sessions/)
   - Formato de nombres (YYYY-MM-DD-descripcion.md)
   - Template para sesiones nuevas
   - Template para updates
   - Criterios: cuándo crear vs actualizar

9. **Flujo Git/GitHub** - Workflow seguro paso a paso:
   - Contexto de integración con Rork
   - 8 pasos desde pull hasta merge
   - Comandos peligrosos (qué NUNCA hacer)
   - Comandos de emergencia (stash, reset soft, etc.)
   - Resolución de conflictos

10. **Al Finalizar Sesión** - Checklist de cierre:
    - Verificación de calidad (lint, start)
    - Documentación
    - Git workflow
    - Template de mensaje de despedida

## Decisiones Técnicas

### ¿Por qué este nivel de detalle en CLAUDE.md?

**Razón**: El proyecto tiene arquitectura compleja con:
- 3 contexts interdependientes
- Workflow multi-paso (formula creation)
- Integración Supabase con transformación de datos (snake_case ↔ camelCase)
- AI multi-modal (texto + imágenes)
- Persistencia híbrida (Supabase + AsyncStorage)

Sin contexto, Claude Code perdería mucho tiempo explorando el codebase para entender estos patrones.

**Trade-offs**:
- ✅ Gana: Onboarding rápido, menos exploración innecesaria
- ⚠️ Pierde: Archivo largo (~650 líneas), requiere mantenimiento

### ¿Por qué sistema de documentación de sesiones?

**Razón**:
- Proyecto activo con cambios frecuentes
- Integración con Rork (múltiples fuentes de commits)
- Necesidad de contexto histórico para debugging
- Capturar decisiones técnicas y razones

**Trade-offs**:
- ✅ Gana: Contexto persistente, menos repetición de errores
- ⚠️ Pierde: Overhead de documentación (mitigado con templates)

### ¿Por qué guía Git tan detallada?

**Razón**:
- Usuario puede ser principiante
- Integración con Rork requiere cuidado especial
- `git push --force` podría romper sincronización

**Trade-offs**:
- ✅ Gana: Seguridad, prevención de errores destructivos
- ⚠️ Pierde: Más largo, pero necesario para safety

## Características Especiales del Proyecto

### Integración con Rork
- Rork hace commits automáticos desde su plataforma
- Cambios locales se sincronizan bidireccionalmente
- Requiere cuidado con rebase/force push

### Stack Moderno
- Bun como package manager (no npm)
- Expo SDK 53 con new architecture enabled
- React Native 0.79.1
- TypeScript estricto

### Patrones Únicos
- Formula workflow de 6 pasos con estado compartido
- AI multi-modal con base64 encoding
- Dual persistence (Supabase para clientes, AsyncStorage para chat)
- Context pattern con custom hook library

## Notas para Futuras Sesiones

### Al trabajar en este proyecto:

1. **SIEMPRE leer últimas 3-5 sesiones** en `sessions/` antes de empezar
2. **VERIFICAR fecha actual** antes de crear archivos de sesión
3. **Seguir templates** de sesión definidos en CLAUDE.md
4. **Ejecutar `bun run lint`** antes de commits
5. **Documentar decisiones técnicas**, no solo cambios

### Gotchas importantes:

- El proyecto usa **Bun, no npm** (verificar comandos)
- **NO hay tests** configurados (no intentar `npm test`)
- Colors está en `constants/colors.ts` (lowercase)
- Supabase usa snake_case, TypeScript usa camelCase (usar helper parseClientDates)
- ChatContext usa AsyncStorage, ClientContext usa Supabase (diferentes estrategias)

### Próximos pasos potenciales:

- [ ] Considerar agregar tests (Jest + React Native Testing Library)
- [ ] Documentar sistema de colors/theming si se expande
- [ ] Agregar session para configuración de Supabase cuando se hagan cambios
- [ ] Documentar proceso de deployment a stores cuando se implemente

## TODOs / Trabajo Pendiente

- [x] Crear CLAUDE.md
- [x] Crear directorio sessions/
- [x] Documentar esta sesión
- [ ] Considerar agregar .gitignore entry para archivos temporales si aparecen
- [ ] Evaluar si se necesita template de issue/PR en .github/

## Metadata de la Sesión

**Archivos creados**: 2
- `CLAUDE.md` (650 líneas)
- `sessions/2025-10-21-claude-md-setup.md` (este archivo)

**Archivos modificados**: 0

**Comandos ejecutados**:
```bash
mkdir -p sessions/
date "+%Y-%m-%d %H:%M"
```

**Tiempo estimado**: ~30 minutos de análisis de codebase + escritura

**Siguiente sesión recomendada**:
Documentar cualquier trabajo de desarrollo significativo siguiendo el template establecido.
