## Prompt Sesión 2025-11-01 · Rate limit IA

### Contexto actual
- Eliminé la espera de 2s en el cliente: `lib/ai-client.ts` ahora usa `EXPO_PUBLIC_AI_MIN_REQUEST_INTERVAL_MS` y por defecto queda en 0 ms cuando `__DEV__` está activo.
- La función edge `supabase/functions/ai-proxy/index.ts` detecta `AI_PROXY_RATE_LIMIT_MODE=disable` (valor que toma automáticamente si el proyecto apunta a localhost) y omite la RPC `check_rate_limit` mostrando un warning en logs.

### Solicitud para la próxima sesión
1. Volver a fijar `MIN_REQUEST_INTERVAL` en 2000 ms dentro de `lib/ai-client.ts` para recuperar el throttle en el cliente.
2. Cambiar `AI_PROXY_RATE_LIMIT_MODE` a `enforce` (o borrar la variable) y redisplegar `ai-proxy` para que `check_rate_limit` vuelva a ejecutarse.
3. Verificar que las respuestas de la IA vuelven a mostrar el error de límite cuando corresponde.
