# Fix: Error en búsqueda de productos (Edge Function)

**Última actualización**: 2025-10-22 10:00

## Contexto

El usuario reportó un error `FunctionsHttpError: Edge Function returned a non-2xx status code` al generar fórmulas en Step 5. El error ocurría específicamente durante la búsqueda de productos con Perplexity.

### Error Original
```
ERROR  [AIClient] Product search error: [FunctionsHttpError: Edge Function returned a non-2xx status code]
WARN   [Step5] Product search failed, using base formula: [AIServiceError: Error al buscar información de productos]
```

## Problema Identificado

### 1. Bug Principal: `.single()` en query de caché
**Archivo**: `supabase/functions/ai-proxy/index.ts:193`

```typescript
// ❌ ANTES - Lanzaba error cuando no había caché
const { data: cached } = await supabase
  .from('product_cache')
  .select('*')
  .eq('brand', brand || '')
  .eq('product_line', productLine || '')
  .eq('query_text', prompt)
  .gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
  .single(); // ← Error: "Expected one row, got zero"
```

**Causa raíz**: El método `.single()` de Supabase lanza un **error** cuando no encuentra exactamente 1 resultado. En un cache miss (primera búsqueda), retorna 0 resultados y la función falla con status 500.

**Solución**: Usar `.maybeSingle()` que retorna `null` sin error cuando no hay resultados.

```typescript
// ✅ DESPUÉS - Retorna null sin error
const { data: cached } = await supabase
  .from('product_cache')
  .select('*')
  .eq('brand', brand || '')
  .eq('product_line', productLine || '')
  .eq('query_text', prompt)
  .gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
  .maybeSingle(); // ← Retorna null si no hay resultados

if (cached) {
  // Cache HIT
} else {
  // Cache MISS - continuar con Perplexity
}
```

### 2. Bug Secundario: Extracción incorrecta del caché
**Archivo**: `supabase/functions/ai-proxy/index.ts:205-207`

```typescript
// ❌ ANTES - No extraía el .content del objeto
response = typeof cached.response_data === 'string'
  ? cached.response_data
  : JSON.stringify(cached.response_data);

// ✅ DESPUÉS - Extrae correctamente .content
response = typeof cached.response_data === 'string'
  ? cached.response_data
  : (cached.response_data.content || JSON.stringify(cached.response_data));
```

**Razón**: Al guardar en caché (línea 262), se usa `response_data: { content: response }`, pero al leer no se extraía el campo `content`.

### 3. Limpieza: Headers duplicadas
**Archivo**: `lib/ai-client.ts:307-309`

```typescript
// ❌ ANTES - Headers innecesarias
const response = await supabase.functions.invoke('ai-proxy', {
  body: { ... },
  headers: {
    Authorization: `Bearer ${session.session.access_token}`,
  },
});

// ✅ DESPUÉS - Supabase SDK las incluye automáticamente
const response = await supabase.functions.invoke('ai-proxy', {
  body: { ... },
});
```

## Cambios Realizados

### Archivos Modificados

**1. `lib/ai-client.ts`**
- Eliminadas headers duplicadas en función `searchProducts()` (líneas 307-309)

**2. `supabase/functions/ai-proxy/index.ts`**
- Cambiado `.single()` por `.maybeSingle()` (línea 193)
- Corregida extracción de `response_data.content` del caché (línea 207)
- Desplegada versión 15 de la Edge Function

## Pruebas Realizadas

### Test 1: Product Search - Cache MISS (primera búsqueda)
```
✅ Status: 200 OK
⏱️  Latency: 8,837ms
💰 Cost: $0.0090 USD
🔄 Cached: false
📦 Provider: Perplexity Sonar Pro
📊 Tokens: 102 prompt + 249 completion = 351 total
```

### Test 2: Product Search - Cache HIT (segunda búsqueda igual)
```
✅ Status: 200 OK
⏱️  Latency: 110ms (80x más rápido!)
💰 Cost: $0.0000 USD (gratis!)
🔄 Cached: true ✅
📊 Tokens: 0 (sin llamada a API)
```

### Test 3: Formula Generation
```
✅ Status: 200 OK
⏱️  Latency: 9,942ms
💰 Cost: $0.0115 USD
📦 Provider: OpenAI GPT-4o
```

### Verificación en Base de Datos

**Tabla `product_cache`**:
- ✅ 4 entradas guardadas correctamente
- ✅ `access_count` incrementándose en cache hits
- ✅ `response_data.content` guardado y recuperado OK

**Tabla `ai_usage_log`**:
- ✅ Primera búsqueda: $0.0090, 8837ms (Perplexity)
- ✅ Segunda búsqueda: $0.0000, 110ms (Cache)
- ✅ Rate limiting funcionando

**Logs de Supabase**:
- ✅ Versión 15: 3 llamadas exitosas con status 200
- ✅ Sin errores 500 (que ocurrían con versión 14)

## Decisiones Técnicas

### ¿Por qué `.maybeSingle()` en lugar de `.single()`?

**Comparación**:
- `.single()`: Retorna **1 row exacta** o **lanza error** (PostgreSQL LIMIT 1 + validación estricta)
- `.maybeSingle()`: Retorna **1 row o null** sin error (PostgreSQL LIMIT 1, tolerante)

**Razón**: En un sistema de caché, es **esperado** que no haya resultados en la primera búsqueda (cache miss). Usar `.single()` trata esto como un error, cuando es un caso de uso normal.

**Trade-offs**:
- ✅ **Ganamos**: Sin errores 500 en cache miss
- ✅ **Ganamos**: Flujo de código más limpio (if cached vs try/catch)
- ⚠️ **Perdemos**: Nada - si hay múltiples resultados, `.maybeSingle()` retorna el primero (igual que `.single()`)

### ¿Por qué caché de 7 días?

```typescript
.gte('updated_at', new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString())
```

**Razón**:
- Productos de coloración profesional **no cambian frecuentemente**
- Catálogos actualizados 1-2 veces al año típicamente
- 7 días balancea **frescura** vs **ahorro de costos**

**Beneficios**:
- 💰 Ahorro: $0.005 por búsqueda repetida (Perplexity base cost)
- ⚡ Velocidad: 80x más rápido (110ms vs 8837ms)

## Resultados

### ✅ Problema Resuelto
- Ya no ocurre `FunctionsHttpError` en búsqueda de productos
- Step 5 (generación de fórmulas) funciona correctamente
- Cache funcionando: búsquedas repetidas son gratis y 80x más rápidas

### 📊 Métricas de Impacto
- **Tasa de error**: 100% → 0% (eliminado error 500)
- **Velocidad (cache hit)**: +8000% (8837ms → 110ms)
- **Costo (cache hit)**: -100% ($0.0090 → $0.0000)

### 🚀 Edge Function Desplegada
- **Versión**: 15 (desplegada exitosamente)
- **Status**: ACTIVE y funcionando en producción
- **URL**: `https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/ai-proxy`

## TODOs / Trabajo Pendiente

- [ ] Opcional: Agregar TTL configurable al caché (actualmente fijo 7 días)
- [ ] Opcional: Implementar cache warming para marcas populares
- [ ] Opcional: Métricas de cache hit rate en dashboard

## Notas para Futuras Sesiones

### Patrón Aprendido: `.single()` vs `.maybeSingle()`
Siempre usar `.maybeSingle()` cuando:
- Es esperado que no haya resultados (como en caché)
- No queremos lanzar error en caso de 0 resultados
- Preferimos flujo `if (data)` en lugar de `try/catch`

Usar `.single()` solo cuando:
- Sabemos con certeza que debe haber 1 resultado
- Queremos error explícito si no hay resultado (validación estricta)

### Debugging de Edge Functions
Para debugear errores en Edge Functions:
1. Revisar logs: `mcp__supabase__get_logs` con service `edge-function`
2. Verificar versión desplegada: `mcp__supabase__list_edge_functions`
3. Probar con script Node.js que llama directamente a la función
4. Verificar tablas relacionadas (cache, logs) en PostgreSQL

### Gotchas de Supabase Functions
- `supabase.functions.invoke()` **automáticamente** incluye el token de auth de la sesión actual
- No es necesario pasar `Authorization` header manualmente
- El token se verifica en la Edge Function con `supabase.auth.getUser(token)`

## Referencias
- Supabase Docs: [`.single()` vs `.maybeSingle()`](https://supabase.com/docs/reference/javascript/select)
- Edge Function deployada: Versión 15
- Commit: `a23d721` - "fix: Resolver error en búsqueda de productos"
