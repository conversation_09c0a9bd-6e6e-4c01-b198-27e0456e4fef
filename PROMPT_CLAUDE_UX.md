# Prompt: Mejorar UX/UI del Chat para igualar Claude.ai

## Objetivo
Transformar la experiencia de usuario de los chats de Salonier AI (main chat y formula chat) para igualar la experiencia de Claude.ai, manteniendo el estilo visual Revolut del proyecto.

## Chats a Mejorar
1. **Main Chat**: `app/(app)/(tabs)/chat.tsx`
2. **Formula Chat**: `app/(app)/formula/step5.tsx`

---

## Componentes Nuevos a Crear

### 1. EmptyState Component (`components/chat/EmptyState.tsx`)
**Propósito**: Pantalla inicial cuando el chat está vacío

**Features:**
- Logo de Salonier prominente
- Saludo contextual según hora del día:
  - Mañana (5am-12pm): "☀️ ¡Buenos días, [Día de la semana]!"
  - Tarde (12pm-7pm): "✨ ¡Buenas tardes, [Día de la semana]!"
  - <PERSON><PERSON> (7pm-5am): "🌙 ¡Buenas noches, [Día de la semana]!"
- Mensaje secundario: "Soy tu experto en coloración capilar. ¿En qué puedo ayudarte hoy?"

**Código ejemplo:**
```typescript
const getGreeting = () => {
  const hour = new Date().getHours();
  const day = new Date().toLocaleDateString('es-ES', { weekday: 'long' });
  if (hour >= 5 && hour < 12) return { emoji: '☀️', text: '¡Buenos días', day };
  else if (hour >= 12 && hour < 19) return { emoji: '✨', text: '¡Buenas tardes', day };
  else return { emoji: '🌙', text: '¡Buenas noches', day };
};
```

---

### 2. ThinkingIndicator Component (`components/chat/ThinkingIndicator.tsx`)
**Propósito**: Indicador animado mientras la IA piensa

**Features:**
- Logo Palette de lucide-react-native animado
- Animación de rotación continua (360°, 2s, loop)
- Animación de pulso simultánea (escala 1.0 → 1.1 → 1.0, 1s por ciclo)
- Usar React Native Animated API con `useNativeDriver: true`

**Código ejemplo:**
```typescript
const rotation = Animated.loop(
  Animated.timing(rotateAnim, {
    toValue: 1,
    duration: 2000,
    easing: Easing.linear,
    useNativeDriver: true,
  })
);

const pulse = Animated.loop(
  Animated.sequence([
    Animated.timing(pulseAnim, {
      toValue: 1.1,
      duration: 1000,
      easing: Easing.easeInOut,
      useNativeDriver: true,
    }),
    Animated.timing(pulseAnim, {
      toValue: 1,
      duration: 1000,
      easing: Easing.easeInOut,
      useNativeDriver: true,
    }),
  ])
);
```

---

### 3. MessageFeedback Component (`components/chat/MessageFeedback.tsx`)
**Propósito**: Botones de interacción bajo cada mensaje de IA

**Features:**
- **Copiar**: Usa `expo-clipboard` para copiar mensaje
- **Me gusta**: Icono ThumbsUp (feedback positivo)
- **No me gusta**: Icono ThumbsDown (feedback negativo)
- **Reintentar**: Icono RotateCcw (regenerar respuesta)
- Iconos de lucide-react-native (size 16, color textTertiary)
- Botones pequeños, horizontales, con gap de 8px

**Código ejemplo:**
```typescript
import * as Clipboard from 'expo-clipboard';

const handleCopy = async () => {
  await Clipboard.setStringAsync(content);
  Alert.alert('Copiado', 'Mensaje copiado al portapapeles');
};
```

---

## Mejoras en Chat Principal (`chat.tsx`)

### A. Pantalla Vacía
- Reemplazar espacio en blanco con `<EmptyState />`
- Mostrar cuando `displayMessages.length === 0`

### B. Indicador de Pensando
- Reemplazar `<ActivityIndicator />` con `<ThinkingIndicator />`
- Usar en función `renderTypingIndicator()`

### C. Feedback en Mensajes
- Agregar `<MessageFeedback />` bajo cada mensaje de rol "assistant"
- Pasar `content` del mensaje como prop

### D. Botones de Foto (PROBLEMA CRÍTICO - Ver Sección "Problemas Encontrados")
**NOTA**: NO implementar modal con botón +. Mantener dos botones separados.

Configuración actual que FUNCIONA:
```typescript
<TouchableOpacity style={styles.imageButton} onPress={takePhoto}>
  <Camera color={Colors.light.primary} size={22} strokeWidth={2} />
</TouchableOpacity>
<TouchableOpacity style={styles.imageButton} onPress={pickImage}>
  <ImageIcon color={Colors.light.primary} size={22} strokeWidth={2} />
</TouchableOpacity>
```

**Estilos:**
```typescript
imageButton: {
  width: 44,
  height: 44,
  borderRadius: 22,
  backgroundColor: Colors.light.backgroundSecondary,
  alignItems: 'center',
  justifyContent: 'center',
}
```

### E. Input Mejorado
**Tamaños aumentados:**
```typescript
input: {
  fontSize: 17,        // aumentado de 15
  paddingVertical: 18, // aumentado de 14
  minHeight: 52,       // aumentado de 44
  borderWidth: 1.5,    // nuevo
  borderColor: Colors.light.border, // nuevo
}
```

### F. Tipografía en Mensajes
**Actualizar `FormattedMessageContent.tsx`:**
```typescript
fontSize: 17,  // aumentado de 15
lineHeight: 26, // aumentado de 24
```

### G. KeyboardAvoidingView Optimizado
**IMPORTANTE**: Eliminar espacios entre input y teclado
```typescript
<KeyboardAvoidingView
  behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
  keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
  style={{ flex: 1 }}
>
```

**Reducir padding en `inputContainer`:**
```typescript
inputContainer: {
  paddingBottom: 10, // reducido de 20+
  paddingHorizontal: 16,
}
```

---

## Mejoras en Formula Chat (`step5.tsx`)

### Aplicar las mismas mejoras:
1. ThinkingIndicator en estados de carga
2. MessageFeedback en mensajes del chat
3. Input con mismo estilo (fontSize 17, paddingVertical 18, minHeight 52)
4. Botones de foto optimizados (44x44)
5. KeyboardAvoidingView optimizado

---

## Problemas Encontrados y Soluciones

### ❌ PROBLEMA CRÍTICO: Modal de Fotos NO Funciona en iOS

#### Intentos Realizados (TODOS FALLARON):

**Intento 1: Botón + con Modal usando TouchableOpacity**
```typescript
// FALLA: Overlay captura todos los eventos, botones no responden
<TouchableOpacity style={overlay} onPress={close}>
  <View style={content}>
    <TouchableOpacity onPress={takePhoto} /> // NO FUNCIONA
  </View>
</TouchableOpacity>
```

**Intento 2: stopPropagation()**
```typescript
// FALLA: stopPropagation() no existe en React Native
<TouchableOpacity onPress={(e) => e.stopPropagation()}>
```

**Intento 3: onStartShouldSetResponder**
```typescript
// FALLA: No previene que overlay capture eventos
<View onStartShouldSetResponder={() => true}>
  <TouchableOpacity onPress={takePhoto} />
</View>
```

**Intento 4: Pressable con stopPropagation**
```typescript
// FALLA: Pressable tampoco soporta stopPropagation en RN
<Pressable onPress={(e) => e.stopPropagation()}>
```

**Intento 5: Backdrop Layer con StyleSheet.absoluteFillObject**
```typescript
// FALLA: absoluteFillObject cubre contenido
<View style={overlay}>
  <TouchableOpacity style={StyleSheet.absoluteFillObject} onPress={close} />
  <View style={content}>
    <TouchableOpacity onPress={takePhoto} /> // Cubierto por backdrop
  </View>
</View>
```

**Intento 6: pointerEvents="box-none"**
```typescript
// FALLA: ImagePicker se cuelga, nunca retorna
<View pointerEvents="box-none">
  <TouchableOpacity style={backdrop} onPress={close} />
  <View pointerEvents="auto">
    <TouchableOpacity onPress={takePhoto} /> // Evento llega, pero picker se cuelga
  </View>
</View>
```

**Logs confirmados:**
```
[PhotoModal] Camera button pressed!
[Chat] takePhoto() called
[Chat] Requesting camera permissions...
[Chat] Camera permission status: granted
[Chat] Launching camera...
// SE CUELGA AQUÍ - ImagePicker nunca retorna
```

**Intento 7: setTimeout con delays (300ms, 500ms)**
```typescript
// FALLA: Aumentar delay no resuelve el cuelgue
setShowPhotoModal(false);
setTimeout(() => takePhoto(), 500); // Picker se cuelga igual
```

**Intento 8: Lanzar picker ANTES de cerrar modal**
```typescript
// FALLA: Picker se cuelga incluso con modal abierto
onPress={async () => {
  await takePhoto(); // Se cuelga aquí
  setShowPhotoModal(false);
}}
```

#### ✅ SOLUCIÓN CONFIRMADA: Dos Botones Separados

**El único enfoque que funciona en iOS:**
```typescript
// Botón Cámara directo
<TouchableOpacity onPress={takePhoto}>
  <Camera size={22} />
</TouchableOpacity>

// Botón Galería directo
<TouchableOpacity onPress={pickImage}>
  <ImageIcon size={22} />
</TouchableOpacity>
```

**Por qué funciona:**
- Sin modal intermedio
- ImagePicker se lanza directamente desde el botón
- No hay conflictos de UI stack en iOS
- Es el patrón nativo más simple

#### Conclusión
**NO intentar implementar modal de fotos estilo Claude en esta app.** El problema es específico de React Native + Expo + iOS, donde ImagePicker tiene conflictos con Modals que no se pueden resolver con técnicas estándar de event handling.

---

## Imports Necesarios

```typescript
// Componentes RN
import {
  StyleSheet,
  View,
  Text,
  TouchableOpacity,
  Animated,
  Easing,
  Platform,
  KeyboardAvoidingView,
  Alert
} from 'react-native';

// Iconos
import {
  Camera,
  ImageIcon,
  Palette,
  Copy,
  ThumbsUp,
  ThumbsDown,
  RotateCcw
} from 'lucide-react-native';

// Expo
import * as Clipboard from 'expo-clipboard';
import * as ImagePicker from 'expo-image-picker';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

// Proyecto
import Colors from '@/constants/colors';
```

---

## Checklist de Implementación

### Fase 1: Componentes Base
- [ ] Crear `EmptyState.tsx` con saludo contextual
- [ ] Crear `ThinkingIndicator.tsx` con animaciones
- [ ] Crear `MessageFeedback.tsx` con botones de interacción

### Fase 2: Chat Principal
- [ ] Integrar EmptyState cuando `displayMessages.length === 0`
- [ ] Reemplazar ActivityIndicator con ThinkingIndicator
- [ ] Agregar MessageFeedback bajo mensajes de IA
- [ ] Actualizar estilos de input (fontSize 17, padding 18, border)
- [ ] Optimizar KeyboardAvoidingView (reducir offsets y padding)
- [ ] **NO TOCAR** botones de foto (dejar como están)

### Fase 3: Formula Chat
- [ ] Aplicar ThinkingIndicator en loading states
- [ ] Aplicar MessageFeedback en mensajes
- [ ] Sincronizar estilos de input con main chat
- [ ] Optimizar KeyboardAvoidingView

### Fase 4: Tipografía Global
- [ ] Actualizar FormattedMessageContent.tsx (fontSize 17, lineHeight 26)

### Fase 5: Testing
- [ ] Probar en dispositivo iOS real (CRÍTICO - no confiar en web/simulador)
- [ ] Verificar EmptyState con diferentes horas del día
- [ ] Verificar animaciones de ThinkingIndicator (smooth, no lag)
- [ ] Verificar botones de feedback (copy, like, dislike, retry)
- [ ] Verificar botones de foto funcionan (Camera + ImageIcon directos)
- [ ] Verificar teclado sin espacios en blanco

---

## Notas Técnicas Importantes

### React Native vs Web
- **ImagePicker NO funciona en web** - solo en iOS/Android
- **Animated API requiere useNativeDriver: true** para performance
- **Modal con event handling es problemático en iOS**
- **Siempre probar en dispositivo real**, no asumir que simulador = dispositivo

### Performance
- Usar `useMemo` para system prompts pesados
- Usar `useCallback` para funciones pasadas a componentes
- Animated API con `useNativeDriver: true`
- Evitar re-renders innecesarios en FlatList

### Accesibilidad
- Touch targets mínimo 44x44 (iOS HIG)
- Labels claros en botones de feedback
- Contraste de color adecuado

---

## Recursos de Referencia

### Documentación
- [React Native Animated](https://reactnative.dev/docs/animated)
- [Expo ImagePicker](https://docs.expo.dev/versions/latest/sdk/imagepicker/)
- [Expo Clipboard](https://docs.expo.dev/versions/latest/sdk/clipboard/)
- [React Native Modal](https://reactnative.dev/docs/modal)
- [Lucide React Native Icons](https://lucide.dev/guide/packages/lucide-react-native)

### Design System
- **Claude.ai**: Referencia UX (greeting, thinking indicator, feedback buttons)
- **Revolut**: Referencia visual (colors, typography, spacing)
- **Salonier**: Mantener identidad de marca (logo Palette, primary color)

---

## Prompt Final para Nueva Implementación

```
Mejora la UX/UI del chat de Salonier AI para igualar la experiencia de Claude.ai,
manteniendo el estilo visual Revolut:

1. Crea 3 componentes nuevos:
   - EmptyState.tsx: Saludo contextual según hora + logo
   - ThinkingIndicator.tsx: Logo Palette animado (rotación + pulso)
   - MessageFeedback.tsx: Botones copy/like/dislike/retry

2. Integra en chat.tsx y step5.tsx:
   - EmptyState cuando no hay mensajes
   - ThinkingIndicator en lugar de ActivityIndicator
   - MessageFeedback bajo cada mensaje de IA

3. Mejora input:
   - fontSize: 17, paddingVertical: 18, minHeight: 52
   - borderWidth: 1.5, borderColor visible

4. Optimiza KeyboardAvoidingView:
   - Reducir keyboardVerticalOffset a 90 (iOS)
   - Reducir paddingBottom de inputContainer a 10

5. Actualiza tipografía en FormattedMessageContent:
   - fontSize: 17, lineHeight: 26

IMPORTANTE:
- NO cambies los botones de foto (Camera + ImageIcon separados)
- NO intentes crear modal de fotos (problema conocido en iOS)
- Probar en dispositivo iOS real, no en web
- Usar useNativeDriver: true en todas las animaciones

Archivos a modificar:
- Crear: components/chat/EmptyState.tsx
- Crear: components/chat/ThinkingIndicator.tsx
- Crear: components/chat/MessageFeedback.tsx
- Editar: app/(app)/(tabs)/chat.tsx
- Editar: app/(app)/formula/step5.tsx
- Editar: components/chat/FormattedMessageContent.tsx
```

---

## Lecciones Aprendidas

1. **React Native ≠ React Web**: Event handling es fundamentalmente diferente
2. **ImagePicker + Modal = Problema en iOS**: Evitar esta combinación
3. **Siempre probar en dispositivo real**: Simuladores y web no muestran problemas reales
4. **Simplicidad > Complejidad**: A veces el diseño más simple es el único que funciona
5. **Logs exhaustivos son esenciales**: Sin logs detallados, debugging es imposible
6. **Async/await con UI es delicado**: Timing entre modal close y picker launch causa cuelgues
7. **pointerEvents existe pero no siempre funciona**: Depende del contexto y componente
8. **Modal event handling es limitado en RN**: No hay equivalente a e.stopPropagation()
