export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.5"
  }
  public: {
    Tables: {
      ai_cache: {
        Row: {
          brand: string | null
          cache_key: string
          cache_type: string
          cache_value: <PERSON>son
          created_at: string
          expires_at: string
          hit_count: number
          last_accessed_at: string | null
          product_line: string | null
          updated_at: string | null
        }
        Insert: {
          brand?: string | null
          cache_key: string
          cache_type: string
          cache_value: <PERSON><PERSON>
          created_at?: string
          expires_at: string
          hit_count?: number
          last_accessed_at?: string | null
          product_line?: string | null
          updated_at?: string | null
        }
        Update: {
          brand?: string | null
          cache_key?: string
          cache_type?: string
          cache_value?: Json
          created_at?: string
          expires_at?: string
          hit_count?: number
          last_accessed_at?: string | null
          product_line?: string | null
          updated_at?: string | null
        }
        Relationships: []
      }
      ai_usage_log: {
        Row: {
          citations: Json | null
          completion_tokens: number | null
          cost_usd: number | null
          created_at: string | null
          error: string | null
          id: string
          image_count: number | null
          latency_ms: number | null
          model: string
          prompt_tokens: number | null
          provider: string
          search_queries: string[] | null
          total_tokens: number | null
          use_case: string
          user_id: string | null
        }
        Insert: {
          citations?: Json | null
          completion_tokens?: number | null
          cost_usd?: number | null
          created_at?: string | null
          error?: string | null
          id?: string
          image_count?: number | null
          latency_ms?: number | null
          model: string
          prompt_tokens?: number | null
          provider: string
          search_queries?: string[] | null
          total_tokens?: number | null
          use_case: string
          user_id?: string | null
        }
        Update: {
          citations?: Json | null
          completion_tokens?: number | null
          cost_usd?: number | null
          created_at?: string | null
          error?: string | null
          id?: string
          image_count?: number | null
          latency_ms?: number | null
          model?: string
          prompt_tokens?: number | null
          provider?: string
          search_queries?: string[] | null
          total_tokens?: number | null
          use_case?: string
          user_id?: string | null
        }
        Relationships: []
      }
      analytics_events: {
        Row: {
          event_name: string
          id: string
          properties: Json | null
          timestamp: string
          user_id: string | null
        }
        Insert: {
          event_name: string
          id?: string
          properties?: Json | null
          timestamp?: string
          user_id?: string | null
        }
        Update: {
          event_name?: string
          id?: string
          properties?: Json | null
          timestamp?: string
          user_id?: string | null
        }
        Relationships: []
      }
      clients: {
        Row: {
          additional_notes: string | null
          chemical_treatments: Json
          communication_preferences: Json
          created_at: string
          created_by: string | null
          email: string | null
          has_sensitive_scalp: boolean
          id: string
          is_pregnant_or_breastfeeding: boolean
          known_allergies: string | null
          last_visit: string | null
          name: string
          notes: string | null
          organization_id: string | null
          phone: string | null
          photo: string | null
          updated_at: string
        }
        Insert: {
          additional_notes?: string | null
          chemical_treatments?: Json
          communication_preferences?: Json
          created_at?: string
          created_by?: string | null
          email?: string | null
          has_sensitive_scalp?: boolean
          id?: string
          is_pregnant_or_breastfeeding?: boolean
          known_allergies?: string | null
          last_visit?: string | null
          name: string
          notes?: string | null
          organization_id?: string | null
          phone?: string | null
          photo?: string | null
          updated_at?: string
        }
        Update: {
          additional_notes?: string | null
          chemical_treatments?: Json
          communication_preferences?: Json
          created_at?: string
          created_by?: string | null
          email?: string | null
          has_sensitive_scalp?: boolean
          id?: string
          is_pregnant_or_breastfeeding?: boolean
          known_allergies?: string | null
          last_visit?: string | null
          name?: string
          notes?: string | null
          organization_id?: string | null
          phone?: string | null
          photo?: string | null
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "clients_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      conversations: {
        Row: {
          created_at: string
          id: string
          is_pinned: boolean
          organization_id: string | null
          title: string
          updated_at: string
          user_id: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          is_pinned?: boolean
          organization_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          is_pinned?: boolean
          organization_id?: string | null
          title?: string
          updated_at?: string
          user_id?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "conversations_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      formula_notes: {
        Row: {
          created_at: string
          formula_id: string
          id: string
          note_text: string
          note_type: string | null
          section_reference: string | null
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          formula_id: string
          id?: string
          note_text: string
          note_type?: string | null
          section_reference?: string | null
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          formula_id?: string
          id?: string
          note_text?: string
          note_type?: string | null
          section_reference?: string | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "formula_notes_formula_id_fkey"
            columns: ["formula_id"]
            isOneToOne: false
            referencedRelation: "formulas"
            referencedColumns: ["id"]
          },
        ]
      }
      formulas: {
        Row: {
          brand: string
          client_id: string
          created_at: string
          current_color_analysis: Json | null
          current_level: number | null
          desired_color_analysis: Json | null
          estimated_duration_minutes: number | null
          formula_text: string
          id: string
          product_line: string | null
          products_used: Json | null
          safety_checklist: Json | null
          service_type: string
          session_number: number
          target_level: number | null
          target_reflection: string | null
          target_tone: string | null
          technique: string | null
          total_cost: number | null
          updated_at: string
          user_id: string
        }
        Insert: {
          brand: string
          client_id: string
          created_at?: string
          current_color_analysis?: Json | null
          current_level?: number | null
          desired_color_analysis?: Json | null
          estimated_duration_minutes?: number | null
          formula_text: string
          id?: string
          product_line?: string | null
          products_used?: Json | null
          safety_checklist?: Json | null
          service_type: string
          session_number?: number
          target_level?: number | null
          target_reflection?: string | null
          target_tone?: string | null
          technique?: string | null
          total_cost?: number | null
          updated_at?: string
          user_id: string
        }
        Update: {
          brand?: string
          client_id?: string
          created_at?: string
          current_color_analysis?: Json | null
          current_level?: number | null
          desired_color_analysis?: Json | null
          estimated_duration_minutes?: number | null
          formula_text?: string
          id?: string
          product_line?: string | null
          products_used?: Json | null
          safety_checklist?: Json | null
          service_type?: string
          session_number?: number
          target_level?: number | null
          target_reflection?: string | null
          target_tone?: string | null
          technique?: string | null
          total_cost?: number | null
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "formulas_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "clients"
            referencedColumns: ["id"]
          },
        ]
      }
      intent_cache: {
        Row: {
          access_count: number
          created_at: string
          intent_result: Json
          query_hash: string
          updated_at: string | null
        }
        Insert: {
          access_count?: number
          created_at?: string
          intent_result: Json
          query_hash: string
          updated_at?: string | null
        }
        Update: {
          access_count?: number
          created_at?: string
          intent_result?: Json
          query_hash?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      messages: {
        Row: {
          content: string
          conversation_id: string
          id: string
          images: Json | null
          role: string
          timestamp: string
        }
        Insert: {
          content: string
          conversation_id: string
          id?: string
          images?: Json | null
          role: string
          timestamp?: string
        }
        Update: {
          content?: string
          conversation_id?: string
          id?: string
          images?: Json | null
          role?: string
          timestamp?: string
        }
        Relationships: [
          {
            foreignKeyName: "fk_conversation"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "messages_conversation_id_fkey"
            columns: ["conversation_id"]
            isOneToOne: false
            referencedRelation: "conversations"
            referencedColumns: ["id"]
          },
        ]
      }
      organization_members: {
        Row: {
          created_at: string
          id: string
          is_active: boolean
          joined_date: string
          organization_id: string
          permissions: Json
          role: string
          updated_at: string
          user_id: string
        }
        Insert: {
          created_at?: string
          id?: string
          is_active?: boolean
          joined_date?: string
          organization_id: string
          permissions?: Json
          role: string
          updated_at?: string
          user_id: string
        }
        Update: {
          created_at?: string
          id?: string
          is_active?: boolean
          joined_date?: string
          organization_id?: string
          permissions?: Json
          role?: string
          updated_at?: string
          user_id?: string
        }
        Relationships: [
          {
            foreignKeyName: "organization_members_organization_id_fkey"
            columns: ["organization_id"]
            isOneToOne: false
            referencedRelation: "organizations"
            referencedColumns: ["id"]
          },
        ]
      }
      organizations: {
        Row: {
          business_hours: Json | null
          city: string | null
          country: string | null
          created_at: string
          email: string | null
          id: string
          instagram: string | null
          logo: string | null
          name: string
          phone: string | null
          plan: string
          postal_code: string | null
          state: string | null
          street: string | null
          updated_at: string
          website: string | null
        }
        Insert: {
          business_hours?: Json | null
          city?: string | null
          country?: string | null
          created_at?: string
          email?: string | null
          id?: string
          instagram?: string | null
          logo?: string | null
          name: string
          phone?: string | null
          plan?: string
          postal_code?: string | null
          state?: string | null
          street?: string | null
          updated_at?: string
          website?: string | null
        }
        Update: {
          business_hours?: Json | null
          city?: string | null
          country?: string | null
          created_at?: string
          email?: string | null
          id?: string
          instagram?: string | null
          logo?: string | null
          name?: string
          phone?: string | null
          plan?: string
          postal_code?: string | null
          state?: string | null
          street?: string | null
          updated_at?: string
          website?: string | null
        }
        Relationships: []
      }
      product_cache: {
        Row: {
          access_count: number | null
          brand: string
          citations: Json | null
          created_at: string | null
          id: string
          last_accessed_at: string | null
          product_line: string | null
          query_text: string
          response_data: Json
          updated_at: string | null
        }
        Insert: {
          access_count?: number | null
          brand: string
          citations?: Json | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          product_line?: string | null
          query_text: string
          response_data: Json
          updated_at?: string | null
        }
        Update: {
          access_count?: number | null
          brand?: string
          citations?: Json | null
          created_at?: string | null
          id?: string
          last_accessed_at?: string | null
          product_line?: string | null
          query_text?: string
          response_data?: Json
          updated_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          bio: string | null
          created_at: string
          email: string
          id: string
          is_freelance: boolean
          license_number: string | null
          name: string
          phone: string | null
          photo: string | null
          specialty: string | null
          updated_at: string
        }
        Insert: {
          bio?: string | null
          created_at?: string
          email: string
          id: string
          is_freelance?: boolean
          license_number?: string | null
          name: string
          phone?: string | null
          photo?: string | null
          specialty?: string | null
          updated_at?: string
        }
        Update: {
          bio?: string | null
          created_at?: string
          email?: string
          id?: string
          is_freelance?: boolean
          license_number?: string | null
          name?: string
          phone?: string | null
          photo?: string | null
          specialty?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      rate_limits: {
        Row: {
          created_at: string | null
          daily_reset_at: string | null
          hourly_reset_at: string | null
          last_request_at: string | null
          requests_this_hour: number | null
          requests_today: number | null
          updated_at: string | null
          user_id: string
        }
        Insert: {
          created_at?: string | null
          daily_reset_at?: string | null
          hourly_reset_at?: string | null
          last_request_at?: string | null
          requests_this_hour?: number | null
          requests_today?: number | null
          updated_at?: string | null
          user_id: string
        }
        Update: {
          created_at?: string | null
          daily_reset_at?: string | null
          hourly_reset_at?: string | null
          last_request_at?: string | null
          requests_this_hour?: number | null
          requests_today?: number | null
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
      security_events: {
        Row: {
          created_at: string
          details: Json | null
          event_type: string
          id: string
          ip_address: string | null
          request_body: Json | null
          request_method: string | null
          request_path: string | null
          severity: string
          user_agent: string | null
          user_id: string | null
        }
        Insert: {
          created_at?: string
          details?: Json | null
          event_type: string
          id?: string
          ip_address?: string | null
          request_body?: Json | null
          request_method?: string | null
          request_path?: string | null
          severity: string
          user_agent?: string | null
          user_id?: string | null
        }
        Update: {
          created_at?: string
          details?: Json | null
          event_type?: string
          id?: string
          ip_address?: string | null
          request_body?: Json | null
          request_method?: string | null
          request_path?: string | null
          severity?: string
          user_agent?: string | null
          user_id?: string | null
        }
        Relationships: []
      }
      user_brand_preferences: {
        Row: {
          brand_preferences: Json
          created_at: string | null
          id: string
          updated_at: string | null
          user_id: string
        }
        Insert: {
          brand_preferences?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id: string
        }
        Update: {
          brand_preferences?: Json
          created_at?: string | null
          id?: string
          updated_at?: string | null
          user_id?: string
        }
        Relationships: []
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      check_rate_limit: {
        Args: {
          p_daily_limit?: number
          p_hourly_limit?: number
          p_user_id: string
        }
        Returns: boolean
      }
      cleanup_expired_ai_cache: { Args: never; Returns: number }
      cleanup_expired_intent_cache: { Args: never; Returns: number }
      cleanup_old_hair_photos: {
        Args: never
        Returns: {
          consultation_deleted: number
          formula_deleted: number
        }[]
      }
      cleanup_old_security_events: { Args: never; Returns: number }
      cleanup_product_cache:
        | { Args: { days_old?: number }; Returns: number }
        | { Args: never; Returns: undefined }
      log_security_event: {
        Args: {
          p_details?: Json
          p_event_type: string
          p_ip_address?: string
          p_request_body?: Json
          p_request_method?: string
          p_request_path?: string
          p_severity: string
          p_user_agent?: string
          p_user_id?: string
        }
        Returns: string
      }
      user_is_org_member: {
        Args: { org_id: string; user_id: string }
        Returns: boolean
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const
