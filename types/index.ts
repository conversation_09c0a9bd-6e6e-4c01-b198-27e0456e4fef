export type MessageRole = 'user' | 'assistant';

export type Message = {
  id: string;
  role: MessageRole;
  content: string;
  timestamp: Date;
  image?: string;
  images?: string[];
};

export type Client = {
  id: string;
  name: string;
  email?: string;
  phone?: string;
  lastVisit?: Date;
  photo?: string;
  notes?: string;
  knownAllergies?: string;
  isPregnantOrBreastfeeding: boolean;
  hasSensitiveScalp: boolean;
  chemicalTreatments: {
    henna: boolean;
    chemicalStraightening: boolean;
    keratin: boolean;
  };
  communicationPreferences: {
    acceptsReminders: boolean;
    preferredMethod: 'whatsapp' | 'sms';
  };
  additionalNotes?: string;
};

// Formula History Types (matches DB schema)
export type ProductUsed = {
  name: string;
  amount: string;
  code?: string;
};

export type ServiceType = 'color' | 'highlights' | 'balayage' | 'toner' | 'correction';

export type Formula = {
  id: string;
  clientId: string;
  userId: string;

  // Session tracking
  sessionNumber: number;
  serviceType: ServiceType;

  // Color transformation data
  currentLevel?: number;
  targetLevel?: number;
  targetTone?: string;
  targetReflection?: string;

  // Brand and technique
  brand: string;
  productLine?: string;
  technique?: string;

  // Formula content (AI-generated)
  formulaText: string;

  // Structured product data
  productsUsed: ProductUsed[];

  // Formula metadata
  totalCost?: number;
  estimatedDurationMinutes?: number;

  // Analysis snapshots (for reference in future sessions)
  currentColorAnalysis?: HairAnalysis;
  desiredColorAnalysis?: DesiredColorAnalysis;
  safetyChecklist?: FormulaData['safetyChecklist'];

  // Timestamps
  createdAt: Date;
  updatedAt: Date;
};

export type NoteType = 'observation' | 'reminder' | 'learning' | 'adjustment';

export type FormulaNotes = {
  id: string;
  formulaId: string;
  userId: string;
  noteText: string;
  noteType: NoteType;
  sectionReference?: string; // e.g., 'application', 'timing', 'products'
  createdAt: Date;
  updatedAt: Date;
};

export type ChemicalHistory = {
  lastProcessType: 'coloración' | 'permanente' | 'mechas' | 'balayage' | 'alisado' | 'queratina' | 'henna' | 'ninguno' | string;
  lastProcessDate?: string;
  hasUsedHomeRemedies: boolean;
  homeRemediesDetails?: string;
};

export type PhysicalMeasurements = {
  totalLength: number;
  monthlyGrowth: number;
};

export type GeneralCharacteristics = {
  thickness: 'fino' | 'medio' | 'grueso';
  density: 'baja' | 'media' | 'alta';
  predominantTone: string;
  predominantToneColor?: string;
  predominantReflection: string;
  predominantReflectionColor?: string;
  lighteningBase: string;
  lighteningBaseColor?: string;
};

export type ZoneAnalysis = {
  level: number;
  tone: string;
  toneColor?: string;
  reflection: string;
  reflectionColor?: string;
  lighteningBase: string;
  lighteningBaseColor?: string;
  state: 'excelente' | 'bueno' | 'regular' | 'dañado' | 'muy dañado';
  unwantedShade?: string;
  unwantedShadeColor?: string;
  pigmentAccumulation: boolean;
  cuticleState: 'cerrada' | 'semiabierta' | 'abierta' | 'muy dañada';
  porosity: 'baja' | 'media' | 'alta';
  elasticity: 'buena' | 'media' | 'baja';
  resistance: 'alta' | 'media' | 'baja';
  damageLevel: number;
};

export type GrayAnalysis = {
  percentage: number;
  visibility: number;
  type: 'vítreo' | 'normal' | 'resistente' | 'amarillento';
  distributionPattern: 'uniforme' | 'en mechones' | 'en zonas' | 'corona' | 'sienes';
};

export type HairAnalysis = {
  chemicalHistory: ChemicalHistory;
  physicalMeasurements: PhysicalMeasurements;
  generalCharacteristics: GeneralCharacteristics;
  roots: ZoneAnalysis;
  mids: ZoneAnalysis;
  ends: ZoneAnalysis;
  grayAnalysis: GrayAnalysis;
  hasDemarcationBands: boolean;
  demarcationBandsDetails?: string;
};

export type DesiredColorZone = {
  level: number;
  tone: string;
  toneColor?: string;
  reflection: string;
  reflectionColor?: string;
};

export type DesiredColorAnalysis = {
  level: number;
  tone: string;
  toneColor?: string;
  reflection: string;
  reflectionColor?: string;
  reflectionIntensity: 'sutil' | 'medio' | 'intenso';
  grayCoverage: number;
  resultType: 'natural' | 'vibrante' | 'fantasía';
  technique: string;
  colorDepth: 'sutil' | 'medio' | 'profundo';
  roots?: DesiredColorZone;
  mids?: DesiredColorZone;
  ends?: DesiredColorZone;
  notes?: string;
};

export type FormulaData = {
  currentColorImages: string[];
  currentColorAnalysis?: HairAnalysis;
  desiredColorImages?: string[];
  desiredColorAnalysis?: DesiredColorAnalysis;
  technique?: string;
  safetyChecklist: {
    gloves: boolean;
    ventilation: boolean;
    patchTest: boolean;
    strandTest: boolean;
    noMetalSalts: boolean;
    hairCondition: boolean;
    photoConsentGiven: boolean;
  };
  clientName?: string;
  clientSignature?: string;
  brand?: string;
  productLine?: string;
  brandTier?: number; // Tier of selected brand (for analytics)
};

export type UserRole = 'owner' | 'admin' | 'stylist' | 'receptionist';

export type TeamMember = {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  photo?: string;
  specialty?: string;
  joinedDate: Date;
  isActive: boolean;
  permissions: {
    manageClients: boolean;
    createFormulas: boolean;
    viewReports: boolean;
    manageTeam: boolean;
    manageSettings: boolean;
  };
};

export type DaySchedule = {
  open: string;
  close: string;
  closed: boolean;
};

export type BusinessProfile = {
  name: string;
  logo?: string;
  street?: string;
  city?: string;
  postalCode?: string;
  state?: string;
  country?: string;
  phone?: string;
  email?: string;
  website?: string;
  instagram?: string;
  businessHours?: {
    monday: DaySchedule;
    tuesday: DaySchedule;
    wednesday: DaySchedule;
    thursday: DaySchedule;
    friday: DaySchedule;
    saturday: DaySchedule;
    sunday: DaySchedule;
  };
};

export type UserProfile = {
  id: string;
  name: string;
  email: string;
  photo?: string;
  phone?: string;
  specialty?: string;
  licenseNumber?: string;
  bio?: string;
  role: UserRole;
};

export type RegionalSettings = {
  language: 'es' | 'en';
  measurementSystem: 'metric' | 'imperial';
  currency: 'USD' | 'EUR' | 'MXN' | 'COP' | 'ARS' | 'CLP';
  timezone: string;
  dateFormat: 'DD/MM/YYYY' | 'MM/DD/YYYY';
};

export type NotificationSettings = {
  push: {
    enabled: boolean;
    appointments: boolean;
    messages: boolean;
    teamUpdates: boolean;
    marketing: boolean;
  };
  email: {
    enabled: boolean;
    appointments: boolean;
    weeklyReport: boolean;
    marketing: boolean;
  };
  reminders: {
    appointmentsBefore: number;
    formulaExpiry: boolean;
  };
};

// Professional Hair Color Brands Types
export type BrandLine = {
  permanent?: string[];
  demi_permanent?: string[];
  lighteners?: string[];
  direct_dye?: string[];
  other?: string[];
};

export type BrandNotes = {
  [key: string]: string;
};

export type ProfessionalBrand = {
  id: string;
  name: string;
  tier: number; // 1 = Global Leaders, 2 = Regional/Specialized, 3 = Niche/Organic
  tier_description: string;
  country: string;
  lines: BrandLine;
  key_features?: string[];
  notes?: BrandNotes;
  specialization?: string;
  distribution?: string; // Deprecated, not in JSON 2025
};

export type BrandCategory = {
  category: string;
  lines: string[];
};

// Brand Preferences Types
export type BrandPreference = {
  brandId: string;
  preferredLines: string[]; // Empty array = all lines
  isPrimary: boolean; // Main brand used in the salon
};

export type UserBrandPreferences = {
  brands: BrandPreference[];
  updatedAt: Date;
};

// App Context Types for AI Contextual Awareness
export type ConversationIntent =
  | 'diagnostico'
  | 'formula'
  | 'soporte'
  | 'productos'
  | 'educacion'
  | 'seguimiento'
  | 'saludo'
  | 'otro';

export interface ConversationMemorySnapshot {
  summary: string;
  keyFacts: string[];
  followUpQuestions: string[];
  lastIntent?: ConversationIntent | null;
  updatedAt?: Date;
}

export type AppContext =
  | FormulaStepContext
  | MainChatContext
  | null;

export interface FormulaStepContext {
  screen: 'formula_step_5';
  currentFormula: {
    naturalBase?: number;
    grayPercentage?: number;
    currentTone?: string;
    desiredLevel?: string;
    desiredTone?: string;
    selectedBrand?: string;
    productLine?: string;
    chemicalHistory?: string[];
    technique?: string;
    generatedFormula?: string; // 🆕 NEW: The actual formula that was generated
  };
  memory?: ConversationMemorySnapshot | null;
  intent?: ConversationIntent;
}

export interface MainChatContext {
  screen: 'main_chat';
  userPreferences: {
    brands: string[];
  };
  memory?: ConversationMemorySnapshot | null;
  intent?: ConversationIntent;
}
