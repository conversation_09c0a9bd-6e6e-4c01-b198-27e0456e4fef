# Repository Guidelines

These instructions are optimized for Codex agents collaborating on Salonier AI; if you swap to <PERSON> or another assistant, sync on workflow differences before pushing.

## Project Structure & Module Organization
- `app/` holds Expo Router screens; feature flows such as chat live in `app/(app)/(tabs)/`.
- Shared UI and logic live in `components/`, `contexts/`, and `lib/` (e.g., `lib/supabase.ts`, `lib/ai-client.ts`).
- Static assets stay in `assets/`; Expo and TypeScript config lives in `app.json` and `tsconfig.json`.
- Automated scenarios and reports reside under `tests/`, `testing/`, and `sessions/`.

## Build, Test, and Development Commands
- `bun install` installs dependencies (Bun is required).
- `bun run start` boots the Expo dev server with Rork’s project ID for device or simulator testing.
- `bun run start-web` serves the app in a browser tunnel for quick UI checks.
- `bun run lint` enforces Expo/ESLint rules; fix warnings before opening a PR.
- `bunx jest tests/chat-multi-turn.test.ts` runs the typed unit tests that validate chat history logic.

## Coding Style & Naming Conventions
- TypeScript is strict (`tsconfig.json`), using the `@/*` alias for absolute imports.
- Follow Expo’s ESLint config (2-space indentation, single quotes in JSX, semicolons optional but consistent).
- Components, contexts, and hooks use PascalCase / `use*` names (`ChatProvider`, `useChat`); files exporting a default screen should match the route name.
- Styling combines `StyleSheet.create` and NativeWind `className` strings; reuse color tokens from `constants/colors.ts`.

## Testing Guidelines
- Keep Jest unit tests in `tests/`; mirror filenames with the module under test and prefer Spanish descriptions where established.
- For AI formulation flows, reuse scripts in `testing/scripts/` (`bun run testing/scripts/test-formula-generation.mjs`) and validators in `testing/validation/`.
- Record manual scenario outcomes in `sessions/` Markdown logs so the QA history stays traceable.
- Target critical logic (chat history, Supabase persistence) with regression tests before merging.

## Commit & Pull Request Guidelines
- Follow the existing Conventional Commit style (`fix:`, `refactor:`, `feat:`) observed in `git log`.
- Scope each PR to one feature or fix, include a concise summary, linked issue, and a “Test Plan” showing the commands you ran.
- Attach screenshots or screen recordings for UI changes (especially new flows under `app/(app)/(tabs)/`).
- Confirm `bun run lint` and relevant Jest or Bun scripts succeed before requesting review.

## Security & Configuration Tips
- Keep Supabase keys in `.env` or Expo environment files (`EXPO_PUBLIC_SUPABASE_URL`, `EXPO_PUBLIC_SUPABASE_ANON_KEY`); `lib/supabase.ts` will throw if they are missing.
- Avoid committing production secrets and review `SECURITY.md` for threat modelling updates.
- Run linting and tests after upgrading dependencies to catch regressions in AI-critical modules.
