#!/usr/bin/env python3
"""
Script de deployment automatizado para Supabase
Ejecuta migraciones SQL y despliega Edge Functions
"""

import os
import json
import subprocess
import sys

# Colores para output
class Colors:
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    RED = '\033[91m'
    BLUE = '\033[94m'
    END = '\033[0m'

def print_step(message):
    print(f"\n{Colors.BLUE}📝 {message}{Colors.END}")

def print_success(message):
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")

def print_error(message):
    print(f"{Colors.RED}❌ {message}{Colors.END}")

def print_warning(message):
    print(f"{Colors.YELLOW}⚠️  {message}{Colors.END}")

def main():
    print(f"\n{Colors.BLUE}{'='*60}")
    print("🚀 Deployment Automatizado de Salonier AI")
    print(f"{'='*60}{Colors.END}\n")

    # Cambiar al directorio del proyecto
    os.chdir('/Users/<USER>/Salonier-AI')

    # Paso 1: Migraciones SQL
    print_step("PASO 1: Migraciones SQL")
    print_warning("Las migraciones SQL requieren ejecución manual en el dashboard")
    print(f"\n{Colors.YELLOW}Por favor, ejecuta los siguientes pasos:{Colors.END}")
    print("1. Abre: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new")
    print("2. Copia el contenido de: supabase/migrations/EXECUTE_ALL_MIGRATIONS.sql")
    print("3. Pega en el SQL Editor y haz click en 'RUN'")

    input(f"\n{Colors.YELLOW}Presiona ENTER cuando hayas ejecutado las migraciones...{Colors.END}")
    print_success("Migraciones marcadas como completadas")

    # Paso 2: Edge Function Deployment
    print_step("PASO 2: Desplegar Edge Function 'ai-proxy'")

    # Verificar que existe el archivo de la función
    function_path = "supabase/functions/ai-proxy/index.ts"
    if not os.path.exists(function_path):
        print_error(f"No se encontró el archivo: {function_path}")
        sys.exit(1)

    print_success(f"Función encontrada: {function_path}")

    # Solicitar access token
    print(f"\n{Colors.YELLOW}Necesitas un access token de Supabase:{Colors.END}")
    print("1. Abre: https://supabase.com/dashboard/account/tokens")
    print("2. Click 'Generate new token' → Nombre: 'Salonier AI CLI'")
    print("3. Copia el token")

    access_token = input(f"\n{Colors.BLUE}Pega tu access token aquí: {Colors.END}").strip()

    if not access_token:
        print_error("Access token es requerido")
        sys.exit(1)

    # Configurar el token
    os.environ['SUPABASE_ACCESS_TOKEN'] = access_token

    # Desplegar la función
    print(f"\n{Colors.BLUE}Desplegando función...{Colors.END}")
    try:
        result = subprocess.run(
            ['supabase', 'functions', 'deploy', 'ai-proxy', '--project-ref', 'guyxczavhtemwlrknqpm'],
            capture_output=True,
            text=True,
            check=True
        )
        print(result.stdout)
        print_success("Edge Function desplegada exitosamente")
    except subprocess.CalledProcessError as e:
        print_error(f"Error al desplegar función: {e.stderr}")
        sys.exit(1)

    # Paso 3: Configurar Secrets
    print_step("PASO 3: Configurar API Keys en Supabase Secrets")
    print_warning("Los secrets deben configurarse manualmente")
    print(f"\n{Colors.YELLOW}Por favor, sigue estos pasos:{Colors.END}")
    print("1. Abre: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions")
    print("2. Scroll a 'Secrets' y click 'Add new secret'")
    print("3. Agregar:")
    print("   - OPENAI_API_KEY (obtener de: https://platform.openai.com/api-keys)")
    print("   - PERPLEXITY_API_KEY (obtener de: https://www.perplexity.ai/settings/api)")

    input(f"\n{Colors.YELLOW}Presiona ENTER cuando hayas configurado los secrets...{Colors.END}")
    print_success("Secrets configurados")

    # Verificación final
    print_step("PASO 4: Verificación")
    print(f"\n{Colors.GREEN}{'='*60}")
    print("🎉 Deployment Completado!")
    print(f"{'='*60}{Colors.END}\n")

    print("Verificación:")
    print("✅ Migraciones SQL ejecutadas")
    print("✅ Edge Function 'ai-proxy' desplegada")
    print("✅ Secrets configurados")

    print(f"\n{Colors.BLUE}Próximos pasos:{Colors.END}")
    print("1. Iniciar la app: npm run start-web")
    print("2. Probar el chat y verificar funcionamiento")
    print("3. Monitorear costos en: ai_usage_log table")

    print(f"\n{Colors.GREEN}📚 Documentación:{Colors.END}")
    print("- DEPLOYMENT_INSTRUCTIONS.md")
    print("- DEPLOYMENT_STATUS.md")
    print("- sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print(f"\n\n{Colors.RED}❌ Deployment cancelado por el usuario{Colors.END}")
        sys.exit(1)
    except Exception as e:
        print(f"\n{Colors.RED}❌ Error inesperado: {e}{Colors.END}")
        sys.exit(1)
