#!/bin/bash

# Script para configurar los secrets de las API keys en Supabase
# SECURITY: Este script lee las API keys de variables de entorno
#
# Este es un archivo de EJEMPLO - copia y configura tus propias keys
#
# USO:
#   1. Copia este archivo: cp configure-secrets.sh.example configure-secrets.sh
#   2. Configura tus variables de entorno:
#      export OPENAI_API_KEY='sk-proj-...'
#      export PERPLEXITY_API_KEY='pplx-...'
#   3. Ejecuta: ./configure-secrets.sh
#
# O alternativamente:
#   1. Agrega las keys a tu archivo .env.local
#   2. Carga las variables: source .env.local
#   3. Ejecuta: ./configure-secrets.sh

set -e

echo "🔐 Configurando API Keys en Supabase Edge Functions..."
echo ""

PROJECT_REF="guyxczavhtemwlrknqpm"

# Leer API keys de variables de entorno
OPENAI_KEY="${OPENAI_API_KEY:-}"
PERPLEXITY_KEY="${PERPLEXITY_API_KEY:-}"

# Validar que las variables de entorno estén configuradas
if [ -z "$OPENAI_KEY" ]; then
    echo "❌ Error: OPENAI_API_KEY no está configurado"
    echo ""
    echo "Por favor, exporta las API keys como variables de entorno:"
    echo "  export OPENAI_API_KEY='sk-proj-...'"
    echo "  export PERPLEXITY_API_KEY='pplx-...'"
    echo ""
    echo "O cárgalas desde .env.local:"
    echo "  source .env.local"
    echo "  ./configure-secrets.sh"
    echo ""
    echo "Para obtener las API keys:"
    echo "  OpenAI: https://platform.openai.com/api-keys"
    echo "  Perplexity: https://www.perplexity.ai/settings/api"
    exit 1
fi

if [ -z "$PERPLEXITY_KEY" ]; then
    echo "❌ Error: PERPLEXITY_API_KEY no está configurado"
    echo ""
    echo "Por favor, exporta las API keys como variables de entorno:"
    echo "  export OPENAI_API_KEY='sk-proj-...'"
    echo "  export PERPLEXITY_API_KEY='pplx-...'"
    echo ""
    echo "O cárgalas desde .env.local:"
    echo "  source .env.local"
    echo "  ./configure-secrets.sh"
    echo ""
    echo "Para obtener las API keys:"
    echo "  OpenAI: https://platform.openai.com/api-keys"
    echo "  Perplexity: https://www.perplexity.ai/settings/api"
    exit 1
fi

echo "📝 Configurando secrets para proyecto: $PROJECT_REF"
echo ""

# Configurar OpenAI API Key
echo "1️⃣ Configurando OPENAI_API_KEY..."
supabase secrets set OPENAI_API_KEY="$OPENAI_KEY" --project-ref "$PROJECT_REF"

if [ $? -eq 0 ]; then
    echo "   ✅ OPENAI_API_KEY configurado exitosamente"
else
    echo "   ❌ Error configurando OPENAI_API_KEY"
    exit 1
fi

echo ""

# Configurar Perplexity API Key
echo "2️⃣ Configurando PERPLEXITY_API_KEY..."
supabase secrets set PERPLEXITY_API_KEY="$PERPLEXITY_KEY" --project-ref "$PROJECT_REF"

if [ $? -eq 0 ]; then
    echo "   ✅ PERPLEXITY_API_KEY configurado exitosamente"
else
    echo "   ❌ Error configurando PERPLEXITY_API_KEY"
    exit 1
fi

echo ""
echo "🎉 ¡Secrets configurados exitosamente!"
echo ""
echo "📋 Próximos pasos:"
echo "   1. Verificar secrets: supabase secrets list --project-ref $PROJECT_REF"
echo "   2. Probar la Edge Function con: bun run start-web"
echo "   3. Ir a la app y testear el chat"
echo ""
