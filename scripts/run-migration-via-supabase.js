/**
 * Ejecutar migración usando el cliente de Supabase JS
 * con Service Role Key (tiene permisos de admin)
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { resolve } from 'path';

async function runMigration() {
  console.log('🔄 Iniciando migración de analytics_events via Supabase JS...\n');

  const SUPABASE_URL = process.env.EXPO_PUBLIC_SUPABASE_URL;
  const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!SUPABASE_URL || !SUPABASE_SERVICE_KEY) {
    console.error('❌ Error: Variables de entorno no encontradas');
    console.error('   EXPO_PUBLIC_SUPABASE_URL:', !!SUPABASE_URL);
    console.error('   SUPABASE_SERVICE_ROLE_KEY:', !!SUPABASE_SERVICE_KEY);
    process.exit(1);
  }

  // Crear cliente con Service Role Key (bypass RLS)
  const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  console.log('✅ Cliente de Supabase creado con Service Role Key\n');

  try {
    // Leer archivo de migración
    const migrationPath = resolve(process.cwd(), 'supabase/migrations/20251023120000_create_analytics_events.sql');
    console.log(`📄 Leyendo migración: ${migrationPath}`);
    const fullSQL = readFileSync(migrationPath, 'utf-8');
    console.log(`✅ Archivo leído (${fullSQL.length} caracteres)\n`);

    // Dividir en statements individuales (separados por ;)
    // y ejecutar uno por uno
    const statements = fullSQL
      .split(';')
      .map(s => s.trim())
      .filter(s => s.length > 0 && !s.startsWith('--'));

    console.log(`⚙️  Ejecutando ${statements.length} SQL statements...\n`);

    for (let i = 0; i < statements.length; i++) {
      const statement = statements[i] + ';';

      // Skip comments
      if (statement.trim().startsWith('--')) continue;

      console.log(`[${i + 1}/${statements.length}] Ejecutando statement...`);
      console.log(`  ${statement.substring(0, 60)}...`);

      try {
        const { data, error } = await supabase.rpc('exec_sql', {
          query: statement
        });

        if (error) {
          console.error(`  ❌ Error: ${error.message}`);

          // Si el error es "already exists", continuar
          if (error.message.includes('already exists')) {
            console.log('  ⚠️  Ya existe, continuando...');
            continue;
          }

          throw error;
        }

        console.log('  ✅ Ejecutado');
      } catch (err) {
        // Intentar con .from().select() si rpc no funciona
        console.log('  ⚠️  RPC no disponible, intentando método alternativo...');

        // Para CREATE TABLE, no podemos usar el cliente JS directamente
        // Necesitamos usar una función SQL o el API REST directo
        throw new Error('No se puede ejecutar DDL directamente con Supabase JS client. Use el SQL Editor del Dashboard.');
      }
    }

    console.log('\n' + '='.repeat(60));
    console.log('✅ Todos los statements ejecutados');
    console.log('='.repeat(60));

    // Verificar que la tabla se creó
    console.log('\n🔍 Verificando que la tabla se creó...');
    const { data: tables, error: tablesError } = await supabase
      .from('information_schema.tables')
      .select('table_name')
      .eq('table_schema', 'public')
      .eq('table_name', 'analytics_events');

    if (tablesError) {
      console.log('⚠️  No se pudo verificar via information_schema, intentando query directa...');

      // Intentar insertar un registro de prueba
      const { error: insertError } = await supabase
        .from('analytics_events')
        .insert({
          event_name: 'test_migration',
          properties: { test: true },
          user_id: '00000000-0000-0000-0000-000000000000'
        });

      if (insertError) {
        if (insertError.message.includes('relation "analytics_events" does not exist')) {
          console.error('❌ La tabla NO se creó correctamente');
          throw insertError;
        } else if (insertError.message.includes('violates foreign key')) {
          console.log('✅ La tabla existe (error de FK es esperado en test)');
        } else {
          console.log('⚠️  Error de test:', insertError.message);
          console.log('   Pero la tabla probablemente existe');
        }
      } else {
        console.log('✅ La tabla existe y funciona');
        // Limpiar test
        await supabase
          .from('analytics_events')
          .delete()
          .eq('event_name', 'test_migration');
      }
    } else {
      console.log('✅ Tabla analytics_events verificada');
    }

    console.log('\n' + '='.repeat(60));
    console.log('🎉 MIGRACIÓN COMPLETADA');
    console.log('='.repeat(60));
    console.log('\n✅ La tabla analytics_events está lista para usar\n');

  } catch (error) {
    console.error('\n❌ Error ejecutando migración:');
    console.error(error.message);

    if (error.message.includes('Use el SQL Editor')) {
      console.log('\n💡 SOLUCIÓN:');
      console.log('   El cliente de Supabase JS no puede ejecutar DDL directamente.');
      console.log('   Debes usar el SQL Editor del Dashboard de Supabase.');
      console.log('\n   URL: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new');
      console.log('\n   SQL ya copiado en clipboard. Solo pega y ejecuta.');
    }

    process.exit(1);
  }
}

// Ejecutar
runMigration();
