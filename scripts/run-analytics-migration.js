/**
 * Script para ejecutar la migración de analytics_events directamente
 * Conecta a PostgreSQL y ejecuta el SQL
 */

import { Client } from 'pg';
import { readFileSync } from 'fs';
import { resolve } from 'path';

async function runMigration() {
  console.log('🔄 Iniciando migración de analytics_events...\n');

  // Construir connection string desde variables de entorno
  const SUPABASE_DB_PASSWORD = process.env.SUPABASE_DB_PASSWORD;
  // Usar conexión directa (no pooler) para ejecutar DDL
  const connectionString = `postgresql://postgres.guyxczavhtemwlrknqpm:${SUPABASE_DB_PASSWORD}@db.guyxczavhtemwlrknqpm.supabase.co:5432/postgres`;

  if (!SUPABASE_DB_PASSWORD) {
    console.error('❌ Error: SUPABASE_DB_PASSWORD no está definida en .env.local');
    process.exit(1);
  }

  // Crear cliente de PostgreSQL
  const client = new Client({
    connectionString,
    ssl: {
      rejectUnauthorized: false
    }
  });

  try {
    // Conectar
    console.log('🔌 Conectando a PostgreSQL...');
    await client.connect();
    console.log('✅ Conectado exitosamente\n');

    // Leer archivo de migración
    const migrationPath = resolve(process.cwd(), 'supabase/migrations/20251023120000_create_analytics_events.sql');
    console.log(`📄 Leyendo migración: ${migrationPath}`);
    const sql = readFileSync(migrationPath, 'utf-8');
    console.log(`✅ Archivo leído (${sql.length} caracteres)\n`);

    // Ejecutar SQL
    console.log('⚙️  Ejecutando SQL...');
    await client.query(sql);
    console.log('✅ SQL ejecutado exitosamente\n');

    // Verificar que la tabla se creó
    console.log('🔍 Verificando que la tabla se creó...');
    const checkTable = await client.query(`
      SELECT tablename
      FROM pg_tables
      WHERE schemaname = 'public'
      AND tablename = 'analytics_events'
    `);

    if (checkTable.rows.length > 0) {
      console.log('✅ Tabla analytics_events creada exitosamente');
    } else {
      console.error('❌ Error: Tabla no encontrada después de ejecutar migración');
      process.exit(1);
    }

    // Verificar columnas
    console.log('\n📋 Verificando estructura de la tabla...');
    const checkColumns = await client.query(`
      SELECT column_name, data_type
      FROM information_schema.columns
      WHERE table_name = 'analytics_events'
      ORDER BY ordinal_position
    `);

    console.log('\nColumnas creadas:');
    checkColumns.rows.forEach(row => {
      console.log(`  • ${row.column_name} (${row.data_type})`);
    });

    // Verificar índices
    console.log('\n🔑 Verificando índices...');
    const checkIndexes = await client.query(`
      SELECT indexname
      FROM pg_indexes
      WHERE tablename = 'analytics_events'
    `);

    console.log('\nÍndices creados:');
    checkIndexes.rows.forEach(row => {
      console.log(`  • ${row.indexname}`);
    });

    // Verificar políticas RLS
    console.log('\n🔒 Verificando políticas RLS...');
    const checkPolicies = await client.query(`
      SELECT policyname
      FROM pg_policies
      WHERE tablename = 'analytics_events'
    `);

    console.log('\nPolíticas RLS creadas:');
    checkPolicies.rows.forEach(row => {
      console.log(`  • ${row.policyname}`);
    });

    console.log('\n' + '='.repeat(60));
    console.log('🎉 MIGRACIÓN COMPLETADA EXITOSAMENTE');
    console.log('='.repeat(60));
    console.log('\n✅ La tabla analytics_events está lista para usar');
    console.log('✅ Los eventos de analytics se empezarán a registrar automáticamente\n');

  } catch (error) {
    console.error('\n❌ Error ejecutando migración:');
    console.error(error.message);

    if (error.code === 'ENOTFOUND') {
      console.error('\n💡 Sugerencia: Verifica tu conexión a internet');
    } else if (error.code === '28P01') {
      console.error('\n💡 Sugerencia: Verifica que SUPABASE_DB_PASSWORD sea correcta');
    } else if (error.message.includes('already exists')) {
      console.log('\n⚠️  La tabla/política ya existe. Verificando estado...');

      try {
        const checkTable = await client.query(`
          SELECT tablename
          FROM pg_tables
          WHERE schemaname = 'public'
          AND tablename = 'analytics_events'
        `);

        if (checkTable.rows.length > 0) {
          console.log('✅ La tabla analytics_events ya existía y está funcionando');
          console.log('✅ No es necesario ejecutar la migración nuevamente');
        }
      } catch (verifyError) {
        console.error('❌ Error verificando tabla:', verifyError.message);
      }
    }

    process.exit(1);
  } finally {
    // Cerrar conexión
    await client.end();
    console.log('🔌 Conexión cerrada\n');
  }
}

// Ejecutar
runMigration();
