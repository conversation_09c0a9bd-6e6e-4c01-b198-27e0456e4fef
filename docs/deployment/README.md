# Deployment Documentation

This directory contains detailed deployment documentation for Salonier AI.

## Quick Links

- **Main Deployment Guide**: [README_DEPLOYMENT.md](./README_DEPLOYMENT.md) - Start here
- **API Keys Setup**: [../CONFIGURAR_API_KEYS.md](../CONFIGURAR_API_KEYS.md) - Configure Supabase secrets
- **Security Guide**: [../../SECURITY.md](../../SECURITY.md) - Security best practices

## Historical Deployment Documents

These files document the deployment process and are kept for reference:

### Comprehensive Guides
- **[DEPLOYMENT_COMPLETO.md](./DEPLOYMENT_COMPLETO.md)** - Complete deployment summary with architecture, costs, and monitoring
- **[DEPLOYMENT_INSTRUCTIONS.md](./DEPLOYMENT_INSTRUCTIONS.md)** - Step-by-step deployment instructions
- **[TEST_DEPLOYMENT.md](./TEST_DEPLOYMENT.md)** - Testing plan and procedures

### Status Reports
- **[DEPLOYMENT_FINAL_STATUS.md](./DEPLOYMENT_FINAL_STATUS.md)** - Final deployment status
- **[DEPLOYMENT_STATUS.md](./DEPLOYMENT_STATUS.md)** - Intermediate deployment status
- **[COMANDOS_DEPLOYMENT.md](./COMANDOS_DEPLOYMENT.md)** - Command reference

## Architecture Overview

```
React Native App
    ↓
lib/ai-client.ts
    ↓
Supabase Edge Function: ai-proxy
    ↓
┌─────────────┬─────────────────┐
│   OpenAI    │   Perplexity    │
│  GPT-4o     │   Sonar Pro     │
└─────────────┴─────────────────┘
    ↓
PostgreSQL (logging, rate limits, cache)
```

## Key Resources

### Dashboards
- Functions: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions
- SQL Editor: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
- Logs: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/logs/edge-functions

### API Documentation
- OpenAI: https://platform.openai.com/docs
- Perplexity: https://docs.perplexity.ai/

## Cost Breakdown

| Operation | Provider | Model | Cost |
|-----------|----------|-------|------|
| Chat | OpenAI | gpt-4o-mini | $0.0003 |
| Vision analysis (3 imgs) | OpenAI | gpt-4o | $0.012 |
| Formula generation | OpenAI | gpt-4o | $0.025 |
| Product search (new) | Perplexity | sonar-pro | $0.008 |
| Product search (cached) | Cache | - | $0.000 |

**Total per client**: $0.051-0.059
**Monthly (100 clients)**: ~$5-6/month

## For More Information

See the [sessions/](../../sessions/) directory for detailed implementation logs and problem-solving documentation.
