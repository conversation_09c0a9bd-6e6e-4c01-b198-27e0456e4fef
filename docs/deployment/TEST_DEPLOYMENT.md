# ✅ Test del Deployment Completo

**Fecha**: 2025-10-22
**Estado**: Listo para testing

---

## 📋 Checklist Pre-Test

- [x] Edge Function `ai-proxy` desplegada
- [x] Secrets configurados en Supabase:
  - [x] `OPENAI_API_KEY`
  - [x] `PERPLEXITY_API_KEY`
- [x] Migraciones SQL ejecutadas (6 migraciones)
- [x] Lint pasó sin errores

---

## 🧪 Tests a Ejecutar

### Test 1: Chat Simple (GPT-4o-mini)

**Objetivo**: Verificar integración básica con OpenAI

**Pasos**:
1. Iniciar la app:
   ```bash
   npm run start-web
   ```

2. En el navegador:
   - Ir al tab **"Chat"**
   - Escribir: `¿Qué es la colorimetría capilar?`
   - Esperar respuesta

**Resultado esperado**:
- ✅ Respuesta coherente en ~3 segundos
- ✅ Sin errores en console del navegador
- ✅ Sin errores en terminal de npm

**Costo esperado**: ~$0.0003

---

### Test 2: Verificar Logging en Supabase

**Objetivo**: Confirmar que los logs se guardan correctamente

**Pasos**:
1. Después del Test 1, ir a Supabase SQL Editor:
   https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new

2. Ejecutar query:
   ```sql
   SELECT
     use_case,
     provider,
     model,
     prompt_tokens,
     completion_tokens,
     cost_usd,
     latency_ms,
     created_at
   FROM ai_usage_log
   ORDER BY created_at DESC
   LIMIT 5;
   ```

**Resultado esperado**:
- ✅ Al menos 1 registro del chat
- ✅ `use_case` = 'chat'
- ✅ `provider` = 'openai'
- ✅ `model` = 'gpt-4o-mini'
- ✅ `cost_usd` ≈ 0.0003
- ✅ `latency_ms` < 5000

---

### Test 3: Rate Limiting

**Objetivo**: Verificar que el rate limiting funciona

**Pasos**:
1. En Supabase SQL Editor:
   ```sql
   SELECT
     requests_today,
     requests_this_hour,
     last_request_at,
     daily_reset_at,
     hourly_reset_at
   FROM rate_limits
   WHERE user_id = (
     SELECT id FROM auth.users LIMIT 1
   );
   ```

**Resultado esperado**:
- ✅ Registro creado automáticamente
- ✅ `requests_today` incrementado
- ✅ `requests_this_hour` incrementado

---

### Test 4: Análisis Visual (GPT-4o Vision) - OPCIONAL

**Nota**: Este test consume más créditos (~$0.012), hazlo solo si quieres validar completamente.

**Pasos**:
1. En la app:
   - Ir a **"Crear Fórmula"**
   - Step 0: Seleccionar un cliente de prueba
   - Step 1: Subir 2-3 fotos de cabello (cualquier foto funciona)
   - Click **"Analizar"**

2. Esperar análisis (puede tardar 10-15 segundos)

**Resultado esperado**:
- ✅ JSON de análisis generado
- ✅ Campos poblados: `roots`, `mids`, `ends`, `grayAnalysis`
- ✅ Sin errores

**Verificar en SQL**:
```sql
SELECT
  use_case,
  provider,
  model,
  image_count,
  cost_usd,
  latency_ms
FROM ai_usage_log
WHERE use_case = 'vision_analysis'
ORDER BY created_at DESC
LIMIT 1;
```

**Esperado**:
- ✅ `image_count` = 2 o 3
- ✅ `cost_usd` ≈ 0.012
- ✅ `latency_ms` < 20000

---

### Test 5: Storage Cleanup

**Objetivo**: Verificar que las imágenes se eliminan después del análisis

**Pasos**:
1. Después del Test 4, ir a Storage:
   https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/storage/buckets/hair-images-temp

2. Verificar que el bucket esté vacío

**Resultado esperado**:
- ✅ Sin archivos residuales (se eliminan automáticamente)

---

## 🐛 Troubleshooting

### Error: "Missing Authorization header"

**Solución**:
- Asegúrate de estar logueado en la app
- Verifica que Supabase auth esté configurado

### Error: "Invalid or expired token"

**Solución**:
- Logout y login nuevamente en la app
- Verificar `.env.local` tiene las credenciales correctas

### Error: OpenAI API dice "Invalid API Key"

**Solución**:
1. Ir a Supabase Dashboard → Settings → Functions → Secrets
2. Verificar que `OPENAI_API_KEY` esté configurado correctamente
3. Re-configurar si es necesario
4. **IMPORTANTE**: Después de cambiar secrets, reiniciar la Edge Function:
   - Dashboard → Functions → ai-proxy → ... → Restart

### Error: Perplexity API falla

**Solución**:
- Verificar que la cuenta de Perplexity tenga créditos
- Verificar que `PERPLEXITY_API_KEY` esté correcto
- Reiniciar Edge Function si cambiaste el secret

---

## 📊 Resultados Esperados

Si todos los tests pasan:

### Costos Totales

| Test | Operación | Costo |
|------|-----------|-------|
| Test 1 | Chat simple | $0.0003 |
| Test 4 | Análisis visual (3 imgs) | $0.012 |
| **TOTAL** | - | **~$0.0123** |

### Logs en Supabase

```sql
-- Deberías ver algo así:
 use_case        | provider | model        | cost_usd | latency_ms
-----------------+----------+--------------+----------+------------
 vision_analysis | openai   | gpt-4o       | 0.012    | 12453
 chat            | openai   | gpt-4o-mini  | 0.0003   | 2187
```

---

## ✅ Criteria de Éxito

Para considerar el deployment exitoso:

- [ ] Test 1 (Chat) pasa sin errores
- [ ] Logs aparecen en `ai_usage_log`
- [ ] Rate limiting funciona
- [ ] Costos están dentro de lo esperado (<$0.015 total)
- [ ] Sin errores en console del navegador
- [ ] Sin errores en logs de Supabase Functions

---

## 🎯 Próximos Pasos Después de Tests

1. **Si tests pasan**:
   ```bash
   git add .
   git commit -m "docs: Agregar guías de deployment y testing"
   git push origin feature/openai-perplexity-migration
   ```

2. **Crear Pull Request** a `main`

3. **Merge** y deployment completo ✅

4. **Monitorear costos** primeras 24-48 horas

---

## 📚 Archivos de Referencia

- **Implementación completa**: `sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md`
- **Deployment**: `sessions/2025-10-22-deployment-edge-function.md`
- **Configuración API Keys**: `CONFIGURAR_API_KEYS.md`
- **Este test plan**: `TEST_DEPLOYMENT.md`

---

**Estado**: Listo para comenzar tests

🚀 **Inicia con Test 1!**
