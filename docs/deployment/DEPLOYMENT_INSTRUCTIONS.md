# 🚀 Instrucciones de Deployment - Migración OpenAI + Perplexity

## Estado Actual
✅ Código implementado y commiteado en rama `feature/openai-perplexity-migration`
⏳ Falta configuración en Supabase (pasos manuales requeridos)

---

## PASO 1: Ejecutar Migraciones SQL ⚡ (5 minutos)

### Opción A: Ejecutar TODAS las migraciones de una vez (RECOMENDADO)

1. Abrir el SQL Editor de Supabase:
   ```
   https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
   ```

2. Copiar **TODO** el contenido del archivo:
   ```
   supabase/migrations/EXECUTE_ALL_MIGRATIONS.sql
   ```

3. Pegar en el editor y hacer click en **"RUN"** (o `Ctrl/Cmd + Enter`)

4. Verificar que no haya errores en la salida

### Opción B: Ejecutar una por una (si prefieres control granular)

Ejecutar en orden los siguientes archivos (mismo proceso que Opción A):

1. `supabase/migrations/20251021210000_create_storage_bucket.sql`
2. `supabase/migrations/20251021210100_create_ai_usage_log.sql`
3. `supabase/migrations/20251021210200_create_rate_limits.sql`
4. `supabase/migrations/20251021210300_create_product_cache.sql`

### ✅ Verificación

Después de ejecutar, verifica en el dashboard que existan:

- **Storage → Buckets**: Debe existir `hair-images-temp`
- **Table Editor**: Deben existir las tablas:
  - `ai_usage_log`
  - `rate_limits`
  - `product_cache`

---

## PASO 2: Configurar API Keys 🔑 (3 minutos)

### 2.1 Obtener API Keys

**OpenAI:**
1. Ir a: https://platform.openai.com/api-keys
2. Click "Create new secret key"
3. Nombre: `Salonier AI Production`
4. Copiar la key (empieza con `sk-proj-...`)

**Perplexity:**
1. Ir a: https://www.perplexity.ai/settings/api
2. Click "Generate API Key"
3. Copiar la key (empieza con `pplx-...`)

### 2.2 Agregar Keys a Supabase

1. Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions
2. Scroll hasta la sección **"Secrets"**
3. Click en **"Add new secret"**
4. Agregar las siguientes secrets (una por una):

   | Name | Value |
   |------|-------|
   | `OPENAI_API_KEY` | `sk-proj-...` (tu key de OpenAI) |
   | `PERPLEXITY_API_KEY` | `pplx-...` (tu key de Perplexity) |

5. Click **"Save"** después de agregar cada secret

### ✅ Verificación

Deberías ver 2 secrets en la lista (los valores estarán ocultos con `***`).

---

## PASO 3: Desplegar Edge Function 📦 (5 minutos)

### Opción A: Via Supabase CLI (si tienes acceso)

```bash
# Desde la raíz del proyecto
cd /Users/<USER>/Salonier-AI

# Login a Supabase (abrirá navegador)
supabase login

# Linkear proyecto
supabase link --project-ref guyxczavhtemwlrknqpm

# Deploy
supabase functions deploy ai-proxy

# Verificar
supabase functions list
```

### Opción B: Via Dashboard de Supabase (si CLI no funciona)

1. Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions

2. Click en **"Create a new function"**

3. Configurar:
   - **Name**: `ai-proxy`
   - **Import from project**: Seleccionar archivo
   - Subir: `supabase/functions/ai-proxy/index.ts`

4. Click **"Deploy function"**

### ✅ Verificación

Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions

Deberías ver `ai-proxy` en la lista con estado "Active" (verde).

---

## PASO 4: Configurar Storage Lifecycle (OPCIONAL pero recomendado) ⏱️ (2 minutos)

Esto eliminará automáticamente las imágenes después de 24h para ahorrar storage.

1. Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/storage/buckets/hair-images-temp

2. Click en **"Settings"** (pestaña superior)

3. Scroll a **"Object lifecycle rules"**

4. Click **"Add rule"**

5. Configurar:
   - **Rule name**: `Auto-delete after 24h`
   - **Delete objects older than**: `1 day`
   - **Object name prefix**: (dejar vacío para aplicar a todos)

6. Click **"Save"**

---

## PASO 5: Testing 🧪 (10 minutos)

### 5.1 Test Edge Function

Crear archivo `test-edge-function.sh`:

```bash
#!/bin/bash

# Obtener tu access token de Supabase Auth
# (Puedes obtenerlo desde el localStorage del navegador después de login)
AUTH_TOKEN="tu_token_aqui"

FUNCTION_URL="https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/ai-proxy"

echo "Test 1: Chat simple"
curl -X POST "$FUNCTION_URL" \
  -H "Authorization: Bearer $AUTH_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "useCase": "chat",
    "prompt": "¿Cuál es la diferencia entre tinte permanente y semipermanente?",
    "systemPrompt": "Eres un experto en coloración capilar."
  }'

echo -e "\n\n"
```

Ejecutar:
```bash
chmod +x test-edge-function.sh
./test-edge-function.sh
```

### 5.2 Test desde la App

1. Iniciar la app:
   ```bash
   npm run start-web
   # O si tienes bun:
   bun run start-web
   ```

2. Abrir en navegador (generalmente http://localhost:19006)

3. Probar:
   - **Chat simple**: Ir a tab "Chat" → Escribir pregunta
   - **Análisis visual**: "Crear Fórmula" → Step 1 → Subir imágenes
   - **Búsqueda productos**: Completar workflow hasta Step 5

### ✅ Verificación de Costos

Después de los tests, verificar logging:

```sql
-- Ejecutar en Supabase SQL Editor
SELECT
  use_case,
  provider,
  model,
  cost_usd,
  latency_ms,
  created_at
FROM ai_usage_log
ORDER BY created_at DESC
LIMIT 10;
```

Deberías ver los requests registrados con sus costos.

---

## PASO 6: Merge a Main 🎯 (2 minutos)

Una vez que todo funciona:

```bash
# Asegurarte de estar en la rama feature
git checkout feature/openai-perplexity-migration

# Pull últimos cambios de main (por si acaso)
git checkout main
git pull origin main

# Volver a feature y hacer merge
git checkout feature/openai-perplexity-migration
git merge main  # Resolver conflictos si existen

# Push feature branch
git push origin feature/openai-perplexity-migration

# Crear PR en GitHub (o hacer merge directo si tienes permisos)
# Si haces merge directo:
git checkout main
git merge feature/openai-perplexity-migration
git push origin main
```

---

## Troubleshooting 🔧

### Error: "Invalid or expired token"
- **Causa**: Token de auth expirado
- **Solución**: Re-login en la app

### Error: "Rate limit exceeded"
- **Causa**: Superaste 100 requests/día o 20/hora
- **Solución**: Esperar o ajustar límites en función `check_rate_limit`

### Error: "Upload failed"
- **Causa**: Permisos de Storage incorrectos
- **Solución**: Re-ejecutar Migración 1

### Error: Edge Function no responde
- **Causa**: API keys no configuradas
- **Solución**: Verificar secrets en Supabase Dashboard

### Costos más altos de lo esperado
- **Causa**: Cache de Perplexity no funciona
- **Solución**: Verificar tabla `product_cache` tiene datos

---

## Recursos

- **OpenAI Dashboard**: https://platform.openai.com/
- **Perplexity API**: https://docs.perplexity.ai/
- **Supabase Project**: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm
- **Guía completa**: `sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md`

---

## Resumen de Costos Esperados

| Operación | Costo |
|-----------|-------|
| Chat simple | ~$0.0003 |
| Análisis visual (3 imgs) | ~$0.012 |
| Generación fórmula | ~$0.025 |
| Búsqueda productos | ~$0.008 (primera vez) |
| Búsqueda productos | $0 (cache hit) |
| **Cliente completo** | **$0.051-0.059** |

---

**¿Dudas?** Consulta la guía completa en `sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md`
