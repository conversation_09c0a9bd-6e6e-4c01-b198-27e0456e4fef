# 🚀 Comandos de Deployment - Ejecución Paso a Paso

## Situación Actual

He intentado automatizar completamente el deployment, pero **Supabase CLI requiere autenticación interactiva** que no puedo completar desde aquí.

**Lo que necesitas hacer** (son solo 3 pasos simples):

---

## PASO 1: Ejecutar Migraciones SQL (2 minutos) ✅

### Opción A: Copiar y Pegar (MÁS RÁPIDO)

El SQL ya está copiado en tu clipboard. Solo necesitas:

1. Abrir: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
2. Hacer `Cmd+V` para pegar
3. Click en botón verde **"RUN"**

### Opción B: Desde Terminal

Si prefieres terminal, ejecuta:

```bash
cd /Users/<USER>/Salonier-AI

# Copiar SQL al clipboard nuevamente
cat supabase/migrations/EXECUTE_ALL_MIGRATIONS.sql | pbcopy

# Abrir dashboard
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new"

# Luego Cmd+V y RUN
```

---

## PASO 2: Obtener Access Token de Supabase (1 minuto) 🔑

Para desplegar la Edge Function desde terminal, necesitas un token personal:

```bash
# Abrir página de tokens
open "https://supabase.com/dashboard/account/tokens"
```

1. Click **"Generate new token"**
2. Nombre: `Salonier AI CLI`
3. **Copiar el token** generado

---

## PASO 3: Desplegar Edge Function (1 minuto) 📦

Una vez tengas el token:

```bash
cd /Users/<USER>/Salonier-AI

# Configurar el token (reemplaza TU_TOKEN_AQUI con el token que copiaste)
export SUPABASE_ACCESS_TOKEN='TU_TOKEN_AQUI'

# Desplegar la función
supabase functions deploy ai-proxy --project-ref guyxczavhtemwlrknqpm

# Verificar que se desplegó
supabase functions list --project-ref guyxczavhtemwlrknqpm
```

**Deberías ver:**
```
ai-proxy      ✓ deployed
```

---

## PASO 4: Configurar API Keys (3 minutos) 🔐

### 4.1 Obtener OpenAI API Key

```bash
open "https://platform.openai.com/api-keys"
```

1. Click **"Create new secret key"**
2. Nombre: `Salonier AI Production`
3. **Copiar la key** (empieza con `sk-proj-...`)

### 4.2 Obtener Perplexity API Key

```bash
open "https://www.perplexity.ai/settings/api"
```

1. Click **"Generate API Key"**
2. **Copiar la key** (empieza con `pplx-...`)

### 4.3 Agregar Secrets a Supabase

```bash
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions"
```

1. Scroll hasta **"Secrets"**
2. Click **"Add new secret"**:
   - Name: `OPENAI_API_KEY`
   - Value: `sk-proj-xxxxx` (el que copiaste)
   - Click "Add"
3. Click **"Add new secret"** nuevamente:
   - Name: `PERPLEXITY_API_KEY`
   - Value: `pplx-xxxxx` (el que copiaste)
   - Click "Add"

---

## 🧪 Verificar que Todo Funciona

### Test 1: Verificar Edge Function

```bash
cd /Users/<USER>/Salonier-AI

# Ver logs de la función
supabase functions serve ai-proxy --project-ref guyxczavhtemwlrknqpm

# En otra terminal, hacer un test (necesitas un auth token de la app)
# Este paso es opcional, mejor probar desde la app directamente
```

### Test 2: Iniciar la App

```bash
cd /Users/<USER>/Salonier-AI

# Iniciar
npm run start-web

# O si tienes bun
bun run start-web
```

Una vez iniciada:
1. Ir al tab **"Chat"**
2. Escribir: `"¿Qué es la colorimetría capilar?"`
3. Debería responder en ~3 segundos ✅

### Test 3: Verificar Costos

En Supabase SQL Editor:

```sql
SELECT
  use_case,
  provider,
  model,
  cost_usd,
  latency_ms,
  created_at
FROM ai_usage_log
ORDER BY created_at DESC
LIMIT 10;
```

Deberías ver tus requests con los costos.

---

## 📋 Resumen de Comandos (Copy-Paste Ready)

Si quieres hacerlo todo desde terminal seguido:

```bash
# 1. Navegar al proyecto
cd /Users/<USER>/Salonier-AI

# 2. Copiar SQL
cat supabase/migrations/EXECUTE_ALL_MIGRATIONS.sql | pbcopy
echo "✅ SQL copiado - Abre SQL Editor y pega (Cmd+V)"

# 3. Abrir SQL Editor
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new"

# 4. ESPERAR - Ejecutar SQL en el dashboard
read -p "Presiona ENTER cuando hayas ejecutado el SQL en el dashboard..."

# 5. Abrir página de tokens
open "https://supabase.com/dashboard/account/tokens"

# 6. ESPERAR - Generar token
read -p "Genera un token y cópialo. Luego presiona ENTER..."
read -p "Pega tu token aquí: " SUPABASE_ACCESS_TOKEN
export SUPABASE_ACCESS_TOKEN

# 7. Desplegar Edge Function
supabase functions deploy ai-proxy --project-ref guyxczavhtemwlrknqpm

# 8. Verificar deployment
supabase functions list --project-ref guyxczavhtemwlrknqpm

# 9. Configurar API Keys
open "https://platform.openai.com/api-keys"
open "https://www.perplexity.ai/settings/api"
open "https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions"

echo "✅ Deployment casi completo!"
echo "Solo falta agregar los API keys de OpenAI y Perplexity en Supabase Secrets"
```

---

## ❓ Troubleshooting

### Error: "Project not found"
```bash
# Verificar que el project-ref es correcto
supabase projects list
```

### Error: "Invalid access token"
```bash
# Regenerar el token en el dashboard
open "https://supabase.com/dashboard/account/tokens"
# Copiar el nuevo token y exportar:
export SUPABASE_ACCESS_TOKEN='nuevo_token'
```

### Error: "Function already exists"
```bash
# No es un error real, la función se actualiza automáticamente
# Verificar con:
supabase functions list --project-ref guyxczavhtemwlrknqpm
```

---

## 🎯 Checklist Final

- [ ] Migraciones SQL ejecutadas en dashboard
- [ ] Access token de Supabase generado
- [ ] Edge Function `ai-proxy` desplegada
- [ ] OpenAI API Key obtenida
- [ ] Perplexity API Key obtenida
- [ ] Ambas keys agregadas como Secrets en Supabase
- [ ] App testeada y funcionando
- [ ] Costos verificados en `ai_usage_log`

---

## 📚 Referencias

- **SQL a ejecutar**: `supabase/migrations/EXECUTE_ALL_MIGRATIONS.sql`
- **Función a desplegar**: `supabase/functions/ai-proxy/index.ts`
- **Docs completas**: `DEPLOYMENT_INSTRUCTIONS.md`
- **Estado**: `DEPLOYMENT_STATUS.md`

---

**¿Listo?** Copia y pega los comandos de la sección "Resumen de Comandos" en tu terminal.
