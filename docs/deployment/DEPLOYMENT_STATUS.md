# 🚀 Estado del Deployment - Migración OpenAI + Perplexity

**Última actualización**: 2025-10-21 20:50
**Rama activa**: `feature/openai-perplexity-migration`

---

## ✅ Completado Automáticamente

### 1. Implementación del Código
- [x] Edge Function `ai-proxy` creada (`supabase/functions/ai-proxy/index.ts`)
- [x] Cliente AI reescrito (`lib/ai-client.ts`)
- [x] Procesador de imágenes (`lib/imageProcessor.ts`)
- [x] Integración en Chat (`app/(tabs)/chat.tsx`)
- [x] Integración en Step 1 (`app/formula/step1.tsx`)
- [x] Integración en Step 2 (`app/formula/step2.tsx`)
- [x] Integración en Step 5 (`app/formula/step5.tsx`)
- [x] 4 Migraciones SQL creadas
- [x] Documentación completa
- [x] Commits creados (2 commits)

### 2. Archivos de Ayuda Creados
- [x] `DEPLOYMENT_INSTRUCTIONS.md` - Guía paso a paso completa
- [x] `EXECUTE_ALL_MIGRATIONS.sql` - SQL consolidado para ejecutar
- [x] Documentación de sesión actualizada

### 3. Navegador Preparado
He abierto automáticamente en tu navegador:
- ✅ Supabase SQL Editor (para ejecutar migraciones)
- ✅ OpenAI API Keys (para obtener key)
- ✅ Perplexity API Settings (para obtener key)
- ✅ Supabase Secrets Configuration (para agregar keys)
- ✅ Supabase Functions (para deployment)

### 4. SQL Copiado
- ✅ El contenido de `EXECUTE_ALL_MIGRATIONS.sql` está en tu clipboard
- Puedes hacer `Cmd+V` directamente en Supabase SQL Editor

---

## ⏳ Pendiente (Pasos Manuales Requeridos)

### PASO 1: Ejecutar Migraciones SQL (3 minutos)
**Ya está abierto en tu navegador**: Supabase SQL Editor

1. En el editor SQL que se abrió, hacer `Cmd+V` (el SQL ya está en clipboard)
2. Click en botón verde **"RUN"** (esquina inferior derecha)
3. Esperar confirmación de éxito

**Verificación**:
- Ir a Storage → Buckets → Debe existir `hair-images-temp`
- Ir a Table Editor → Deben existir: `ai_usage_log`, `rate_limits`, `product_cache`

---

### PASO 2: Obtener y Configurar API Keys (5 minutos)

#### 2.1 OpenAI
**Ya está abierto en tu navegador**: https://platform.openai.com/api-keys

1. Click "Create new secret key"
2. Nombre: `Salonier AI Production`
3. **COPIAR LA KEY** (solo se muestra una vez)
4. Formato: `sk-proj-xxxxxxxxxxxxx`

#### 2.2 Perplexity
**Ya está abierto en tu navegador**: https://www.perplexity.ai/settings/api

1. Click "Generate API Key"
2. **COPIAR LA KEY**
3. Formato: `pplx-xxxxxxxxxxxxx`

#### 2.3 Agregar a Supabase Secrets
**Ya está abierto en tu navegador**: Supabase Settings → Functions

1. Scroll hasta sección **"Secrets"**
2. Click **"Add new secret"**
3. Agregar:
   - Name: `OPENAI_API_KEY`
   - Value: `sk-proj-...` (la que copiaste de OpenAI)
   - Click "Add"
4. Repetir:
   - Name: `PERPLEXITY_API_KEY`
   - Value: `pplx-...` (la que copiaste de Perplexity)
   - Click "Add"

**Verificación**: Deberías ver 2 secrets en la lista

---

### PASO 3: Desplegar Edge Function (3 minutos)

**Ya está abierto en tu navegador**: Supabase Functions

#### Opción A: Via Dashboard (RECOMENDADO)
1. Click botón **"Create a new function"**
2. En "Import from local filesystem":
   - Click "Choose file"
   - Seleccionar: `/Users/<USER>/Salonier-AI/supabase/functions/ai-proxy/index.ts`
3. Name: `ai-proxy` (debería autocompletarse)
4. Click **"Deploy function"**
5. Esperar ~30 segundos

**Verificación**: Deberías ver `ai-proxy` con estado verde "Active"

#### Opción B: Via CLI (si prefieres terminal)
```bash
cd /Users/<USER>/Salonier-AI

# Login (abrirá navegador)
supabase login

# Deploy
supabase functions deploy ai-proxy

# Verificar
supabase functions list
```

---

### PASO 4 (OPCIONAL): Storage Lifecycle (2 minutos)

1. Ir a: Storage → Buckets → `hair-images-temp` → Settings
2. Scroll a "Object lifecycle rules"
3. Click "Add rule"
4. Configurar:
   - Name: `Auto-delete after 24h`
   - Delete objects older than: `1 day`
5. Click "Save"

---

## 🧪 Testing

### Test Rápido desde App

```bash
# Opción 1: Con npm
npm run start-web

# Opción 2: Con bun (si está instalado)
bun run start-web
```

Una vez iniciada la app:
1. Ir a tab "Chat"
2. Escribir: "¿Qué es el tono ceniza en coloración?"
3. Debería responder en ~3 segundos

### Verificar Costos

Después de usar la app, ejecutar en Supabase SQL Editor:

```sql
SELECT
  use_case,
  provider,
  model,
  cost_usd,
  latency_ms,
  created_at
FROM ai_usage_log
ORDER BY created_at DESC
LIMIT 10;
```

---

## 📊 Archivos Importantes

| Archivo | Propósito |
|---------|-----------|
| `DEPLOYMENT_INSTRUCTIONS.md` | Guía completa paso a paso |
| `EXECUTE_ALL_MIGRATIONS.sql` | SQL para ejecutar todas las migraciones |
| `sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md` | Documentación técnica detallada |
| `supabase/functions/ai-proxy/index.ts` | Edge Function principal |
| `lib/ai-client.ts` | Cliente AI del frontend |

---

## 🔗 Links Rápidos

- [SQL Editor](https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new)
- [Secrets Config](https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions)
- [Functions Dashboard](https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions)
- [Storage Buckets](https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/storage/buckets)
- [OpenAI API Keys](https://platform.openai.com/api-keys)
- [Perplexity API](https://www.perplexity.ai/settings/api)

---

## 🎯 Siguiente Paso

**👉 Empieza con PASO 1**: Pega el SQL en Supabase SQL Editor (ya está en clipboard) y haz click en RUN.

El SQL ya está copiado en tu clipboard, solo necesitas hacer `Cmd+V` en el editor que se abrió.

---

## ✅ Checklist de Deployment

Marca con `x` cuando completes cada paso:

```
[ ] PASO 1: Migraciones SQL ejecutadas
[ ] PASO 2: API Keys obtenidas y configuradas en Supabase
[ ] PASO 3: Edge Function desplegada
[ ] PASO 4: (Opcional) Storage lifecycle configurado
[ ] PASO 5: App testeada y funcionando
[ ] PASO 6: Costos verificados en ai_usage_log
```

---

**¿Tienes dudas?** Consulta `DEPLOYMENT_INSTRUCTIONS.md` para más detalles o troubleshooting.
