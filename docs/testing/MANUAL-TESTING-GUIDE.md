# Guía de Testing Manual - Sistema de Verificación de Proporciones

**Última actualización**: 2025-10-22
**Rama**: `testing/formula-edge-function-validation`
**Propósito**: Verificar que el sistema de verificación inteligente de proporciones funciona correctamente antes del merge

---

## Pre-requisitos

- ✅ App instalada localmente
- ✅ Variables de entorno configuradas (`.env.local`)
- ✅ Navegador con DevTools (Chrome/Firefox/Safari)
- ✅ Conexión a internet (Perplexity necesita consultar en tiempo real)

---

## Test 1: Wella Color Fresh CREATE (El Caso Crítico) 🔴

**Por qué este test es crítico**: Este fue el producto que generaba error "1:2 con oxidante" cuando NO requiere oxidante.

### Pasos

1. **Iniciar la app**:
   ```bash
   npm run start-web
   ```

2. **Esperar a que abra el navegador** (usualmente `http://localhost:8081`)

3. **Abrir DevTools del navegador**:
   - Chrome/Firefox: `F12` o `Cmd+Option+I` (Mac)
   - Ir a la pestaña "Console"

4. **Navegar en la app**:
   - Click en tab "Clients"
   - Click en "New Client" o seleccionar uno existente
   - Click en "Create Formula"

5. **Completar Step 0** (Client Selection):
   - Seleccionar o confirmar cliente
   - Click "Continue"

6. **Completar Step 1** (Current Color):
   - Subir foto o usar mock data
   - Completar análisis de color actual
   - Click "Continue"

7. **Completar Step 2** (Desired Color):
   - Subir foto de color deseado
   - Nivel deseado: cualquier nivel
   - Click "Continue"

8. **Completar Step 3** (Safety Checklist):
   - Marcar/desmarcar según preferencia
   - Click "Continue"

9. **Completar Step 4** (Brand Selection):
   - 🎯 **IMPORTANTE**: Seleccionar "Wella Professionals"
   - 🎯 **IMPORTANTE**: Seleccionar línea "Color Fresh CREATE"
   - Click "Generate Formula"

10. **Observar en DevTools Console**:
    ```
    [Step5] Verifying mixing ratios...
    [Step5] Mixing ratio verified for Wella Professionals Color Fresh CREATE
    [Step5] Generating base formula...
    [Step5] Formula enriched with product info
    ```

11. **Verificar la fórmula generada**:

    ✅ **DEBE incluir**:
    - "Aplicación directa"
    - "NO requiere oxidante" (o similar)
    - "Semi-permanente"
    - Tiempo de procesamiento (20-30 min)

    ❌ **NO DEBE incluir**:
    - "1:2 con oxidante"
    - "Mezclar con developer"
    - "Activador"
    - "Oxidante 10vol/20vol/30vol/40vol"

### Resultado Esperado

✅ **APROBADO** si:
- La fórmula menciona claramente que NO requiere oxidante
- La fórmula indica aplicación directa
- NO menciona ningún tipo de developer o activador

❌ **FALLIDO** si:
- La fórmula menciona oxidante o developer
- La fórmula sugiere proporción 1:2 o similar
- El console muestra error de verificación

---

## Test 2: Wella Koleston Perfect (Permanente Estándar) 🟢

**Por qué este test es importante**: Verifica que productos permanentes estándar siguen usando proporciones correctas.

### Pasos

Repetir pasos 1-8 del Test 1, pero en Step 4:
- 🎯 Seleccionar "Wella Professionals"
- 🎯 Seleccionar línea "Koleston Perfect"

### Verificar la fórmula generada

✅ **DEBE incluir**:
- "Proporción 1:1"
- "60ml color + 60ml oxidante" (o similar)
- "Welloxon Perfect Developer"
- Volumen de oxidante (10vol, 20vol, 30vol según caso)
- "Permanente"

❌ **NO DEBE incluir**:
- "Aplicación directa sin oxidante"
- Proporciones incorrectas (1:3, 2:1, etc)

### Resultado Esperado

✅ **APROBADO** si:
- La fórmula menciona proporción 1:1
- Especifica volumen de oxidante apropiado
- Identifica correctamente como permanente

---

## Test 3: L'Oréal INOA (Permanente Sin Amoníaco) 🟡

**Por qué este test es importante**: INOA tiene particularidades (solo se mezcla con INOA Oil Developer).

### Pasos

Repetir pasos 1-8 del Test 1, pero en Step 4:
- 🎯 Seleccionar "L'Oréal Professionnel"
- 🎯 Seleccionar línea "INOA"

### Verificar la fórmula generada

✅ **DEBE incluir**:
- "Proporción 1:1"
- "INOA Oil Developer" (específicamente INOA, no otro)
- "60ml + 60ml" (o similar)
- Advertencia de usar SOLO INOA developer

### Resultado Esperado

✅ **APROBADO** si:
- La fórmula menciona proporción 1:1
- Especifica INOA Oil Developer (no solo "oxidante")
- Menciona que NO debe mezclarse con otros oxidantes

---

## Test 4: Marca Sin Especificar (Fallback) ⚪

**Por qué este test es importante**: Verifica que el fallback funciona si no hay marca específica.

### Pasos

Repetir pasos 1-8 del Test 1, pero en Step 4:
- 🎯 Seleccionar "Sin marca específica" o dejar en blanco

### Verificar la fórmula generada

✅ **DEBE incluir**:
- Reglas generales (permanentes 1:1, decoloradores 1:2)
- Advertencia de consultar manual técnico
- Especificación de que es información general

### Resultado Esperado

✅ **APROBADO** si:
- La fórmula usa conocimiento general razonable
- Advierte al usuario sobre verificar con fabricante
- No rompe el flujo

---

## Checklist de Verificación Post-Tests

Después de ejecutar los 4 tests, verifica:

### Logs del Sistema
- [ ] `[Step5] Verifying mixing ratios...` aparece en console
- [ ] `[Step5] Mixing ratio verified for...` aparece después
- [ ] NO aparece `[Step5] Mixing ratio verification failed` (excepto en Test 4)
- [ ] Tiempo de verificación es razonable (<5 segundos)

### Fórmulas Generadas
- [ ] Test 1 (Color Fresh): NO menciona oxidante ✅
- [ ] Test 2 (Koleston): Menciona 1:1 con oxidante ✅
- [ ] Test 3 (INOA): Menciona INOA Oil Developer específico ✅
- [ ] Test 4 (Sin marca): Incluye advertencia de verificar ✅

### Performance
- [ ] Tiempo total de generación: 8-12 segundos (aceptable)
- [ ] No hay errores de timeout
- [ ] La app no se congela durante verificación

### UX
- [ ] El loading "Generando Fórmula..." es claro
- [ ] El usuario no percibe demora excesiva
- [ ] La fórmula se renderiza correctamente

---

## Troubleshooting

### Problema: "Mixing ratio verification failed"

**Síntomas**: Console muestra warning de fallback

**Posibles causas**:
1. No hay conexión a internet
2. Perplexity API no responde
3. Variables de entorno incorrectas

**Solución**:
1. Verificar conexión a internet
2. Ver error específico en console
3. Verificar que Supabase Edge Function 'ai-proxy' está funcionando

### Problema: La fórmula sigue mencionando oxidante en Color Fresh

**Síntomas**: Color Fresh menciona "1:2 con developer"

**Posibles causas**:
1. La información verificada no llegó al system prompt
2. GPT-4 ignoró las instrucciones

**Solución**:
1. Verificar en console que `mixingRatioInfo` tiene contenido
2. Revisar que el system prompt incluye la sección "=== INFORMACIÓN TÉCNICA VERIFICADA ==="
3. Reportar el caso para ajustar el prompt

### Problema: Timeout en verificación

**Síntomas**: Demora >30 segundos sin respuesta

**Posibles causas**:
1. Perplexity sobrecargado
2. Query demasiado complejo

**Solución**:
1. Reducir `maxRetries` de 1 a 0 en `verifyMixingRatio()`
2. Simplificar el query
3. Considerar caché de verificaciones

---

## Métricas de Éxito

Para considerar el sistema **LISTO PARA MERGE**, debe cumplir:

1. **Test 1 (Color Fresh)**: 100% correcto ✅
2. **Test 2 (Koleston)**: 100% correcto ✅
3. **Test 3 (INOA)**: 100% correcto ✅
4. **Test 4 (Fallback)**: Funciona sin romper ✅
5. **Performance**: <12 segundos total ✅
6. **Sin errores**: 0 crashes durante tests ✅

---

## Próximos Pasos Después del Testing

### Si TODOS los tests pasan ✅

1. Hacer merge a `main`:
   ```bash
   git checkout main
   git merge testing/formula-edge-function-validation
   git push origin main
   ```

2. Monitorear primeras 50 formulaciones en producción

3. Recopilar feedback de usuarios

### Si ALGÚN test falla ❌

1. Documentar el fallo específico
2. Investigar causa raíz
3. Ajustar código en `step5.tsx`
4. Repetir testing
5. NO hacer merge hasta que todos pasen

---

## Notas Adicionales

### Limitaciones Conocidas

- **Perplexity dependency**: Si Perplexity falla, se usa fallback
- **Internet required**: Necesita conexión para verificar
- **Costo adicional**: +$0.001 por fórmula
- **Tiempo adicional**: +2-3 segundos

### Testing en Producción

Después del merge, monitorear:
- Tasa de éxito de `verifyMixingRatio()` (meta: >95%)
- Tiempo promedio de verificación (meta: <3s)
- Reportes de usuarios sobre proporciones incorrectas (meta: 0%)

---

**Última actualización**: 2025-10-22
**Versión del sistema**: 1.0
**Estado**: ✅ Listo para testing manual
