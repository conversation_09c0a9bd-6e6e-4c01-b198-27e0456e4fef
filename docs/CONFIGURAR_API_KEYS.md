# 🔐 Configuración de API Keys en Supabase

**Fecha**: 2025-10-22
**Estado**: ✅ Edge Function desplegada | ✅ Secrets configurados en Supabase

---

## ⚠️ AVISO DE SEGURIDAD

**Las API keys ya están configuradas como secrets en Supabase y NO deben estar en archivos de código.**

Este documento contenía API keys hardcodeadas que fueron removidas por razones de seguridad.

---

## ✅ Estado Actual

- ✅ Edge Function `ai-proxy` desplegada exitosamente
- ✅ Migraciones SQL ejecutadas
- ✅ API Keys configuradas como secrets en Supabase
- ✅ Scripts de configuración refactorizados para usar variables de entorno

---

## 🎯 Verificar Secrets Configurados

### Ver secrets en Supabase

1. **Dashboard de Supabase**:
   - Ve a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions
   - Scroll hasta "Secrets"
   - De<PERSON>ías ver listados (valores ocultos con `***`):
     - `OPENAI_API_KEY`
     - `PERPLEXITY_API_KEY`

2. **Desde CLI**:
   ```bash
   supabase secrets list --project-ref guyxczavhtemwlrknqpm
   ```

---

## 🔄 Rotar API Keys (Si es necesario)

Si necesitas actualizar las API keys:

### 1. Obtener nuevas keys

**OpenAI:**
- https://platform.openai.com/api-keys
- Click "Create new secret key"
- Copiar la key (empieza con `sk-proj-...`)

**Perplexity:**
- https://www.perplexity.ai/settings/api
- Click "Generate API Key"
- Copiar la key (empieza con `pplx-...`)

### 2. Actualizar en Supabase

**Opción A - Dashboard:**
1. Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/functions
2. En la sección "Secrets", editar o eliminar el secret existente
3. Crear nuevo secret con el mismo nombre y nuevo valor

**Opción B - CLI:**
```bash
# Configurar variables de entorno
export OPENAI_API_KEY='tu-nueva-key'
export PERPLEXITY_API_KEY='tu-nueva-key'

# Ejecutar script de configuración
./configure-secrets.sh
```

**NOTA:** El script `configure-secrets.sh` ha sido refactorizado para leer las keys desde variables de entorno, nunca tenerlas hardcodeadas.

---

## 🧪 Verificar que Todo Funciona

Una vez configurados los secrets:

### 1. Iniciar la App

```bash
cd /Users/<USER>/Salonier-AI
bun run start-web
```

### 2. Test Básico - Chat

1. Abrir la app en el navegador
2. Ir al tab **"Chat"**
3. Escribir: `¿Qué es la colorimetría capilar?`
4. **Esperado**: Respuesta en ~3 segundos ✅

### 3. Test Avanzado - Análisis Visual

1. Ir a **"Crear Fórmula"** → Step 0
2. Seleccionar un cliente de prueba
3. Step 1: Subir 2-3 fotos de cabello
4. Click **"Analizar"**
5. **Esperado**:
   - Análisis JSON generado
   - Campos poblados (roots, mids, ends)
   - Sin errores en console

### 4. Verificar Costos en Supabase

```sql
-- En Supabase SQL Editor
SELECT
  use_case,
  provider,
  model,
  cost_usd,
  latency_ms,
  created_at
FROM ai_usage_log
ORDER BY created_at DESC
LIMIT 10;
```

**Esperado**:
- Chat simple: ~$0.0003
- Análisis visual: ~$0.012
- Sin errores

---

## 🐛 Troubleshooting

### Error: "Missing Authorization header"

**Causa**: Usuario no autenticado en la app

**Solución**: Asegúrate de estar logueado en la app

### Error: "Invalid or expired token"

**Causa**: Token de Supabase Auth expirado

**Solución**: Re-login en la app

### Error: "Rate limit exceeded"

**Causa**: Superaste los límites (100/día o 20/hora)

**Solución**: Esperar o ajustar límites en Edge Function

### Error: OpenAI API dice "Invalid API Key"

**Causa**: Secret no configurado correctamente

**Solución**:
1. Verificar que copiaste la key completa (sin espacios)
2. Re-configurar el secret en dashboard
3. Reiniciar Edge Function si es necesario

---

## 📊 Costos Esperados

| Operación | Proveedor | Costo |
|-----------|-----------|-------|
| Chat simple | OpenAI GPT-4o-mini | $0.0003 |
| Análisis visual (3 imgs) | OpenAI GPT-4o Vision | $0.012 |
| Generación fórmula | OpenAI GPT-4o | $0.025 |
| Búsqueda productos (1ª vez) | Perplexity Sonar Pro | $0.008 |
| Búsqueda productos (cache) | Cache | $0 |
| **Cliente completo** | - | **$0.051-0.059** |

---

## 🎯 Próximos Pasos

Una vez verificado que todo funciona:

1. **Commit final**:
   ```bash
   git add .
   git commit -m "docs: Agregar guía de configuración de API keys"
   git push origin feature/openai-perplexity-migration
   ```

2. **Crear Pull Request** a `main`

3. **Merge** y deployment completo ✅

---

## 📚 Recursos

- **Edge Function URL**: https://guyxczavhtemwlrknqpm.supabase.co/functions/v1/ai-proxy
- **Dashboard Functions**: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions
- **SQL Editor**: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/sql/new
- **Documentación completa**: `sessions/2025-10-21-migration-openai-perplexity-implementation-guide.md`

---

**Estado**: 95% Completado | Solo falta configurar secrets en dashboard (5 minutos)

🚀 **Casi listo para producción!**
