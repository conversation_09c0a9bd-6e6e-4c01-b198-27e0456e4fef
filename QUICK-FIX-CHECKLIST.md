# Quick Fix Checklist - Salonier AI

**Generated:** 2025-10-29  
**Full Report:** TESTING-360-REPORT.md (528 lines)

---

## CRITICAL - FIX IMMEDIATELY (Today)

- [ ] **1. Add Error Boundary** → Wrap `app/_layout.tsx` root
  - File: `/Users/<USER>/Salonier-AI/app/_layout.tsx`
  - Create `components/ErrorBoundary.tsx`
  
- [ ] **2. Remove 640+ console.log** → Production data leak
  - Replace with logger that disables in production
  - Priority files: `lib/ai-client.ts`, all contexts
  
- [ ] **3. Fix IDOR Vulnerability** → Client access bypass
  - Files: `app/(app)/clients/[id].tsx`, `app/(app)/formula/step*.tsx`
  - Use `validateClientAccess()` from `lib/sanitize.ts`
  
- [ ] **4. Add null checks** → Formula crash risk
  - Files: `app/(app)/formula/step3.tsx:50`, `app/(app)/formula/step5.tsx`
  - Redirect to step0 if `selectedClient` is null

---

## HIGH PRIORITY - FIX THIS WEEK

### Testing (BLOCKING)
- [ ] **5. Setup Jest + Testing Library**
  - Add to `package.json`: jest, jest-expo, @testing-library/react-native
  - Create `jest.config.js`
  - Add scripts: `"test": "jest"`, `"test:watch": "jest --watch"`

- [ ] **6. Write Auth Tests**
  - File: `__tests__/auth.test.tsx`
  - Cover: signup, login, logout, session persistence

- [ ] **7. Write Formula Tests**
  - File: `__tests__/formula-workflow.test.tsx`
  - Cover: 6-step wizard, state persistence, validation

### Performance
- [ ] **8. Fix N+1 Query** → ChatContext slow startup
  - File: `contexts/ChatContext.tsx:132-161`
  - Use JOIN or batch query for messages

- [ ] **9. Add Client-Side Rate Limiting** → AI spam prevention
  - File: `lib/ai-client.ts`
  - Throttle: 1 request per 2 seconds

- [ ] **10. Fix Context Re-renders** → UI lag
  - Files: All context files
  - Wrap return values in `useMemo`, callbacks in `useCallback`

### Security
- [ ] **11. Add GDPR Revocation Flow**
  - File: `app/(app)/settings/privacy.tsx` (create)
  - Button: "Revoke Photo Consent"

- [ ] **12. Validate RLS Policies** → Data leak risk
  - Use Supabase MCP: `get_advisors({ type: 'security' })`
  - Review all 25 migration files with RLS

---

## MEDIUM PRIORITY - FIX THIS MONTH

- [ ] **13. Optimize FlatList** → Chat scroll lag
  - File: `app/(app)/(tabs)/chat.tsx:68`
  - Add: keyExtractor, getItemLayout, removeClippedSubviews

- [ ] **14. Standardize Error Handling** → Debugging
  - All contexts
  - Create `lib/error-handler.ts`

- [ ] **15. Add Loading States** → User feedback
  - Files: `app/(app)/formula/step5.tsx`, chat
  - Progress bar + estimated time

- [ ] **16. Add Accessibility Labels** → Screen readers
  - All TouchableOpacity, TextInput
  - Add: accessibilityLabel, accessibilityHint

- [ ] **17. Extract Duplicate Code** → DRY
  - Files: `lib/storage.ts:8-30`, `lib/ai-client.ts:42-64`
  - Extract `readImageAsBase64` to `lib/image-utils.ts`

- [ ] **18. Debounce AsyncStorage** → Performance
  - File: `contexts/FormulaContext.tsx:57-68`
  - Debounce saves: 500ms

- [ ] **19. Audit Bundle Size** → Slow startup
  - Run: `npx expo-bundle-stats`
  - Target: <5MB JS bundle

- [ ] **20. Add Code Splitting** → Faster startup
  - Use `React.lazy()` for formula screens

---

## TEST INFRASTRUCTURE SETUP (Detailed)

### Step 1: Install Dependencies
```bash
bun add -D jest jest-expo @testing-library/react-native @testing-library/jest-native
```

### Step 2: Create jest.config.js
```javascript
module.exports = {
  preset: 'jest-expo',
  transformIgnorePatterns: [
    'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg)'
  ],
  setupFilesAfterEnv: ['@testing-library/jest-native/extend-expect'],
  collectCoverageFrom: [
    'app/**/*.{ts,tsx}',
    'contexts/**/*.{ts,tsx}',
    'lib/**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
  ],
};
```

### Step 3: Add Scripts to package.json
```json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

### Step 4: Write First Test
```typescript
// __tests__/auth.test.tsx
import { renderHook, waitFor } from '@testing-library/react-native';
import { useAuth } from '@/contexts/AuthContext';

describe('AuthContext', () => {
  it('should initialize with no user', () => {
    const { result } = renderHook(() => useAuth());
    expect(result.current.user).toBeNull();
    expect(result.current.isAuthenticated).toBe(false);
  });
});
```

### Step 5: Run Tests
```bash
bun test
```

---

## SUCCESS METRICS

### Week 1
- [ ] 5 critical issues fixed
- [ ] Test infrastructure setup complete
- [ ] First 10 tests written (auth + formula)
- [ ] 0 production console.log statements
- [ ] RLS policies validated

### Week 2
- [ ] 8 high priority issues fixed
- [ ] 30+ tests written
- [ ] 50%+ code coverage
- [ ] CI/CD pipeline configured

### Week 3
- [ ] 12 medium priority issues fixed
- [ ] 70%+ code coverage
- [ ] E2E tests for critical flows
- [ ] Performance benchmarks established

### Month 1
- [ ] All critical/high issues resolved
- [ ] 70%+ test coverage
- [ ] Security audit passed
- [ ] Performance targets met

---

## RESOURCES

- **Full Report:** TESTING-360-REPORT.md (528 lines)
- **File Paths:** All absolute paths provided in report
- **Testing Docs:** React Native Testing Library
- **Security Guide:** OWASP Mobile Top 10
- **Performance:** React DevTools Profiler

---

**Next Action:** Review TESTING-360-REPORT.md for detailed findings and implementation guidance.
