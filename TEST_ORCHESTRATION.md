# Test Plan: Intelligent AI Orchestration System

**Fecha**: 28 Octubre 2025
**Versión Edge Function**: 44
**Sistema**: Orquestación Inteligente (11 componentes)

---

## ✅ Test 1: Intent Detection

### 1.1 Query que NO requiere web (base knowledge)
**Input**: "¿Cómo funciona el tono ceniza en coloración?"

**Logs esperados**:
```
[Intent Detection] requires_current_info: false, confidence: 0.92
[Router] Decision: openai (reason: Color theory - base knowledge)
```

**Resultado**: ✅ / ❌

---

### 1.2 Query que SÍ requiere web (producto actual)
**Input**: "¿Cuáles son los nombres de Wella Koleston Perfect 2025?"

**Logs esperados**:
```
[Intent Detection] requires_current_info: true, entities: ["brand", "catalog", "date"]
[Router] Decision: perplexity (reason: Product catalog lookup required)
```

**Resultado**: ✅ / ❌

---

## ✅ Test 2: Hybrid Mode (Formula + Verification)

### 2.1 Generar fórmula con marca whitelisted
**Input**:
- Brand: Schwarzkopf Professional
- Color actual: Castaño 6
- Color deseado: Rubio 9

**Logs esperados**:
```
[Formula Routing] Decision: hybrid
[Hybrid Executor] Formula generated with X products
[Product Extractor] Extracted X products from formula
[Product Extractor] Regex found Y products
[Product Extractor] NLP found Z products
[Hybrid Executor] Verifying batch 1 (5 products)...
[Hybrid Executor] Verification summary: A verified, B unverified
```

**Resultado**: ✅ / ❌

---

### 2.2 Self-Correction (productos no verificados)
**Si hay productos no verificados en Test 2.1**:

**Logs esperados**:
```
[Self-Correction] Attempting to correct X unverified products...
[Self-Correction] Successfully corrected Y products
[Self-Correction] Completed with Z total corrections in N attempts
```

**Resultado**: ✅ / ❌

---

## ✅ Test 3: Cache System

### 3.1 Product Search (primera vez)
**Input**: "Proporción de mezcla para L'Oréal INOA"

**Logs esperados**:
```
[AIClient] Cost: $0.005 (Perplexity API call)
Latency: 1000-3000ms
```

**Resultado**: ✅ / ❌

---

### 3.2 Product Search (cache hit)
**Input**: Repetir misma query dentro de 30 días

**Logs esperados**:
```
[AIClient] Cost: $0.0000 (cache hit)
Latency: <200ms
[Cache HIT] product_catalog:loreal_professionnel
```

**Resultado**: ✅ / ❌

---

## ✅ Test 4: Vision Analysis (Safety Rejection Fix)

### 4.1 Foto con persona visible
**Input**: Upload foto con rostro + cabello

**Logs esperados** (v44 con nuevo prompt):
```
[Vision gpt-4o][Attempt 1/2] SUCCESS - Valid response received
response_length: >1000 (análisis completo JSON)
NO debe aparecer: "Lo siento, no puedo ayudar"
```

**Logs NO deseados** (v43 bug):
```
response: "Lo siento, no puedo ayudar con el análisis de individuos"
[Vision Safety Rejection]
```

**Resultado**: ✅ / ❌

---

### 4.2 Prompt Caching
**Input**: Analizar 2da foto (mismo system prompt)

**Logs esperados**:
```
cached_tokens: >1000
completion_tokens: <1000
cost_reduction: ~60% (por cached tokens)
```

**Resultado**: ✅ / ❌

---

## ✅ Test 5: Brand Validation

### 5.1 Marca en whitelist
**Input**: Brand ID = "wella_professionals"

**Logs esperados**:
```
[Brand Validator] Approved domains for wella_professionals: ["wella.com", "wella.es", ...]
[Router] Decision: hybrid (needsVerification: true)
```

**Resultado**: ✅ / ❌

---

### 5.2 Marca NO en whitelist
**Input**: Brand ID = "unknown_brand_xyz"

**Logs esperados**:
```
[Brand Validator] Invalid brand ID: unknown_brand_xyz
[Router] Decision: openai (reason: Brand not whitelisted, skipping verification)
needsVerification: false
```

**Resultado**: ✅ / ❌

---

## ✅ Test 6: Fallback Strategy

### 6.1 Intent Detection timeout
**Simular**: Network issue o timeout en GPT-4o-mini

**Logs esperados**:
```
[Intent Detection] GPT-4o-mini failed after Xms: timeout
[Intent Detection] Using fallback regex (confidence: 0.6)
[Router] Decision: openai (reason: Low confidence intent - using OpenAI)
fallbackUsed: true
```

**Resultado**: ✅ / ❌

---

## 📊 Métricas de Éxito

### Performance
- [ ] Intent Detection: <3s (95% queries)
- [ ] Cache Hit Rate: >50% (después de 1 semana uso)
- [ ] Vision Analysis: Sin safety rejections (<1% false positives)

### Costos
- [ ] Intent Detection: <$0.0002 per query (GPT-4o-mini)
- [ ] Product Search cache hit: $0.00 (vs $0.005 miss)
- [ ] Formula hybrid mode: <$0.05 per formula

### Accuracy
- [ ] Intent Classification: >85% accuracy
- [ ] Product Verification: >90% valid products detected
- [ ] Self-Correction: >70% unverified products fixed

---

## 🐛 Bugs Conocidos

### ❌ Safety Rejection (FIXED en v44)
**Versión**: v43
**Problema**: Vision analysis rechazaba fotos con personas
**Fix**: Prompt mejorado en v44 (líneas 475-502)
**Status**: ✅ RESUELTO

---

## 📝 Notas de Testing

**Fecha**: ___________
**Tester**: ___________
**Versión**: ___________

**Observaciones**:


**Issues encontrados**:


**Mejoras sugeridas**:


