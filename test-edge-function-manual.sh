#!/bin/bash

# Manual Test Script for AI Proxy Edge Function v45
# Tests intelligent orchestration system components

SUPABASE_URL="https://guyxczavhtemwlrknqpm.functions.supabase.co"
ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Imd1eXhjemF2aHRlbXdscmtucXBtIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NjEwNDg3ODYsImV4cCI6MjA3NjYyNDc4Nn0.fs6bWg2F-o54KuNUEzsCHMkAuE-as_FILYTIqqgrAng"

# ⚠️ IMPORTANTE: Este script requiere un ACCESS_TOKEN de usuario autenticado
# No podemos obtenerlo sin login interactivo en la app

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🧪 MANUAL TEST GUIDE: AI Proxy Edge Function v45"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "⚠️  Este edge function requiere autenticación de usuario."
echo "⚠️  Debes ejecutar estas pruebas DESDE LA APP (Expo) para obtener el access_token."
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📋 TESTS A REALIZAR EN LA APP"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "1️⃣  TEST INTENT DETECTION (Base Knowledge)"
echo "   - Abre el chat en la app"
echo "   - Pregunta: '¿Cómo funciona el tono ceniza en coloración?'"
echo "   - Verifica en logs de Supabase:"
echo "     ✅ [Intent Detection] requires_current_info: false"
echo "     ✅ [Router] Decision: openai"
echo ""
echo "2️⃣  TEST INTENT DETECTION (Current Info)"
echo "   - Pregunta: '¿Cuáles son los productos de Wella Koleston 2025?'"
echo "   - Verifica en logs:"
echo "     ✅ [Intent Detection] requires_current_info: true"
echo "     ✅ [Chat Routing] Decision: perplexity"
echo "     ✅ Respuesta con información actualizada (NO dice 'mi conocimiento está actualizado solo hasta...')"
echo ""
echo "3️⃣  TEST VISION ANALYSIS (No Safety Rejection)"
echo "   - Ve a Formula > Step 1 (Current Color)"
echo "   - Sube una foto con cabello + persona visible"
echo "   - Verifica en logs:"
echo "     ✅ [Vision gpt-4o][Attempt 1/2] SUCCESS"
echo "     ✅ response_length: >1000"
echo "     ❌ NO debe aparecer: 'Lo siento, no puedo ayudar con el análisis de individuos'"
echo ""
echo "4️⃣  TEST HYBRID MODE (Formula Generation + Verification)"
echo "   - Ve a Formula > Step 4 (Brand/Line Selection)"
echo "   - Selecciona: Schwarzkopf Professional > IGORA Royal"
echo "   - Completa hasta Step 5 (Formula Generation)"
echo "   - Verifica en logs:"
echo "     ✅ [Router] Decision: hybrid"
echo "     ✅ [Hybrid Executor] Formula generated"
echo "     ✅ [Product Extractor] Extracted X products"
echo "     ✅ [Hybrid Executor] Verifying batch"
echo ""
echo "5️⃣  TEST BRAND VALIDATION"
echo "   - Prueba con marca en whitelist: Wella Professionals"
echo "   - Verifica en logs:"
echo "     ✅ [Brand Validator] Approved domains for wella_professionals"
echo "     ✅ needsVerification: true"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "📊 CÓMO VER LOGS DETALLADOS"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "Opción 1: Supabase Dashboard (Recomendado)"
echo "  1. Ve a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/functions/ai-proxy/logs"
echo "  2. Filtra por 'Recent' (últimas 24 horas)"
echo "  3. Expande cada request para ver console.log detallados"
echo ""
echo "Opción 2: CLI (solo resumen)"
echo "  SUPABASE_ACCESS_TOKEN=sbp_xxx supabase functions list --project-ref guyxczavhtemwlrknqpm"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ EVIDENCIA DE DEPLOYMENT EXITOSO"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "✅ v43: Intelligent Orchestration System (11 components, 122KB)"
echo "✅ v44: Vision Analysis Fix (improved system prompt)"
echo "✅ v45: Chat Routing Fix (intelligent routing for chat)"
echo ""
echo "Versión actual: 45 (ACTIVE)"
echo "Última actualización: 2025-10-28 23:08:23 UTC"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
