# AI Orchestration System - Deployment Instructions

**Status**: Ready for deployment (code complete, DB migrated, scripts prepared)

---

## Quick Start (3 Steps)

### 1. Authenticate with Supa<PERSON>

```bash
# Option A - Browser (recommended)
supabase login

# Option B - Access Token
supabase login --token YOUR_TOKEN
# Get token: https://supabase.com/dashboard/account/tokens
```

### 2. Deploy Edge Function

```bash
./deploy-ai-proxy.sh
```

### 3. Test Deployment

```bash
./test-ai-orchestration.sh
```

---

## What Was Completed

### ✅ Phase 1-4: Intelligent AI Orchestration (COMPLETE)

**Phase 1: Intelligent Router** ✅
- `intelligent-router.ts` - Routes requests to OpenAI, Perplexity, or Hybrid mode
- `intent-detector.ts` - GPT-4o-mini intent detection with fallback
- Rules: Images→OpenAI, Web queries→Perplexity, Brand formulas→Hybrid

**Phase 2: Hybrid Execution** ✅
- `hybrid-executor.ts` - Generate formulas + verify products
- `product-extractor.ts` - Regex + NLP extraction
- `brand-validator.ts` - Whitelist validation with 44 brands

**Phase 3: Self-Correction** ✅
- `self-correction.ts` - Auto-corrects unverified products
- Max 2 retry attempts with product replacements

**Phase 4: Intelligent Cache** ✅
- `cache-manager.ts` - 3-level cache system
  - Product Catalog (90 days TTL)
  - Mixing Ratios (180 days TTL)
  - Formulas (30 days TTL)
- `intent_cache` table - Intent detection cache (30 days TTL)
- `ai_cache` table - General AI cache with hit tracking

### ✅ Database Migrations

- `20251028205200_create_intent_cache.sql` ✅
- `20251028_create_ai_cache_table.sql` ✅
- Tables verified in production DB

### ✅ Data Files

- `brands.json` - 44 professional brands (1129 lines, 27.2 KB)
- Copied to: `supabase/functions/ai-proxy/data/brands.json`

### ✅ Environment Secrets

- `OPENAI_API_KEY` ✅
- `PERPLEXITY_API_KEY` ✅ (verified in screenshot)
- `SUPABASE_URL` ✅
- `SUPABASE_SERVICE_ROLE_KEY` ✅

---

## Deployment Workflow

### Pre-Deployment Checks (Automated in `deploy-ai-proxy.sh`)

```bash
# 1. Verify brands.json (1129 lines)
ls -la supabase/functions/ai-proxy/data/brands.json

# 2. Verify all TypeScript files exist
# - index.ts, intelligent-router.ts, hybrid-executor.ts
# - self-correction.ts, product-extractor.ts, intent-detector.ts
# - brand-validator.ts, cache-manager.ts, types.ts, prompts.ts

# 3. Check Supabase authentication
supabase functions list --project-ref guyxczavhtemwlrknqpm
```

### Deployment Command

```bash
./deploy-ai-proxy.sh
```

**Expected Output:**
```
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🚀 Deploying AI Proxy Edge Function to Supabase
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

📋 Step 1: Pre-deployment checks...
   ✅ brands.json verified: 1129 lines
   ✅ All required files present

🔐 Step 2: Checking Supabase authentication...
   ✅ Authenticated with Supabase CLI

🚀 Step 3: Deploying edge function...
Deploying function ai-proxy (project ref: guyxczavhtemwlrknqpm)
Bundled ai-proxy size: 15.2 KB
Deployed ai-proxy successfully: v2

✅ Step 4: Verifying deployment...

Current edge functions:
┌──────────┬────────┬─────────┐
│ NAME     │ STATUS │ VERSION │
├──────────┼────────┼─────────┤
│ ai-proxy │ ACTIVE │ v2      │
└──────────┴────────┴─────────┘

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
✅ DEPLOYMENT COMPLETE!
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
```

---

## Testing Workflow

### Automated Testing Script

```bash
./test-ai-orchestration.sh
```

This script will:
1. Check edge function status
2. Start live log monitoring (background)
3. Display manual testing instructions
4. Analyze logs after testing
5. Generate summary report

### Manual Tests

#### Test 1: Hybrid Mode (Formula with Brand)

1. Start app: `bun run start-web`
2. Navigate: Formula → Step 1 → Step 2 → Step 4
3. Select: **L'Oréal Professionnel** + **INOA**
4. Step 5 → Generate formula

**Expected Logs:**
```
[Formula Routing] Decision: hybrid (confidence: 0.95, reason: Formula requires real-time product verification)
[Hybrid Executor] Generating formula for L'Oréal Professionnel INOA...
[Hybrid Executor] Formula generated (1234 chars)
[Hybrid Executor] Extracted 5 products, verifying...
[Hybrid Executor] Verification completed in 2500ms
[Hybrid Executor] Formula generated with 5 products (3 verified, 2 unverified)
[Self-Correction] Attempting to correct 2 unverified products...
[Self-Correction] 2 corrections made in 1 attempts
```

#### Test 2: OpenAI Mode (Formula without Brand)

1. Formula → Step 4 → **Leave brand empty**
2. Step 5 → Generate formula

**Expected Logs:**
```
[Formula Routing] Decision: openai (confidence: 0.8, reason: No brand specified)
```

#### Test 3: Intent Cache

1. Chat → Ask: **"Dame la lista de productos de Wella Koleston"**
2. **First time:**
   ```
   [Intent Detection] GPT-4o-mini success - 245ms (confidence: 0.98)
   ```
3. Ask **exactly the same** question again
4. **Second time:**
   ```
   [Intent Detection] Cache hit - 12ms
   ```

### Log Monitoring Commands

```bash
# Live tail
tail -f ai-proxy-logs.txt

# Search for patterns
grep 'Hybrid Executor' ai-proxy-logs.txt
grep 'Self-Correction' ai-proxy-logs.txt
grep 'Cache HIT' ai-proxy-logs.txt

# Direct Supabase logs
supabase functions logs ai-proxy --project-ref guyxczavhtemwlrknqpm --tail
```

---

## Performance Validation

### Cache Statistics (After 10-20 Formulas)

Run in Supabase SQL Editor:

```sql
-- Intent cache stats
SELECT
  COUNT(*) as total_entries,
  AVG(access_count) as avg_hits,
  MAX(access_count) as max_hits
FROM intent_cache;

-- AI cache stats by type
SELECT
  cache_type,
  COUNT(*) as total_entries,
  AVG(hit_count) as avg_hits,
  MAX(hit_count) as max_hits
FROM ai_cache
GROUP BY cache_type;

-- Cache hit rate (last 100 entries)
SELECT
  cache_type,
  SUM(hit_count) as total_hits,
  COUNT(*) as total_entries,
  ROUND(AVG(hit_count), 2) as avg_hits_per_entry
FROM ai_cache
GROUP BY cache_type
ORDER BY total_hits DESC;
```

**Expected Results:**
- Intent cache: avg_hits > 1 (repetitive queries)
- Product catalog cache: Entries for popular brands (L'Oréal, Wella, etc.)
- Hit rate should increase over time

---

## Troubleshooting

### Error: brands.json not found

```bash
# Verify existence
ls -la supabase/functions/ai-proxy/data/brands.json

# Re-copy if missing
cp assets/data/brands.json supabase/functions/ai-proxy/data/brands.json

# Re-deploy
./deploy-ai-proxy.sh
```

### Error: PERPLEXITY_API_KEY undefined

Check Supabase Dashboard:
- https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/settings/secrets

If missing:
```bash
supabase secrets set PERPLEXITY_API_KEY=pplx-xxxxx --project-ref guyxczavhtemwlrknqpm
```

### Error: Perplexity API 401 Unauthorized

API key exists but is invalid:
1. Go to: https://www.perplexity.ai/settings/api
2. Generate new key
3. Update secret:
   ```bash
   supabase secrets set PERPLEXITY_API_KEY=nueva_key --project-ref guyxczavhtemwlrknqpm
   ```

### Logs Empty / No Messages

```bash
# Verify deployment
supabase functions list --project-ref guyxczavhtemwlrknqpm

# Check function version (should be recent)
supabase functions inspect ai-proxy --project-ref guyxczavhtemwlrknqpm

# Force re-deploy
./deploy-ai-proxy.sh
```

---

## Production Readiness Checklist

### Minimum Viable (Must Have)

- [ ] Edge function deployed without errors
- [ ] Fórmula with brand generates hybrid mode logs
- [ ] No crashes in the app
- [ ] PERPLEXITY_API_KEY is set

### Ideal (Production-Ready)

- [ ] Hybrid mode verifies products correctly
- [ ] Self-correction replaces unverified products
- [ ] Cache hit after 2nd identical query
- [ ] Performance < 10s for formulas with 5-8 products
- [ ] No errors in logs (check: `grep ERROR ai-proxy-logs.txt`)
- [ ] Intent cache has entries (check SQL query above)
- [ ] AI cache hit rate > 20% after 20 formulas

---

## Success Metrics

### Latency Targets

- Intent detection: < 3s (with fallback)
- Formula generation (OpenAI): < 10s
- Hybrid mode (with verification): < 15s
- Cache hit: < 100ms

### Quality Targets

- Product verification accuracy: > 80%
- Self-correction success rate: > 70%
- Cache hit rate (after warmup): > 30%

### Cost Optimization

- Intent cache: ~$0.000015 per query (GPT-4o-mini)
- Cached intents: $0 (free)
- Product verification: ~$0.005 per product (Perplexity)
- Cached products: $0 (free)

**Expected savings**: 40-60% reduction in API costs after cache warmup

---

## Next Steps After Deployment

1. **Monitor for 24 hours**
   - Check logs regularly: `tail -f ai-proxy-logs.txt`
   - Look for errors or unexpected behavior

2. **Collect metrics**
   - Cache hit rates
   - Latency measurements
   - Error rates

3. **Optimize if needed**
   - Adjust cache TTLs
   - Tune confidence thresholds
   - Add more brands to whitelist

4. **Document learnings**
   - Update `sessions/2025-10-28-intelligent-ai-orchestration-complete.md`
   - Add any issues found to troubleshooting guide

---

## Support

**Documentation:**
- Full implementation: `sessions/2025-10-28-intelligent-ai-orchestration-complete.md`
- Code review: Architecture (9.2/10), mentioned in session docs

**Key Files:**
- Main entry: `supabase/functions/ai-proxy/index.ts:628-720`
- Router: `supabase/functions/ai-proxy/intelligent-router.ts`
- Hybrid executor: `supabase/functions/ai-proxy/hybrid-executor.ts`
- Self-correction: `supabase/functions/ai-proxy/self-correction.ts`

**Project Info:**
- Project ID: `guyxczavhtemwlrknqpm`
- Region: EU West (Paris)
- Database: PostgreSQL with RLS enabled
