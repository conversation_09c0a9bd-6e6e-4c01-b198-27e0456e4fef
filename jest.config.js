module.exports = {
  preset: 'jest-expo',

  // Platform-specific test matching
  testMatch: [
    '**/__tests__/**/*.(test|spec).[jt]s?(x)',
    '**/?(*.)+(spec|test).[jt]s?(x)',
  ],

  // Module path aliases (match tsconfig.json)
  moduleNameMapper: {
    '^@/(.*)$': '<rootDir>/$1',
  },

  // Transform files with babel-jest
  transformIgnorePatterns: [
    'node_modules/(?!((jest-)?react-native|@react-native(-community)?)|expo(nent)?|@expo(nent)?/.*|@expo-google-fonts/.*|react-navigation|@react-navigation/.*|@unimodules/.*|unimodules|sentry-expo|native-base|react-native-svg|@supabase/.*|@nkzw/.*)',
  ],

  // Setup files
  setupFilesAfterEnv: [
    '<rootDir>/jest.setup.js',
    '@testing-library/jest-native/extend-expect',
  ],

  // Coverage configuration
  collectCoverageFrom: [
    '**/*.{ts,tsx}',
    '!**/*.d.ts',
    '!**/node_modules/**',
    '!**/.expo/**',
    '!**/coverage/**',
    '!**/jest.config.js',
    '!**/babel.config.js',
    '!**/metro.config.js',
    '!**/app.config.js',
  ],

  coverageThreshold: {
    global: {
      statements: 70,
      branches: 65,
      functions: 70,
      lines: 70,
    },
    // Critical files require higher coverage
    './contexts/**/*.{ts,tsx}': {
      statements: 90,
      branches: 85,
      functions: 90,
      lines: 90,
    },
    './lib/sanitize.ts': {
      statements: 95,
      branches: 90,
      functions: 95,
      lines: 95,
    },
  },

  // Test environment
  testEnvironment: 'jest-environment-node',

  // Clear mocks between tests
  clearMocks: true,
  resetMocks: true,
  restoreMocks: true,

  // Verbose output
  verbose: true,

  // Max workers (optimize for CI)
  maxWorkers: '50%',
};
