/**
 * Test REAL de verificación de proporciones con usuario autenticado
 */

import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Cargar env
const envPath = join(__dirname, '.env.local');
const envContent = readFileSync(envPath, 'utf-8');
const env = {};
envContent.split('\n').forEach(line => {
  const [key, ...value] = line.split('=');
  if (key && value.length) {
    env[key.trim()] = value.join('=').trim();
  }
});

// Cargar token de usuario de prueba
const token = readFileSync(join(__dirname, '.test-token'), 'utf-8').trim();

const supabaseUrl = env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('\n🧪 TEST REAL DE VERIFICACIÓN DE PROPORCIONES\n');
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Token: ${token.substring(0, 30)}...\n`);

// Test de verificación
async function testVerification(brand, productLine, expectedChecks) {
  console.log(`\n🔍 TEST: ${brand}${productLine ? ` - ${productLine}` : ''}`);
  console.log('═'.repeat(70));

  const query = `Proporción de mezcla EXACTA para ${brand}${productLine ? ` ${productLine}` : ''} según manual técnico profesional oficial 2025.

IMPORTANTE: Responde en formato estructurado:
1. Proporción estándar (ej: 1:1, 1:2, o "NO REQUIERE OXIDANTE")
2. Tipo de producto (permanente/semi-permanente/demi-permanente/decolorador/toner)
3. Volúmenes de oxidante compatibles (si aplica)
4. Tiempo de procesamiento típico
5. Instrucciones críticas o advertencias especiales

Solo información verificada de fuentes oficiales del fabricante.`;

  const body = {
    useCase: 'product_search',
    prompt: query,
    brand: brand,
    productLine: productLine || ''
  };

  const startTime = Date.now();

  try {
    console.log('📡 Llamando a Edge Function con autenticación...');

    const response = await fetch(`${supabaseUrl}/functions/v1/ai-proxy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${token}`, // ✅ Ahora incluimos el token
      },
      body: JSON.stringify(body)
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);

    if (!response.ok) {
      const errorData = await response.json();
      console.log(`\n❌ Error ${response.status}: ${response.statusText}`);
      console.log('Respuesta:', JSON.stringify(errorData, null, 2));
      return { success: false, duration };
    }

    const data = await response.json();

    console.log(`✅ Respuesta recibida en ${duration}s!\n`);
    console.log('📋 RESULTADO:\n');
    console.log(data.text);
    console.log('\n');

    if (data.citations && data.citations.length > 0) {
      console.log('📚 Fuentes:');
      data.citations.slice(0, 3).forEach((cite, i) => {
        console.log(`  ${i + 1}. ${cite}`);
      });
      console.log('');
    }

    if (data.cost) {
      console.log(`💰 Costo: $${data.cost.toFixed(4)}`);
    }

    if (data.usage?.cached) {
      console.log(`⚡ Cache HIT - Respuesta desde cache`);
    }

    console.log('');

    // Validaciones
    if (expectedChecks) {
      console.log('✓ VALIDACIONES:');
      const lowerResult = data.text.toLowerCase();
      let allPassed = true;

      for (const [key, check] of Object.entries(expectedChecks)) {
        const passed = check.test(lowerResult);
        const status = passed ? '✅' : '❌';
        console.log(`  ${status} ${check.description}`);
        if (!passed) allPassed = false;
      }

      console.log('');
      console.log(allPassed ? '🎉 TODAS LAS VALIDACIONES PASARON' : '⚠️  ALGUNAS VALIDACIONES FALLARON');
      console.log('');

      return { success: allPassed, duration, result: data.text };
    }

    return { success: true, duration, result: data.text };

  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.error(`\n❌ Error después de ${duration}s:`, error.message);
    return { success: false, duration, error: error.message };
  }
}

// Tests
async function runAllTests() {
  console.log('Este es un TEST REAL con usuario autenticado');
  console.log('Llamará a Perplexity y obtendrá respuestas reales.\n');

  const results = [];

  // Test 1: Color Fresh CREATE (el crítico)
  console.log('─'.repeat(70) + '\n');
  const test1 = await testVerification(
    'Wella Professionals',
    'Color Fresh CREATE',
    {
      noOxidant: {
        description: 'Menciona que NO requiere oxidante',
        test: (text) => text.includes('no requiere oxidante') ||
                        text.includes('no oxidante') ||
                        text.includes('sin oxidante') ||
                        text.includes('no developer') ||
                        text.includes('aplicación directa')
      },
      semiPermanent: {
        description: 'Identifica como semi-permanente',
        test: (text) => text.includes('semi-permanente') || text.includes('semipermanente')
      }
    }
  );
  results.push({ name: 'Color Fresh CREATE', ...test1 });

  // Test 2: Koleston Perfect
  console.log('─'.repeat(70) + '\n');
  const test2 = await testVerification(
    'Wella Professionals',
    'Koleston Perfect',
    {
      ratio1to1: {
        description: 'Menciona proporción 1:1',
        test: (text) => text.includes('1:1')
      },
      requiresOxidant: {
        description: 'Menciona que requiere oxidante',
        test: (text) => text.includes('oxidante') || text.includes('developer')
      }
    }
  );
  results.push({ name: 'Koleston Perfect', ...test2 });

  // Test 3: INOA
  console.log('─'.repeat(70) + '\n');
  const test3 = await testVerification(
    'L\'Oréal Professionnel',
    'INOA',
    {
      ratio1to1: {
        description: 'Menciona proporción 1:1',
        test: (text) => text.includes('1:1')
      },
      inoaSpecific: {
        description: 'Menciona INOA Oil Developer o developer específico',
        test: (text) => text.includes('inoa') || text.includes('oil')
      }
    }
  );
  results.push({ name: 'INOA', ...test3 });

  // Resumen
  console.log('═'.repeat(70));
  console.log('RESUMEN DE TESTS');
  console.log('═'.repeat(70));
  console.log('');

  results.forEach(result => {
    const status = result.success ? '✅ APROBADO' : '❌ FALLIDO';
    console.log(`${result.name.padEnd(30)} ${status} (${result.duration}s)`);
  });

  const allPassed = results.every(r => r.success);
  const avgDuration = (results.reduce((sum, r) => sum + parseFloat(r.duration), 0) / results.length).toFixed(2);

  console.log('');
  console.log('─'.repeat(70));
  console.log(`Total: ${results.filter(r => r.success).length}/${results.length} aprobados`);
  console.log(`Tiempo promedio: ${avgDuration}s por verificación`);
  console.log('═'.repeat(70));
  console.log('');

  if (allPassed) {
    console.log('🎉 TODOS LOS TESTS APROBADOS');
    console.log('✅ Sistema de verificación funcionando correctamente');
    console.log('✅ Listo para merge a main');
    console.log('');
  } else {
    console.log('⚠️  ALGUNOS TESTS FALLARON');
    console.log('❌ Revisar resultados antes de merge');
    console.log('');
  }

  process.exit(allPassed ? 0 : 1);
}

runAllTests();
