/**
 * Crear usuario de prueba y obtener token
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Cargar env
const envPath = join(__dirname, '.env.local');
const envContent = readFileSync(envPath, 'utf-8');
const env = {};
envContent.split('\n').forEach(line => {
  const [key, ...value] = line.split('=');
  if (key && value.length) {
    env[key.trim()] = value.join('=').trim();
  }
});

const supabaseUrl = env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function createTestUser() {
  console.log('\n🔐 Creando usuario de prueba...\n');

  const testEmail = '<EMAIL>';
  const testPassword = 'TestPassword123!';

  // Intentar crear usuario
  const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
    email: testEmail,
    password: testPassword,
  });

  if (signUpError) {
    // Si ya existe, intentar login
    if (signUpError.message.includes('already registered')) {
      console.log('ℹ️  Usuario ya existe, intentando login...\n');

      const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
        email: testEmail,
        password: testPassword,
      });

      if (signInError) {
        console.error('❌ Error al hacer login:', signInError.message);
        return null;
      }

      console.log('✅ Login exitoso!');
      console.log(`📧 Email: ${testEmail}`);
      console.log(`🔑 Token: ${signInData.session.access_token.substring(0, 40)}...\n`);

      return signInData.session.access_token;
    } else {
      console.error('❌ Error al crear usuario:', signUpError.message);
      return null;
    }
  }

  console.log('✅ Usuario creado exitosamente!');
  console.log(`📧 Email: ${testEmail}`);

  if (signUpData.session) {
    console.log(`🔑 Token: ${signUpData.session.access_token.substring(0, 40)}...\n`);
    return signUpData.session.access_token;
  } else {
    console.log('⚠️  Usuario creado pero requiere confirmación de email');
    console.log('   Intenta hacer login después de confirmar el email\n');
    return null;
  }
}

// Ejecutar
createTestUser().then(async token => {
  if (token) {
    // Guardar token en archivo temporal
    const fs = await import('fs');
    fs.default.writeFileSync(join(__dirname, '.test-token'), token);
    console.log('💾 Token guardado en .test-token');
  }
  process.exit(token ? 0 : 1);
});
