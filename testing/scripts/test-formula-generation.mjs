/**
 * Test REAL de generación de fórmulas con usuario autenticado
 * Ejecuta los 6 casos de prueba documentados en sessions/2025-10-22-formula-testing-results.md
 */

import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Cargar env
const envPath = join(__dirname, '.env.local');
const envContent = readFileSync(envPath, 'utf-8');
const env = {};
envContent.split('\n').forEach(line => {
  const [key, ...value] = line.split('=');
  if (key && value.length) {
    env[key.trim()] = value.join('=').trim();
  }
});

// Cargar token de usuario de prueba
const token = readFileSync(join(__dirname, '.test-token'), 'utf-8').trim();

const supabaseUrl = env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

console.log('\n🧪 TEST REAL DE GENERACIÓN DE FÓRMULAS\n');
console.log(`Supabase URL: ${supabaseUrl}`);
console.log(`Token: ${token.substring(0, 30)}...\n`);

// Test de generación de fórmula
async function testFormulaGeneration(testCase) {
  console.log(`\n🔍 TEST ${testCase.id}: ${testCase.name}`);
  console.log('═'.repeat(70));
  console.log(`Categoría: ${testCase.category} | Dificultad: ${testCase.difficulty}`);
  console.log('─'.repeat(70));

  const body = {
    useCase: 'formula_generation',
    prompt: testCase.prompt,
    conversationContext: testCase.context || []
  };

  const startTime = Date.now();

  try {
    console.log('📡 Llamando a Edge Function con autenticación...');

    const response = await fetch(`${supabaseUrl}/functions/v1/ai-proxy`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': supabaseAnonKey,
        'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify(body)
    });

    const duration = ((Date.now() - startTime) / 1000).toFixed(2);

    if (!response.ok) {
      const errorData = await response.json();
      console.log(`\n❌ Error ${response.status}: ${response.statusText}`);
      console.log('Respuesta:', JSON.stringify(errorData, null, 2));
      return { success: false, duration };
    }

    const data = await response.json();

    console.log(`✅ Respuesta recibida en ${duration}s!\n`);

    if (data.cost) {
      console.log(`💰 Costo: $${data.cost.toFixed(4)}`);
    }

    // Validaciones
    console.log('\n✓ VALIDACIONES:');
    const lowerResult = data.text.toLowerCase();
    let allPassed = true;
    let score = 0;
    const maxScore = testCase.validations.length * 10;

    for (const validation of testCase.validations) {
      const passed = validation.test(lowerResult, data.text);
      const status = passed ? '✅' : '❌';
      console.log(`  ${status} ${validation.description}`);
      if (passed) score += 10;
      else allPassed = false;
    }

    const percentage = ((score / maxScore) * 100).toFixed(0);
    console.log('');
    console.log(`📊 Puntuación: ${score}/${maxScore} (${percentage}%)`);
    console.log('');

    if (allPassed) {
      console.log('🎉 TODAS LAS VALIDACIONES PASARON');
    } else {
      console.log('⚠️  ALGUNAS VALIDACIONES FALLARON');
    }

    console.log('');
    console.log('📋 FÓRMULA GENERADA:');
    console.log('─'.repeat(70));
    // Mostrar solo primeros 800 caracteres para no saturar
    const preview = data.text.length > 800 ? data.text.substring(0, 800) + '\n\n[...continuación omitida para brevedad...]' : data.text;
    console.log(preview);
    console.log('─'.repeat(70));
    console.log('');

    return { success: allPassed, duration, score, maxScore, result: data.text };

  } catch (error) {
    const duration = ((Date.now() - startTime) / 1000).toFixed(2);
    console.error(`\n❌ Error después de ${duration}s:`, error.message);
    return { success: false, duration, error: error.message };
  }
}

// Definir casos de prueba
const testCases = [
  {
    id: 'gc-001',
    name: 'Cobertura de Canas 30% (EASY)',
    category: 'Gray Coverage',
    difficulty: 'Easy',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Nivel de color base: 5 (castaño medio)
- Porcentaje de canas: 30%
- Ubicación de canas: Principalmente en sienes
- Condición del cabello: Virgen (nunca teñido)
- Textura: Media
- Porosidad: Normal

OBJETIVO DESEADO:
- Mantener el nivel 5 (castaño medio)
- Cobertura 100% de canas
- Resultado natural

PREFERENCIAS:
- Marca: Wella Professionals
- Línea de producto: Koleston Perfect

Proporciona una fórmula profesional completa con productos, proporciones, técnica de aplicación y tiempos.`,
    validations: [
      {
        description: 'Menciona Koleston Perfect (marca solicitada)',
        test: (text) => text.includes('koleston perfect') || text.includes('koleston')
      },
      {
        description: 'Especifica tono nivel 5 para cubrir canas',
        test: (text) => /5[\/\.]0|5[\/\.]00|nivel 5/i.test(text)
      },
      {
        description: 'Indica oxidante 20 volúmenes (apropiado para canas)',
        test: (text) => /20\s*vol|6\s*%/.test(text)
      },
      {
        description: 'Menciona proporción 1:1 (estándar Koleston)',
        test: (text) => text.includes('1:1')
      },
      {
        description: 'Especifica tiempo de procesamiento (30-40 min)',
        test: (text) => /3[05]-?4[05]\s*min|35\s*min|40\s*min/.test(text)
      }
    ]
  },
  {
    id: 'gc-002',
    name: 'Cobertura de Canas 70% Vítreo (MEDIUM)',
    category: 'Gray Coverage',
    difficulty: 'Medium',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Nivel de color base: 6 (rubio oscuro)
- Porcentaje de canas: 70%
- Tipo de canas: Vítreas (muy resistentes)
- Ubicación: Distribuidas uniformemente
- Condición del cabello: Previamente teñido hace 6 meses
- Textura: Gruesa
- Porosidad: Baja

OBJETIVO DESEADO:
- Mantener nivel 6 (rubio oscuro)
- Cobertura 100% de canas resistentes
- Tono cálido natural

PREFERENCIAS:
- Marca: L'Oréal Professionnel
- Línea de producto: INOA

Proporciona una fórmula profesional completa con productos, proporciones, técnica de aplicación y tiempos.`,
    validations: [
      {
        description: 'Menciona INOA (marca solicitada)',
        test: (text) => text.includes('inoa')
      },
      {
        description: 'Especifica tono nivel 6 con refuerzo para canas',
        test: (text) => /6[\/\.]0|nivel 6/i.test(text)
      },
      {
        description: 'Indica INOA Oil Developer apropiado',
        test: (text) => /inoa.*developer|inoa.*oxidante|ods/i.test(text)
      },
      {
        description: 'Menciona proporción 1:1',
        test: (text) => text.includes('1:1')
      },
      {
        description: 'Tiempo extendido para canas resistentes (35-40 min)',
        test: (text) => /3[05]-?4[05]\s*min|40\s*min/.test(text)
      }
    ]
  },
  {
    id: 'tn-001',
    name: 'Tonalización Ceniza (EASY)',
    category: 'Toning',
    difficulty: 'Easy',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Nivel de color base: 7 (rubio medio con tonos cálidos)
- Resultado de decoloración previa: Base amarilla
- Porcentaje de canas: 0%
- Condición del cabello: Sensibilizado por decoloración reciente
- Porosidad: Alta

OBJETIVO DESEADO:
- Neutralizar amarillo
- Conseguir rubio ceniza nivel 7
- Resultado frío sin reflejos cálidos

PREFERENCIAS:
- Marca: Wella Professionals
- Línea de producto: Color Fresh CREATE

Proporciona una fórmula profesional completa con productos, proporciones, técnica de aplicación y tiempos.`,
    validations: [
      {
        description: 'Menciona Color Fresh CREATE (semi-permanente solicitado)',
        test: (text) => text.includes('color fresh create') || text.includes('color fresh')
      },
      {
        description: 'NO menciona oxidante (Color Fresh CREATE no lo requiere)',
        test: (text) => !text.match(/\bcon\s+oxidante\b|\bmezcl[ae]r?\s+con\s+oxidante\b/i) || text.includes('no requiere oxidante') || text.includes('sin oxidante')
      },
      {
        description: 'Menciona tono ceniza o violeta para neutralizar amarillo',
        test: (text) => /ceniza|ash|violet|morado|purple/i.test(text)
      },
      {
        description: 'Indica aplicación sobre cabello limpio y húmedo',
        test: (text) => /limpio|húmedo|towel.?dried/i.test(text)
      },
      {
        description: 'Tiempo de procesamiento 15-20 min (típico para tonalizador)',
        test: (text) => /1[05]-?20\s*min|20\s*min/.test(text)
      }
    ]
  },
  {
    id: 'lt-001',
    name: 'Aclaración 4 Niveles (HARD)',
    category: 'Lightening',
    difficulty: 'Hard',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Nivel de color base: 4 (castaño medio-oscuro)
- Coloración previa: Tinte permanente hace 8 meses (nivel 4)
- Porcentaje de canas: 15%
- Condición del cabello: Buena, algo de porosidad en puntas
- Textura: Media
- Porosidad: Normal a media

OBJETIVO DESEADO:
- Aclarar hasta nivel 8 (rubio claro)
- 4 niveles de aclaración
- Resultado uniforme

PREFERENCIAS:
- Marca: L'Oréal Professionnel
- Línea de producto: Blond Studio

Por favor indica si es necesario más de una sesión y explica el proceso completo.`,
    validations: [
      {
        description: 'Menciona necesidad de 2 sesiones (aclaración de 4 niveles es extrema)',
        test: (text) => /2\s*sesiones|dos\s*sesiones|segunda\s*sesión|segunda\s*aplicación/i.test(text)
      },
      {
        description: 'Especifica decolorador (Blond Studio o similar)',
        test: (text) => /blond studio|decolorador|bleach|lightener/i.test(text)
      },
      {
        description: 'Menciona oxidante alto (30 o 40 vol)',
        test: (text) => /30\s*vol|40\s*vol|9\s*%|12\s*%/.test(text)
      },
      {
        description: 'Incluye advertencia sobre tratamientos previos y posteriores',
        test: (text) => /tratamiento|olaplex|plex|protección|bonding|cuidado/i.test(text)
      },
      {
        description: 'Indica monitoreo constante durante decoloración',
        test: (text) => /monitorear|controlar|revisar|vigilar|supervisar/i.test(text)
      }
    ]
  },
  {
    id: 'cr-001',
    name: 'Corrección de Color Naranja (HARD)',
    category: 'Correction',
    difficulty: 'Hard',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Resultado actual: Color naranja (nivel 7-8)
- Problema: Decoloración casera mal ejecutada
- Color previo: Castaño oscuro (nivel 3) con tinte permanente
- Porcentaje de canas: 0%
- Condición del cabello: Muy poroso, quebradizo, puntas dañadas
- Estado: Color disparejo (raíces más claras, puntas más oscuras)

OBJETIVO DESEADO:
- Corregir el naranja
- Conseguir rubio ceniza nivel 8-9
- Mejorar condición del cabello

PREFERENCIAS:
- Marca: Wella Professionals
- Disponible para usar: Koleston Perfect, Color Touch, Blondor

¿Cómo procederías? ¿Cuántas sesiones? ¿Qué tratamientos recomiendas?`,
    validations: [
      {
        description: 'Menciona 2-3 sesiones (corrección compleja)',
        test: (text) => /2-3\s*sesiones|2\s*o\s*3\s*sesiones|múltiples\s*sesiones|varias\s*sesiones/i.test(text)
      },
      {
        description: 'Indica uso de tonos ceniza/azul para neutralizar naranja',
        test: (text) => /ceniza|ash|azul|blue|violet|morado/i.test(text)
      },
      {
        description: 'Recomienda tratamiento reparador (Olaplex, Wellaplex, etc.)',
        test: (text) => /olaplex|wellaplex|fibreplex|plex|tratamiento.*reparador|bonding/i.test(text)
      },
      {
        description: 'Sugiere corte de puntas dañadas',
        test: (text) => /cortar|corte|sanear|recortar|eliminar.*puntas/i.test(text)
      },
      {
        description: 'Advierte sobre tiempo entre sesiones (mínimo 2 semanas)',
        test: (text) => /2\s*semanas|dos\s*semanas|esperar|descanso|tiempo.*entre.*sesiones/i.test(text)
      }
    ]
  },
  {
    id: 'dk-001',
    name: 'Oscurecimiento de Cabello Decolorado (MEDIUM)',
    category: 'Darkening',
    difficulty: 'Medium',
    prompt: `Necesito una fórmula para un cliente con las siguientes características:

ANÁLISIS ACTUAL:
- Nivel de color actual: 9-10 (rubio muy claro, resultado de decoloración)
- Tiempo desde última decoloración: 3 meses
- Porcentaje de canas: 0%
- Condición del cabello: Poroso, pero en recuperación
- Porosidad: Alta

OBJETIVO DESEADO:
- Oscurecer a nivel 6 (rubio oscuro)
- 3-4 niveles de oscurecimiento
- Tono natural cálido
- Evitar que el color se lave rápido

PREFERENCIAS:
- Marca: Wella Professionals
- Línea de producto: Koleston Perfect

¿Qué pre-tratamiento recomiendas? ¿Qué precauciones tomar?`,
    validations: [
      {
        description: 'Menciona necesidad de relleno de pigmento previo',
        test: (text) => /relleno|pre.?pigmentación|color filler|rellenar.*pigmento/i.test(text)
      },
      {
        description: 'Sugiere Koleston Perfect (solicitado)',
        test: (text) => text.includes('koleston')
      },
      {
        description: 'Indica oxidante bajo (10 vol máximo para oscurecer)',
        test: (text) => /10\s*vol|6\s*vol|3\s*%|1\.9\s*%/i.test(text)
      },
      {
        description: 'Advierte sobre tono base cálido para evitar verde',
        test: (text) => /cálido|warm|gold|dorado|copper|cobrizo/i.test(text)
      },
      {
        description: 'Recomienda champú post-color y mantenimiento',
        test: (text) => /champú.*post|shampoo.*post|mantenimiento|cuidado.*color/i.test(text)
      }
    ]
  }
];

// Ejecutar todos los tests
async function runAllTests() {
  console.log('Este es un TEST REAL con usuario autenticado');
  console.log('Llamará a OpenAI y generará fórmulas reales.\n');

  const results = [];

  for (const testCase of testCases) {
    console.log('─'.repeat(70) + '\n');
    const result = await testCase;
    const testResult = await testFormulaGeneration(testCase);
    results.push({
      id: testCase.id,
      name: testCase.name,
      category: testCase.category,
      difficulty: testCase.difficulty,
      ...testResult
    });

    // Esperar un poco entre tests para no saturar la API
    await new Promise(resolve => setTimeout(resolve, 2000));
  }

  // Resumen
  console.log('═'.repeat(70));
  console.log('RESUMEN DE TESTS DE FORMULACIÓN');
  console.log('═'.repeat(70));
  console.log('');

  results.forEach(result => {
    const status = result.success ? '✅ APROBADO' : '❌ FALLIDO';
    const scoreDisplay = result.score !== undefined ? ` - ${result.score}/${result.maxScore}` : '';
    console.log(`${result.id} - ${result.name.padEnd(40)} ${status}${scoreDisplay} (${result.duration}s)`);
  });

  const allPassed = results.every(r => r.success);
  const avgDuration = (results.reduce((sum, r) => sum + parseFloat(r.duration), 0) / results.length).toFixed(2);
  const avgScore = results.filter(r => r.score !== undefined).length > 0
    ? (results.reduce((sum, r) => sum + (r.score || 0), 0) / results.filter(r => r.score !== undefined).length).toFixed(0)
    : 'N/A';

  console.log('');
  console.log('─'.repeat(70));
  console.log(`Total: ${results.filter(r => r.success).length}/${results.length} aprobados`);
  console.log(`Puntuación promedio: ${avgScore}/50`);
  console.log(`Tiempo promedio: ${avgDuration}s por formulación`);
  console.log('═'.repeat(70));
  console.log('');

  if (allPassed) {
    console.log('🎉 TODOS LOS TESTS APROBADOS');
    console.log('✅ Sistema de formulación funcionando correctamente');
    console.log('');
  } else {
    console.log('⚠️  ALGUNOS TESTS FALLARON');
    console.log('❌ Revisar resultados antes de merge');
    console.log('');
  }

  process.exit(allPassed ? 0 : 1);
}

runAllTests();
