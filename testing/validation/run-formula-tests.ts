/**
 * Script ejecutor de tests de formulación
 *
 * Este script genera formulaciones reales para cada escenario de prueba
 * y permite su evaluación manual por un experto en coloración.
 *
 * USO:
 * npx tsx testing/run-formula-tests.ts [scenario-id]
 *
 * Ejemplos:
 * npx tsx testing/run-formula-tests.ts              # Ejecuta todos
 * npx tsx testing/run-formula-tests.ts gc-001       # Solo gray coverage básico
 * npx tsx testing/run-formula-tests.ts lt-002       # Solo aclaración extrema
 */

import { testScenarios, type TestResult, type EvaluationCriteria } from './formula-validation';
import { supabase } from '../lib/supabase';
import * as fs from 'fs';
import * as path from 'path';

// Asegurarse de que estamos autenticados
async function ensureAuthenticated() {
  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    console.error('\n❌ ERROR: No estás autenticado en Supabase');
    console.error('Por favor, inicia sesión en la aplicación primero.\n');
    process.exit(1);
  }

  console.log(`✅ Autenticado como: ${session.user.email}\n`);
}

// Generar fórmula usando la Edge Function (simulando el flujo de step5)
async function generateFormulaForScenario(scenario: typeof testScenarios[0]): Promise<string> {
  const { currentAnalysis, desiredAnalysis, brand, productLine } = scenario;

  // Crear el sistema y user prompt exactamente como en step5.tsx
  const systemPrompt = `Eres un MAESTRO COLORISTA con más de 20 años de experiencia. Tu misión es actuar como MENTOR educativo, no solo dar instrucciones.

PRINCIPIOS FUNDAMENTALES:
- EXPLICA el "por qué" de cada decisión técnica
- EDUCA sobre los procesos químicos relevantes
- ANTICIPA los desafíos y cómo superarlos
- Proporciona CONTEXTO profesional
- Sé ESPECÍFICO con productos y cantidades
- Genera sesiones REALISTAS (transformaciones grandes necesitan múltiples sesiones)

FORMATO OBLIGATORIO:

## DIAGNÓSTICO PROFESIONAL
[Explica qué ves en el cabello actual y por qué es relevante]

## ESTRATEGIA DE TRANSFORMACIÓN
[Explica el plan completo: ¿Una o varias sesiones? ¿Por qué?]

## SESIÓN 1: [Título descriptivo]

### OBJETIVOS DE ESTA SESIÓN
[Qué lograremos y por qué este es el primer paso]

### PRODUCTOS
[Lista con cantidades específicas y razón de la elección]

### PREPARACIÓN
[Mezcla con explicación de proporciones]

### TÉCNICA DE APLICACIÓN
[Paso a paso con fundamentos técnicos]

### TIEMPOS Y CONTROL
[Tiempos con explicación de señales visuales]

### ENJUAGUE Y TRATAMIENTO
[Protocolo completo post-color]

## SESIÓN 2 (si aplica): [Título]
[Repetir estructura]

## CUIDADO EN CASA
[Productos y rutina específica]

## MANTENIMIENTO PROFESIONAL
[Calendario de retoques]

REGLAS ESTRICTAS:
- Usa productos REALES de ${brand}
- Si no conoces un producto específico, describe el tipo necesario
- Cada decisión debe tener su JUSTIFICACIÓN técnica
- Para cambios de más de 3 niveles, REQUIERE múltiples sesiones
- Considera el estado del cabello (${currentAnalysis.roots.state})
- Sin emojis, lenguaje profesional y limpio`;

  const levelDifference = Math.abs(desiredAnalysis.level - currentAnalysis.roots.level);
  const needsMultipleSessions = levelDifference > 3 ||
    currentAnalysis.roots.state === 'dañado' ||
    currentAnalysis.roots.state === 'muy dañado' ||
    (currentAnalysis.chemicalHistory.lastProcessType !== 'ninguno' && levelDifference > 2);

  const userPrompt = `IMPORTANTE: Actúa como un mentor colorista que EDUCA mientras formula.

=== ANÁLISIS COMPLETO DEL CABELLO ACTUAL ===

ESTRUCTURA Y CONDICIÓN:
- Grosor: ${currentAnalysis.generalCharacteristics.thickness}
- Densidad: ${currentAnalysis.generalCharacteristics.density}
- Estado raíces: ${currentAnalysis.roots.state}
- Estado medios: ${currentAnalysis.mids.state}
- Estado puntas: ${currentAnalysis.ends.state}
- Porosidad: ${currentAnalysis.roots.porosity}
- Elasticidad: ${currentAnalysis.roots.elasticity}
- Resistencia: ${currentAnalysis.roots.resistance}

COLOR POR ZONAS:
- RAÍCES: Nivel ${currentAnalysis.roots.level}/10 | Tono: ${currentAnalysis.roots.tone} | Reflejo: ${currentAnalysis.roots.reflection}
- MEDIOS: Nivel ${currentAnalysis.mids.level}/10 | Estado: ${currentAnalysis.mids.state}
- PUNTAS: Nivel ${currentAnalysis.ends.level}/10 | Estado: ${currentAnalysis.ends.state}

CANAS:
- Porcentaje: ${currentAnalysis.grayAnalysis.percentage}%
- Visibilidad: ${currentAnalysis.grayAnalysis.visibility}/10
- Tipo: ${currentAnalysis.grayAnalysis.type}
- Distribución: ${currentAnalysis.grayAnalysis.distributionPattern}

HISTORIAL QUÍMICO:
${currentAnalysis.chemicalHistory.lastProcessType !== 'ninguno' ? `- Último proceso: ${currentAnalysis.chemicalHistory.lastProcessType}` : '- Sin tratamientos químicos previos'}
${currentAnalysis.chemicalHistory.lastProcessDate ? `- Fecha: ${currentAnalysis.chemicalHistory.lastProcessDate}` : ''}
${currentAnalysis.chemicalHistory.hasUsedHomeRemedies ? `- Remedios caseros: ${currentAnalysis.chemicalHistory.homeRemediesDetails}` : ''}
${currentAnalysis.hasDemarcationBands ? `- Bandas de demarcación: ${currentAnalysis.demarcationBandsDetails}` : ''}

=== COLOR OBJETIVO ===
- Nivel deseado: ${desiredAnalysis.level}/10 (cambio de ${levelDifference} niveles)
- Tono: ${desiredAnalysis.tone}
- Reflejo: ${desiredAnalysis.reflection} (intensidad: ${desiredAnalysis.reflectionIntensity})
- Profundidad: ${desiredAnalysis.colorDepth}
- Tipo de resultado: ${desiredAnalysis.resultType}
- Cobertura de canas: ${desiredAnalysis.grayCoverage}%
- Técnica solicitada: ${desiredAnalysis.technique}
${desiredAnalysis.notes ? `- Notas adicionales: ${desiredAnalysis.notes}` : ''}

=== MARCA Y PRODUCTOS ===
- Marca: ${brand}
${productLine ? `- Línea: ${productLine}` : ''}

=== CLIENTE ===
${scenario.name} - ${scenario.description}

=== INSTRUCCIONES CRÍTICAS ===

${needsMultipleSessions ?
`⚠️ ALERTA: Esta transformación requiere MÚLTIPLES SESIONES
- Cambio de ${levelDifference} niveles detectado
- Estado del cabello: ${currentAnalysis.roots.state}
- Debes dividir el proceso en sesiones SEGURAS
- EXPLICA por qué no puede hacerse en una sola sesión` :
`✓ Esta transformación puede realizarse en UNA sesión de forma segura`}

1. DIAGNOSTICA el cabello explicando qué ves y qué significa
2. EXPLICA tu estrategia completa (¿1 sesión o más? ¿Por qué?)
3. Para cada sesión, describe:
   - QUÉ lograremos (objetivo específico)
   - POR QUÉ elegiste estos productos
   - CÓMO funcionan químicamente
   - QUÉ buscar durante el proceso
4. ANTICIPA problemas potenciales
5. Da instrucciones de CUIDADO específicas

Recuerda: Eres un MENTOR, no solo un formulador. Tu objetivo es que el colorista entienda el RAZONAMIENTO detrás de cada decisión.`;

  console.log(`  📡 Llamando a Edge Function...`);

  const startTime = Date.now();

  const response = await supabase.functions.invoke('ai-proxy', {
    body: {
      useCase: 'formula_generation',
      prompt: userPrompt,
      systemPrompt: systemPrompt,
      brand,
      productLine,
    },
  });

  const executionTime = Date.now() - startTime;

  if (response.error) {
    throw new Error(`Edge Function error: ${JSON.stringify(response.error)}`);
  }

  if (!response.data) {
    throw new Error('Edge Function returned no data');
  }

  console.log(`  ✅ Fórmula generada en ${executionTime}ms (costo: $${response.data.cost.toFixed(4)})`);

  return response.data.text;
}

// Ejecutar test para un escenario
async function runTest(scenario: typeof testScenarios[0]): Promise<TestResult> {
  console.log(`\n${'='.repeat(80)}`);
  console.log(`🧪 TEST: ${scenario.id} - ${scenario.name}`);
  console.log(`${'='.repeat(80)}`);
  console.log(`Categoría: ${scenario.category.toUpperCase()}`);
  console.log(`Dificultad: ${scenario.difficulty.toUpperCase()}`);
  console.log(`Descripción: ${scenario.description}`);
  console.log(`Sesiones esperadas: ${scenario.expectedSessions}`);
  if (scenario.expectedWarnings.length > 0) {
    console.log(`Advertencias esperadas:`);
    scenario.expectedWarnings.forEach(w => console.log(`  ⚠️  ${w}`));
  }
  console.log();

  const startTime = Date.now();

  try {
    const formula = await generateFormulaForScenario(scenario);
    const executionTime = Date.now() - startTime;

    // Template de evaluación vacío para llenado manual
    const evaluation: EvaluationCriteria = {
      safety: {
        score: 0,
        preservesHairIntegrity: false,
        appropriateOxidantVolume: false,
        includesProtectiveTreatments: false,
        respectsHairCondition: false,
        notes: '',
      },
      effectiveness: {
        score: 0,
        willAchieveDesiredColor: false,
        correctProductSelection: false,
        appropriateTechnique: false,
        realisticTimeline: false,
        notes: '',
      },
      coherence: {
        score: 0,
        logicalStepSequence: false,
        correctDiagnosis: false,
        appropriateSessionCount: false,
        technicallySound: false,
        notes: '',
      },
      education: {
        score: 0,
        explainsReasoning: false,
        providesContext: false,
        anticipatesProblems: false,
        includesWhyNotJustHow: false,
        notes: '',
      },
      problemManagement: {
        score: 0,
        identifiesRisks: false,
        providesContingencyPlans: false,
        realisticAboutChallenges: false,
        includesAftercare: false,
        notes: '',
      },
      overall: {
        totalScore: 0,
        wouldUseThisFormula: false,
        majorConcerns: [],
        strengths: [],
        improvements: [],
      },
    };

    const result: TestResult = {
      scenario,
      generatedFormula: formula,
      evaluation,
      timestamp: new Date(),
      executionTimeMs: executionTime,
    };

    console.log(`\n✅ Test completado en ${executionTime}ms\n`);

    return result;

  } catch (error: any) {
    console.error(`\n❌ Error al ejecutar test: ${error.message}\n`);
    throw error;
  }
}

// Guardar resultados en archivo Markdown
function saveResults(results: TestResult[]) {
  const timestamp = new Date().toISOString().split('T')[0];
  const outputDir = path.join(__dirname, '../sessions');
  const outputFile = path.join(outputDir, `${timestamp}-formula-testing-results.md`);

  let markdown = `# Resultados de Testing de Formulaciones\n\n`;
  markdown += `**Fecha**: ${new Date().toLocaleString('es-ES')}\n`;
  markdown += `**Tests ejecutados**: ${results.length}\n\n`;

  markdown += `## Resumen\n\n`;
  markdown += `| ID | Escenario | Categoría | Dificultad | Sesiones esperadas | Tiempo (ms) |\n`;
  markdown += `|----|-----------|-----------|------------|--------------------|-------------|\n`;

  results.forEach(result => {
    markdown += `| ${result.scenario.id} | ${result.scenario.name} | ${result.scenario.category} | ${result.scenario.difficulty} | ${result.scenario.expectedSessions} | ${result.executionTimeMs} |\n`;
  });

  markdown += `\n---\n\n`;

  results.forEach((result, index) => {
    markdown += `## ${index + 1}. ${result.scenario.id}: ${result.scenario.name}\n\n`;

    markdown += `### Contexto\n\n`;
    markdown += `- **Descripción**: ${result.scenario.description}\n`;
    markdown += `- **Categoría**: ${result.scenario.category}\n`;
    markdown += `- **Dificultad**: ${result.scenario.difficulty}\n`;
    markdown += `- **Marca**: ${result.scenario.brand}${result.scenario.productLine ? ` - ${result.scenario.productLine}` : ''}\n`;
    markdown += `- **Sesiones esperadas**: ${result.scenario.expectedSessions}\n`;

    if (result.scenario.expectedWarnings.length > 0) {
      markdown += `- **Advertencias esperadas**:\n`;
      result.scenario.expectedWarnings.forEach(w => {
        markdown += `  - ${w}\n`;
      });
    }

    markdown += `\n### Estado del Cabello Actual\n\n`;
    markdown += `- **Nivel raíces**: ${result.scenario.currentAnalysis.roots.level}/10\n`;
    markdown += `- **Tono**: ${result.scenario.currentAnalysis.roots.tone}\n`;
    markdown += `- **Estado raíces**: ${result.scenario.currentAnalysis.roots.state}\n`;
    markdown += `- **Estado medios**: ${result.scenario.currentAnalysis.mids.state}\n`;
    markdown += `- **Estado puntas**: ${result.scenario.currentAnalysis.ends.state}\n`;
    markdown += `- **Canas**: ${result.scenario.currentAnalysis.grayAnalysis.percentage}%\n`;
    markdown += `- **Historial químico**: ${result.scenario.currentAnalysis.chemicalHistory.lastProcessType}\n`;

    markdown += `\n### Color Deseado\n\n`;
    markdown += `- **Nivel**: ${result.scenario.desiredAnalysis.level}/10\n`;
    markdown += `- **Tono**: ${result.scenario.desiredAnalysis.tone}\n`;
    markdown += `- **Reflejo**: ${result.scenario.desiredAnalysis.reflection} (${result.scenario.desiredAnalysis.reflectionIntensity})\n`;
    markdown += `- **Técnica**: ${result.scenario.desiredAnalysis.technique}\n`;
    markdown += `- **Cobertura de canas**: ${result.scenario.desiredAnalysis.grayCoverage}%\n`;

    markdown += `\n### Fórmula Generada\n\n`;
    markdown += `\`\`\`\n${result.generatedFormula}\n\`\`\`\n\n`;

    markdown += `### Evaluación del Experto\n\n`;
    markdown += `**PENDIENTE DE COMPLETAR**\n\n`;
    markdown += `#### 1. Seguridad (0-10): ___\n`;
    markdown += `- [ ] Preserva integridad del cabello\n`;
    markdown += `- [ ] Volumen de oxidante apropiado\n`;
    markdown += `- [ ] Incluye tratamientos protectores\n`;
    markdown += `- [ ] Respeta condición del cabello\n`;
    markdown += `- **Notas**: \n\n`;

    markdown += `#### 2. Efectividad Técnica (0-10): ___\n`;
    markdown += `- [ ] Logrará el color deseado\n`;
    markdown += `- [ ] Selección correcta de productos\n`;
    markdown += `- [ ] Técnica apropiada\n`;
    markdown += `- [ ] Timeline realista\n`;
    markdown += `- **Notas**: \n\n`;

    markdown += `#### 3. Coherencia Profesional (0-10): ___\n`;
    markdown += `- [ ] Secuencia lógica de pasos\n`;
    markdown += `- [ ] Diagnóstico correcto\n`;
    markdown += `- [ ] Número apropiado de sesiones\n`;
    markdown += `- [ ] Técnicamente sólido\n`;
    markdown += `- **Notas**: \n\n`;

    markdown += `#### 4. Educación y Explicación (0-10): ___\n`;
    markdown += `- [ ] Explica el razonamiento\n`;
    markdown += `- [ ] Proporciona contexto\n`;
    markdown += `- [ ] Anticipa problemas\n`;
    markdown += `- [ ] Incluye el "por qué", no solo el "cómo"\n`;
    markdown += `- **Notas**: \n\n`;

    markdown += `#### 5. Gestión de Problemas (0-10): ___\n`;
    markdown += `- [ ] Identifica riesgos\n`;
    markdown += `- [ ] Planes de contingencia\n`;
    markdown += `- [ ] Realista sobre desafíos\n`;
    markdown += `- [ ] Incluye aftercare\n`;
    markdown += `- **Notas**: \n\n`;

    markdown += `#### Evaluación Global\n`;
    markdown += `- **Puntaje total**: ___/50\n`;
    markdown += `- **¿Usarías esta fórmula?**: [ ] Sí / [ ] No\n`;
    markdown += `- **Preocupaciones principales**:\n`;
    markdown += `  1. \n`;
    markdown += `  2. \n`;
    markdown += `  3. \n`;
    markdown += `- **Fortalezas**:\n`;
    markdown += `  1. \n`;
    markdown += `  2. \n`;
    markdown += `  3. \n`;
    markdown += `- **Mejoras sugeridas**:\n`;
    markdown += `  1. \n`;
    markdown += `  2. \n`;
    markdown += `  3. \n\n`;

    markdown += `---\n\n`;
  });

  // Crear directorio si no existe
  if (!fs.existsSync(outputDir)) {
    fs.mkdirSync(outputDir, { recursive: true });
  }

  fs.writeFileSync(outputFile, markdown);
  console.log(`\n📝 Resultados guardados en: ${outputFile}\n`);
}

// Main
async function main() {
  const args = process.argv.slice(2);
  const scenarioId = args[0];

  console.log('\n🧪 TESTING DE FORMULACIONES - Salonier AI\n');

  await ensureAuthenticated();

  let scenariosToRun = testScenarios;

  if (scenarioId) {
    const scenario = testScenarios.find(s => s.id === scenarioId);
    if (!scenario) {
      console.error(`❌ Escenario "${scenarioId}" no encontrado\n`);
      console.log('Escenarios disponibles:');
      testScenarios.forEach(s => {
        console.log(`  - ${s.id}: ${s.name}`);
      });
      process.exit(1);
    }
    scenariosToRun = [scenario];
  }

  console.log(`📋 Ejecutando ${scenariosToRun.length} test(s)...\n`);

  const results: TestResult[] = [];

  for (const scenario of scenariosToRun) {
    try {
      const result = await runTest(scenario);
      results.push(result);
    } catch (error: any) {
      console.error(`❌ Error fatal en ${scenario.id}: ${error.message}`);
      process.exit(1);
    }
  }

  saveResults(results);

  console.log(`\n${'='.repeat(80)}`);
  console.log(`✅ TESTING COMPLETADO`);
  console.log(`${'='.repeat(80)}`);
  console.log(`Tests ejecutados: ${results.length}`);
  console.log(`\n📊 Próximos pasos:`);
  console.log(`  1. Revisa las fórmulas generadas en el archivo sessions/`);
  console.log(`  2. Completa las evaluaciones como experto en coloración`);
  console.log(`  3. Documenta hallazgos y recomendaciones\n`);
}

main().catch(error => {
  console.error('\n💥 Error fatal:', error);
  process.exit(1);
});
