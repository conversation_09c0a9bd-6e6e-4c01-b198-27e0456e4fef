# NEXT SESSION PROMPT - Fix #3: Sistema de Logging Centralizado

**Fecha de creación**: 2025-10-30
**Prioridad**: Media (CVSS 7.4 - Information Disclosure)
**Estimación**: 2-3 horas
**Contexto**: Post PR #30 (6 fixes críticos completados exitosamente)

---

## 🎯 Objetivo

Reemplazar 66 ocurrencias de `console.log` con un sistema de logging centralizado que:
- ✅ Prevenga exposición de datos sensibles en producción
- ✅ Mantenga debugging útil en desarrollo
- ✅ Permita niveles de log (debug, info, warn, error)
- ✅ Sea compatible con React Native (iOS/Android/Web)

---

## 📊 Análisis de Impacto

### Archivos Afectados (66 console.log total)

| Archivo | Ocurrencias | Prioridad | Riesgo |
|---------|-------------|-----------|--------|
| `contexts/ChatContext.tsx` | 33 | ALTA | Mensajes de usuarios, imágenes, tokens AI |
| `contexts/AuthContext.tsx` | 17 | CRÍTICA | Datos de autenticación, user IDs, sesiones |
| `contexts/ClientContext.tsx` | 16 | ALTA | Información de clientes, salud, alergias |

### Riesgos de Seguridad

**CVSS 7.4 - Information Disclosure**:
- Exposición de tokens de usuario en logs
- Mensajes de chat con información sensible
- Datos de salud de clientes (alergias, embarazos)
- User IDs y session tokens
- Prompts a la IA (podrían contener PII)

---

## 🛠️ Implementación Detallada

### PASO 1: Crear Sistema de Logging Centralizado

**Archivo**: `lib/logger.ts` (NUEVO)

```typescript
/**
 * Sistema de Logging Centralizado
 *
 * Features:
 * - Niveles de log: debug, info, warn, error
 * - Sanitización automática de datos sensibles
 * - Deshabilitado en producción (solo errors)
 * - Compatible con React Native
 * - Formato consistente con timestamps
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  enabled: boolean;
  minLevel: LogLevel;
  sanitize: boolean;
}

class Logger {
  private config: LoggerConfig;
  private readonly levels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
  };

  constructor() {
    const isDev = __DEV__; // React Native global

    this.config = {
      enabled: isDev,
      minLevel: isDev ? 'debug' : 'error', // Solo errors en producción
      sanitize: !isDev, // Sanitizar en producción
    };
  }

  /**
   * Sanitiza datos sensibles antes de loggear
   */
  private sanitize(data: any): any {
    if (!this.config.sanitize) return data;

    if (typeof data === 'string') {
      // Redactar tokens, emails, etc.
      return data
        .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]')
        .replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, '[UUID_REDACTED]')
        .replace(/Bearer\s+[A-Za-z0-9\-._~+\/]+=*/g, 'Bearer [TOKEN_REDACTED]')
        .replace(/token[=:]\s*[A-Za-z0-9\-._~+\/]+=*/gi, 'token=[TOKEN_REDACTED]');
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized: any = Array.isArray(data) ? [] : {};

      for (const key in data) {
        // Redactar campos sensibles
        if (/password|token|secret|key|auth/i.test(key)) {
          sanitized[key] = '[REDACTED]';
        } else if (/email/i.test(key)) {
          sanitized[key] = '[EMAIL_REDACTED]';
        } else {
          sanitized[key] = this.sanitize(data[key]);
        }
      }

      return sanitized;
    }

    return data;
  }

  /**
   * Formatea el mensaje de log con timestamp y contexto
   */
  private format(level: LogLevel, context: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const levelUpper = level.toUpperCase().padEnd(5);

    let formatted = `[${timestamp}] ${levelUpper} [${context}] ${message}`;

    if (data !== undefined) {
      const sanitizedData = this.sanitize(data);
      formatted += `\n${JSON.stringify(sanitizedData, null, 2)}`;
    }

    return formatted;
  }

  /**
   * Verifica si el nivel de log debe ejecutarse
   */
  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return level === 'error';
    return this.levels[level] >= this.levels[this.config.minLevel];
  }

  /**
   * Log de debugging (solo desarrollo)
   */
  debug(context: string, message: string, data?: any): void {
    if (!this.shouldLog('debug')) return;
    console.debug(this.format('debug', context, message, data));
  }

  /**
   * Log informativo
   */
  info(context: string, message: string, data?: any): void {
    if (!this.shouldLog('info')) return;
    console.info(this.format('info', context, message, data));
  }

  /**
   * Log de advertencia
   */
  warn(context: string, message: string, data?: any): void {
    if (!this.shouldLog('warn')) return;
    console.warn(this.format('warn', context, message, data));
  }

  /**
   * Log de error (siempre se ejecuta, incluso en producción)
   */
  error(context: string, message: string, error?: Error | any): void {
    if (!this.shouldLog('error')) return;

    const errorData = error instanceof Error
      ? { message: error.message, stack: error.stack }
      : error;

    console.error(this.format('error', context, message, errorData));
  }

  /**
   * Grupo de logs (útil para operaciones complejas)
   */
  group(context: string, label: string, callback: () => void): void {
    if (!this.config.enabled) return;

    console.group(`[${context}] ${label}`);
    callback();
    console.groupEnd();
  }
}

// Exportar instancia singleton
export const logger = new Logger();

// Exportar tipos para TypeScript
export type { LogLevel };
```

---

### PASO 2: Actualizar Tests de Seguridad

**Archivo**: `__tests__/security/logger-security.test.ts` (NUEVO)

```typescript
import { logger } from '@/lib/logger';

describe('Logger Security', () => {
  beforeEach(() => {
    jest.spyOn(console, 'debug').mockImplementation();
    jest.spyOn(console, 'info').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('Sanitization', () => {
    it('CRITICAL: should redact email addresses', () => {
      logger.info('Test', 'User data', { email: '<EMAIL>' });

      const loggedData = (console.info as jest.Mock).mock.calls[0][0];
      expect(loggedData).not.toContain('<EMAIL>');
      expect(loggedData).toContain('[EMAIL_REDACTED]');
    });

    it('CRITICAL: should redact UUIDs', () => {
      const uuid = '550e8400-e29b-41d4-a716-************';
      logger.info('Test', 'Client ID', { clientId: uuid });

      const loggedData = (console.info as jest.Mock).mock.calls[0][0];
      expect(loggedData).not.toContain(uuid);
      expect(loggedData).toContain('[UUID_REDACTED]');
    });

    it('CRITICAL: should redact Bearer tokens', () => {
      logger.info('Test', 'Auth header', 'Authorization: Bearer eyJhbGc...');

      const loggedData = (console.info as jest.Mock).mock.calls[0][0];
      expect(loggedData).not.toContain('eyJhbGc');
      expect(loggedData).toContain('[TOKEN_REDACTED]');
    });

    it('CRITICAL: should redact password fields', () => {
      logger.info('Test', 'User login', { email: '<EMAIL>', password: 'secret123' });

      const loggedData = (console.info as jest.Mock).mock.calls[0][0];
      expect(loggedData).not.toContain('secret123');
      expect(loggedData).toContain('[REDACTED]');
    });
  });

  describe('Production Safety', () => {
    it('CRITICAL: should only log errors in production', () => {
      // Mock production environment
      (global as any).__DEV__ = false;

      logger.debug('Test', 'Debug message');
      logger.info('Test', 'Info message');
      logger.warn('Test', 'Warn message');
      logger.error('Test', 'Error message');

      expect(console.debug).not.toHaveBeenCalled();
      expect(console.info).not.toHaveBeenCalled();
      expect(console.warn).not.toHaveBeenCalled();
      expect(console.error).toHaveBeenCalled();

      // Restore
      (global as any).__DEV__ = true;
    });
  });
});
```

---

### PASO 3: Migrar ChatContext (33 console.log)

**Archivo**: `contexts/ChatContext.tsx`

**Patrón de reemplazo**:

```typescript
// ❌ ANTES
console.log('[Chat] Starting new conversation');
console.log('[Chat] Message sent:', message);
console.error('[Chat] Error loading conversations:', error);

// ✅ DESPUÉS
import { logger } from '@/lib/logger';

logger.info('ChatContext', 'Starting new conversation');
logger.debug('ChatContext', 'Message sent', { messageId: message.id, length: message.content.length });
logger.error('ChatContext', 'Error loading conversations', error);
```

**Casos especiales**:
- Mensajes de usuario: NO loggear contenido completo, solo metadata (ID, length)
- Imágenes: NO loggear base64, solo URI o tamaño
- Tokens AI: NUNCA loggear

---

### PASO 4: Migrar AuthContext (17 console.log)

**Archivo**: `contexts/AuthContext.tsx`

**CRÍTICO**: Este archivo maneja autenticación

```typescript
// ❌ ANTES
console.log('User logged in:', user);
console.log('Session token:', session.access_token);

// ✅ DESPUÉS
logger.info('AuthContext', 'User logged in', { userId: user.id }); // Sin email ni tokens
logger.debug('AuthContext', 'Session refreshed'); // Sin tokens
```

**Reglas estrictas**:
- NUNCA loggear `access_token`, `refresh_token`, `password`
- Solo loggear `userId` (ya está sanitizado por UUID redaction)
- Emails solo en development (sanitizados en producción)

---

### PASO 5: Migrar ClientContext (16 console.log)

**Archivo**: `contexts/ClientContext.tsx`

**SENSIBLE**: Datos de salud (alergias, embarazos)

```typescript
// ❌ ANTES
console.log('Client updated:', client);
console.log('Known allergies:', client.knownAllergies);

// ✅ DESPUÉS
logger.info('ClientContext', 'Client updated', { clientId: client.id, hasAllergies: !!client.knownAllergies });
logger.debug('ClientContext', 'Health data updated'); // Sin detalles de alergias
```

**Reglas**:
- NO loggear nombres completos de clientes
- NO loggear datos de salud específicos
- Solo metadata: `clientId`, `hasAllergies` (boolean), `isPregnant` (boolean)

---

## ✅ Validaciones (CRÍTICAS)

### 1. Tests de Seguridad
```bash
bun run test:security
```
**Esperado**: 43 + 10 nuevos tests = 53 tests passing

### 2. Lint
```bash
bun run lint
```
**Esperado**: 0 errors (warnings reducidos al eliminar console.log)

### 3. IDE Diagnostics
```typescript
mcp__ide__getDiagnostics()
```
**Esperado**: 0 errors

### 4. Búsqueda de console.log restantes
```bash
grep -r "console\.log" --include="*.ts" --include="*.tsx" contexts/ lib/ app/ | grep -v node_modules | grep -v "__tests__"
```
**Esperado**: 0 resultados (excepto comentarios)

### 5. Prueba Manual en Desarrollo
```bash
bun run start-web
```
**Verificar**:
- Logs aparecen en consola con formato correcto
- Timestamps presentes
- Contexto claro (`[ChatContext]`, `[AuthContext]`, etc.)
- Datos sensibles NO aparecen

### 6. Prueba Manual en "Producción" Simulada
```typescript
// Temporalmente en lib/logger.ts
const isDev = false; // Simular producción

bun run start-web
```
**Verificar**:
- Solo errores aparecen en consola
- Info/debug/warn NO aparecen
- Datos sanitizados (emails → [EMAIL_REDACTED], UUIDs → [UUID_REDACTED])

---

## 📦 Entregables

1. ✅ `lib/logger.ts` - Sistema de logging centralizado
2. ✅ `__tests__/security/logger-security.test.ts` - 10 tests de seguridad
3. ✅ `contexts/ChatContext.tsx` - 33 console.log reemplazados
4. ✅ `contexts/AuthContext.tsx` - 17 console.log reemplazados
5. ✅ `contexts/ClientContext.tsx` - 16 console.log reemplazados
6. ✅ Actualizar `jest.config.security.js` - Incluir logger tests
7. ✅ Documentación en `sessions/YYYY-MM-DD-logger-implementation.md`

---

## 🚀 Workflow de Ejecución

### Setup
```bash
git checkout main
git pull origin main
git checkout -b fix/centralized-logger-system
```

### Implementación (orden recomendado)
1. Crear `lib/logger.ts` con clase Logger completa
2. Crear `__tests__/security/logger-security.test.ts`
3. Ejecutar tests: `bun run test:security` (verificar 10 nuevos tests pasan)
4. Migrar `contexts/AuthContext.tsx` (PRIMERO - más crítico)
5. Ejecutar tests + lint después de AuthContext
6. Migrar `contexts/ChatContext.tsx`
7. Ejecutar tests + lint después de ChatContext
8. Migrar `contexts/ClientContext.tsx`
9. Ejecutar tests + lint después de ClientContext
10. Validación final con IDE diagnostics
11. Búsqueda de console.log restantes
12. Pruebas manuales (dev + producción simulada)

### Commit Strategy (4 commits recomendados)
```bash
# Commit 1: Logger infrastructure
git add lib/logger.ts __tests__/security/logger-security.test.ts jest.config.security.js
git commit -m "Feat: Add centralized logging system with sanitization"

# Commit 2: Migrate AuthContext
git add contexts/AuthContext.tsx
git commit -m "Security: Replace console.log with logger in AuthContext (17 occurrences)"

# Commit 3: Migrate ChatContext + ClientContext
git add contexts/ChatContext.tsx contexts/ClientContext.tsx
git commit -m "Security: Replace console.log with logger in Chat and Client contexts (49 occurrences)"

# Commit 4: Documentation
git add sessions/YYYY-MM-DD-logger-implementation.md
git commit -m "Docs: Document logger implementation and security improvements"
```

### Pull Request
```bash
git push -u origin fix/centralized-logger-system
gh pr create --title "Security: Centralized Logging System (Fix #3 - CVSS 7.4)" --body "..."
```

### Code Review (Agentes en paralelo)
```bash
# Invocar 3 agentes simultáneamente
@Code-Reviewer - Revisar implementación de logger y migraciones
@Security-Reviewer - Validar sanitización y prevención de data leaks
@Test-Engineer - Verificar cobertura de tests de logger
```

---

## 🎯 Criterios de Éxito

- [ ] 0 console.log en `contexts/ChatContext.tsx`
- [ ] 0 console.log en `contexts/AuthContext.tsx`
- [ ] 0 console.log en `contexts/ClientContext.tsx`
- [ ] 53/53 tests de seguridad passing (43 existentes + 10 nuevos)
- [ ] Lint: 0 errors
- [ ] IDE diagnostics: 0 errors
- [ ] Logs sanitizados en producción simulada
- [ ] Solo errors en producción, todos los niveles en development
- [ ] Formato consistente con timestamps y contexto
- [ ] Documentación completa en sessions/

---

## 📚 Referencias

- **CVSS 7.4**: Information Disclosure via Application Logs
- **OWASP**: A09:2021 - Security Logging and Monitoring Failures
- **CWE-532**: Insertion of Sensitive Information into Log File
- **React Native Docs**: [`__DEV__` global variable](https://reactnative.dev/docs/javascript-environment#polyfills)
- **Supabase Security**: [Best Practices for Logging](https://supabase.com/docs/guides/platform/going-into-prod#logging)

---

## ⚠️ Notas Importantes

1. **NO** loggear en producción excepto errors
2. **SANITIZAR** siempre en producción (emails, UUIDs, tokens)
3. **NUNCA** loggear: passwords, tokens, full messages, health data
4. **SOLO** loggear metadata: IDs, lengths, booleans, counts
5. **Usar** grupos (`logger.group`) para operaciones complejas
6. **Verificar** que __DEV__ funciona correctamente en React Native

---

## 💡 Prompt para Copiar/Pegar en Próxima Sesión

```
Lee NEXT_SESSION_PROMPT.md y ejecuta el Fix #3: Sistema de Logging Centralizado.

Implementa en este orden:
1. Crear lib/logger.ts con clase Logger completa
2. Crear __tests__/security/logger-security.test.ts (10 tests)
3. Ejecutar bun run test:security (verificar 53/53 passing)
4. Migrar contexts/AuthContext.tsx (17 console.log)
5. Migrar contexts/ChatContext.tsx (33 console.log)
6. Migrar contexts/ClientContext.tsx (16 console.log)
7. Validaciones finales (lint, IDE diagnostics, búsqueda de console.log restantes)
8. Pruebas manuales (desarrollo + producción simulada)
9. Crear 4 commits granulares
10. Push y crear PR
11. Code review con 3 agentes en paralelo (@Code-Reviewer, @Security-Reviewer, @Test-Engineer)

IMPORTANTE:
- NO loggear datos sensibles (tokens, passwords, emails completos, mensajes completos)
- Solo metadata en producción (IDs, lengths, booleans)
- Verificar que __DEV__ funciona correctamente
- Sanitización automática en producción

¿Empezamos con la creación de lib/logger.ts?
```

---

**Última actualización**: 2025-10-30
**Autor**: Claude Code
**Sesión anterior**: PR #30 (6 Critical Fixes) - Merged exitosamente ✅
