import React from 'react';
import ChatScreen from '@/app/(app)/(tabs)/chat';
import { createStep5AnchorHandler } from '@/app/(app)/formula/step5';
import { Alert, LayoutAnimation } from 'react-native';
import type { MutableRefObject } from 'react';

jest.mock('@testing-library/react-native/build/helpers/ensure-peer-deps', () => ({
  ensurePeerDeps: () => {},
}));

const {
  act,
  fireEvent,
  render,
  waitFor,
  waitForElementToBeRemoved,
} = require('@testing-library/react-native');

declare global {
  // eslint-disable-next-line no-var
  var requestAnimationFrame: typeof requestAnimationFrame;
}

const mockScrollToIndex = jest.fn();
const mockScrollToEnd = jest.fn();
const mockScrollToOffset = jest.fn();
const mockScrollViewTo = jest.fn();

jest.mock('react-native/Libraries/Lists/FlatList', () => {
  const React = require('react');
  const { View } = require('react-native');

  const FlatListMock = React.forwardRef((props: any, ref) => {
    const {
      data = [],
      renderItem,
      ListHeaderComponent,
      ListFooterComponent,
      testID,
      onLayout,
      onContentSizeChange,
    } = props;

    React.useImperativeHandle(ref, () => ({
      scrollToIndex: mockScrollToIndex,
      scrollToEnd: mockScrollToEnd,
      scrollToOffset: mockScrollToOffset,
    }));

    React.useEffect(() => {
      onLayout?.({ nativeEvent: { layout: { width: 360, height: 640 } } });
    }, [onLayout]);

    React.useEffect(() => {
      onContentSizeChange?.(360, data.length * 100);
    }, [onContentSizeChange, data.length]);

    return (
      <View testID={testID}>
        {React.isValidElement(ListHeaderComponent)
          ? ListHeaderComponent
          : ListHeaderComponent?.()}
        {data.map((item: any, index: number) => renderItem({ item, index }))}
        {ListFooterComponent ? ListFooterComponent() : null}
      </View>
    );
  });

  FlatListMock.displayName = 'FlatListMock';

  return {
    __esModule: true,
    default: FlatListMock,
  };
});

jest.mock('react-native/Libraries/Components/ScrollView/ScrollView', () => {
  const React = require('react');
  const { View } = require('react-native');

  const ScrollViewMock = React.forwardRef((props: any, ref) => {
    const { children, testID, style, contentContainerStyle } = props;

    React.useImperativeHandle(ref, () => ({
      scrollTo: mockScrollViewTo,
    }));

    return (
      <View testID={testID} style={style}>
        <View style={contentContainerStyle}>{children}</View>
      </View>
    );
  });

  ScrollViewMock.displayName = 'ScrollViewMock';

  return {
    __esModule: true,
    default: ScrollViewMock,
  };
});

jest.mock('react-native/Libraries/Components/Clipboard/Clipboard', () => ({
  default: {
    setString: jest.fn(),
  },
}));

jest.mock('react-native-safe-area-context', () => ({
  useSafeAreaInsets: () => ({ top: 0, bottom: 0, left: 0, right: 0 }),
}));

const mockUseChat = jest.fn();
jest.mock('@/contexts/ChatContext', () => ({
  useChat: () => mockUseChat(),
}));

jest.mock('@/contexts/ClientContext', () => ({
  useClients: () => ({
    clients: [
      {
        id: 'client-1',
        name: 'Ana',
        lastVisit: new Date('2025-01-01T10:00:00Z').toISOString(),
        knownAllergies: null,
      },
    ],
  }),
}));

jest.mock('@/lib/ai-client', () => ({
  generateTextSafe: jest.fn(async (options: any) => {
    if (options.stream && typeof options.onStreamResult === 'function') {
      options.onStreamResult({ type: 'token', text: 'stream token' });
      options.onStreamResult({ type: 'done', text: 'stream final' });
      return 'stream final';
    }
    return 'respuesta asistente';
  }),
  getErrorMessage: jest.fn(() => 'Error controlado'),
}));

jest.mock('@/lib/conversation-intelligence', () => ({
  detectConversationIntent: jest.fn(async () => ({ intent: 'diagnostico', confidence: 0.9 })),
  describeIntent: jest.fn(() => 'Intento: diagnostico'),
  formatMemoryForPrompt: jest.fn(() => 'Memoria: sin datos críticos'),
  summarizeConversation: jest.fn(async () => ({
    summary: 'Resumen breve',
    keyFacts: [],
    followUpQuestions: [],
    lastIntent: 'diagnostico',
  })),
}));

jest.mock('@/lib/context-builders', () => ({
  buildMainChatContext: jest.fn(async () => ({
    sections: ['contexto'],
  })),
  buildFormulaContext: jest.fn(() => ({
    sections: ['formula'],
  })),
  sanitizeContext: jest.fn(() => ({
    systemPrompt: 'System mock',
    userPrompt: 'User mock',
    appContext: { locale: 'es-MX' },
  })),
}));

jest.mock('@/lib/vision-safety-utils', () => ({
  showVisionSafetyError: jest.fn(),
  isVisionSafetyError: jest.fn(() => false),
}));

jest.mock('@/lib/storage', () => ({
  uploadFormulaPhoto: jest.fn(async () => 'https://example.com/photo.jpg'),
}));

jest.mock('@/lib/formula-prompts', () => ({
  getFormulaSystemPrompt: jest.fn(() => 'Sistema formula'),
  getFormulaUserPrompt: jest.fn(() => 'Usuario formula'),
  getChatSystemPrompt: jest.fn(() => 'System chat'),
  getQuickQuestions: jest.fn(() => []),
}));

jest.mock('@/lib/supabase-formulas', () => ({
  saveFormula: jest.fn(async () => ({ id: 'formula-1' })),
  extractProductsFromText: jest.fn(() => []),
  getLatestSessionNumber: jest.fn(async () => 1),
  estimateCost: jest.fn(() => ({ total: 0, breakdown: [] })),
}));

jest.mock('@/lib/supabase', () => ({
  supabase: {
    from: jest.fn().mockReturnThis(),
    insert: jest.fn().mockReturnThis(),
    select: jest.fn().mockReturnThis(),
    single: jest.fn().mockReturnThis(),
    upsert: jest.fn().mockReturnThis(),
    eq: jest.fn().mockReturnThis(),
  },
}));

beforeAll(() => {
  global.requestAnimationFrame = (cb: FrameRequestCallback): number => {
    cb(0);
    return 0;
  };
  LayoutAnimation.configureNext = jest.fn();
  jest.spyOn(Alert, 'alert').mockImplementation(() => undefined);
});

const originalDateNow = Date.now;

beforeEach(() => {
  jest.clearAllMocks();
  let timestamp = 1740686400000; // 2025-02-28T12:00:00Z
  Date.now = jest.fn(() => (timestamp += 1000));

  const expoRouter = require('expo-router');
  (expoRouter.useLocalSearchParams as jest.Mock).mockReturnValue({});
  (expoRouter.useRouter as jest.Mock).mockReturnValue({
    push: jest.fn(),
    back: jest.fn(),
    replace: jest.fn(),
  });

  mockUseChat.mockImplementation(() => {
    const [conversation, setConversation] = React.useState(() => ({
      id: 'conv-1',
      title: 'Consulta de prueba',
      messages: [
        {
          id: 'msg-1',
          role: 'user',
          content: 'Consulta inicial',
          timestamp: new Date('2025-02-28T11:58:00Z'),
        },
        {
          id: 'msg-2',
          role: 'assistant',
          content: 'Respuesta inicial',
          timestamp: new Date('2025-02-28T11:58:30Z'),
        },
      ],
      isPinned: false,
      createdAt: new Date('2025-02-28T11:50:00Z'),
      updatedAt: new Date('2025-02-28T11:58:30Z'),
      memorySummary: null,
    }));

    const addMessage = jest.fn(async (_conversationId: string, message: any) => {
      setConversation(prev => ({
        ...prev,
        messages: [...prev.messages, message],
        updatedAt: message.timestamp,
      }));
    });

    return {
      conversations: [conversation],
      currentConversation: conversation,
      currentConversationId: conversation.id,
      isLoading: false,
      startNewConversation: jest.fn(),
      selectConversation: jest.fn(),
      renameConversation: jest.fn(),
      togglePinConversation: jest.fn(),
      deleteConversation: jest.fn(),
      addMessage,
      refreshConversationMemory: jest.fn(),
    };
  });

});

afterEach(() => {
  Date.now = originalDateNow;
});

it('anchors the latest user message below the sticky header in ChatScreen', async () => {
  const { getByPlaceholderText, getByTestId, findAllByText } = render(<ChatScreen />);

  const input = getByPlaceholderText('Escribe tu consulta...');
  fireEvent.changeText(input, 'Necesito diagnóstico general');

  const sendButton = getByTestId('chat-send-button');
  await act(async () => {
    fireEvent.press(sendButton);
  });

  const userMessages = await findAllByText('Necesito diagnóstico general');
  const latestMessage = userMessages[userMessages.length - 1];
  fireEvent(latestMessage.parent ?? latestMessage, 'layout');

  await waitFor(() => {
    expect(mockScrollToIndex).toHaveBeenCalled();
  });

  const lastCall = mockScrollToIndex.mock.calls.at(-1);
  expect(lastCall?.[0]).toMatchObject({
    viewPosition: 0,
    viewOffset: 112,
  });
});

it('computes Step5 scroll targets and uses scrollToIndex to honor the anchor offset', () => {
  const pendingAnchorIdRef = { current: 'msg-123' } as MutableRefObject<string | null>;
  const chatContainerOffsetRef = { current: 90 } as MutableRefObject<number>;
  const scrollToIndex = jest.fn();
  const scrollToOffset = jest.fn();
  const scrollableRef = { current: { scrollToIndex, scrollToOffset } } as MutableRefObject<any>;

  const anchorMessagePosition = createStep5AnchorHandler({
    pendingAnchorIdRef,
    chatContainerOffsetRef,
    scrollableRef,
  });

  anchorMessagePosition('msg-123', 450, { index: 2 });

  expect(scrollToIndex).toHaveBeenCalledWith({
    index: 2,
    animated: true,
    viewPosition: 0,
    viewOffset: 24,
  });
  expect(pendingAnchorIdRef.current).toBeNull();

  anchorMessagePosition('msg-456', 480);
  expect(scrollToIndex).toHaveBeenCalledTimes(1);
});
