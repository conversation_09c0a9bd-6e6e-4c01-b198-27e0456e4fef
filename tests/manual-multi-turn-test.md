# 🧪 Prueba Manual: Multi-Turn Chat con Imágenes

## Objetivo
Verificar que la IA recuerda las imágenes enviadas en mensajes anteriores durante una conversación.

---

## Pre-requisitos

1. **Servidor corriendo**:
   ```bash
   bun run start-web
   ```

2. **Tener fotos de prueba listas** (pueden ser cualquier imagen, idealmente fotos de cabello)

3. **Navegador abierto** en: http://localhost:8081 (o el puerto que muestre Expo)

---

## 📝 Caso de Prueba 1: Memoria Básica de Imagen

### Paso 1: Enviar primera imagen
1. Ir al tab "Chat" 💬
2. Hacer clic en el botón de cámara 📷
3. Seleccionar una imagen
4. Escribir: **"Analiza este cabello y dime qué nivel de color tiene"**
5. Enviar

**Resultado esperado**:
- ✅ La imagen aparece en el mensaje
- ✅ La IA responde con un análisis (puede ser genérico si no es una foto real de cabello)

---

### Paso 2: Hacer pregunta de seguimiento SIN imagen
1. **NO adjuntar ninguna imagen**
2. Escribir solo texto: **"¿Qué fórmula me recomiendas para oscurecerlo un nivel?"**
3. Enviar

**Resultado esperado**:
- ✅ La IA debe hacer referencia a la imagen anterior
- ✅ Su respuesta debe ser específica basada en el "análisis" del paso 1
- ❌ NO debe decir "no veo ninguna imagen" o "necesito ver una foto"

**Verificación en consola**:
Abrir DevTools (F12) → Console, buscar:
```
[Chat] Conversation history: ...
[Chat] Images in context: 1
```

---

## 📝 Caso de Prueba 2: Múltiples Imágenes

### Paso 3: Enviar segunda imagen diferente
1. Hacer clic en el botón de cámara 📷
2. Seleccionar una imagen DIFERENTE
3. Escribir: **"¿Y este otro cabello qué nivel tiene?"**
4. Enviar

**Resultado esperado**:
- ✅ Nueva imagen aparece en el mensaje
- ✅ La IA analiza la nueva imagen

---

### Paso 4: Comparar ambas imágenes
1. **NO adjuntar imagen**
2. Escribir: **"Compara ambos cabellos que te mostré. ¿Cuál necesita más trabajo?"**
3. Enviar

**Resultado esperado**:
- ✅ La IA debe mencionar AMBAS imágenes anteriores
- ✅ Debe comparar los niveles o características de ambas
- ✅ En consola debería mostrar: `[Chat] Images in context: 2`

---

## 📝 Caso de Prueba 3: Límite de 3 Mensajes con Imágenes

### Paso 5: Enviar 4 imágenes más (total 6)
Enviar 4 mensajes seguidos, cada uno con una imagen:
1. **[Imagen 3]** "Analiza este"
2. **[Imagen 4]** "Y este"
3. **[Imagen 5]** "¿Qué opinas de este?"
4. **[Imagen 6]** "Último cabello"

---

### Paso 6: Verificar límite
1. Escribir: **"Resume todas las fotos que te mostré en esta conversación"**
2. Enviar

**Resultado esperado**:
- ✅ La IA solo debe recordar las últimas 3 imágenes (4, 5, 6)
- ✅ Las primeras 3 (1, 2, 3) deben aparecer como `[imagen analizada previamente]` en el historial
- ✅ En consola: `[Chat] Images in context: 3` (máximo)

**Verificación en código**:
Si inspeccionas el historial enviado a la IA (en consola), verás:
```json
[
  {"role": "user", "content": "Analiza este [imagen analizada previamente]"},
  {"role": "user", "content": [
    {"type": "text", "text": "Último cabello"},
    {"type": "image_url", "image_url": {"url": "https://..."}}
  ]}
]
```

---

## 📝 Caso de Prueba 4: Contexto de Clientes

### Paso 7: Verificar que la IA conoce tus clientes
1. Ir al tab "Clientes" 👥
2. Asegurarte de tener al menos 1 cliente con nombre (ej: "María")
3. Volver al tab "Chat" 💬
4. Escribir: **"¿Qué le puedo hacer a María?"**
5. Enviar

**Resultado esperado**:
- ✅ La IA debe reconocer el nombre "María"
- ✅ Si María tiene alergias registradas, debe mencionarlas
- ✅ Debe dar recomendaciones personalizadas

---

## 🔍 Verificaciones Técnicas

### Verificar Analytics
En la consola del navegador (F12), deberías ver:
```
[Analytics] Event tracked: chat_message_sent {
  has_images: true,
  image_count: 1,
  conversation_id: "...",
  message_length: 45
}
```

### Verificar en Base de Datos (Opcional)
Ejecuta en Supabase SQL Editor:
```sql
SELECT
  event_name,
  properties->>'has_images' as has_images,
  properties->>'image_count' as image_count,
  timestamp
FROM analytics_events
ORDER BY timestamp DESC
LIMIT 10;
```

**Resultado esperado**:
```
event_name           | has_images | image_count | timestamp
---------------------|------------|-------------|-------------------
chat_message_sent    | true       | 1           | 2025-10-23 18:30:00
chat_message_sent    | false      | 0           | 2025-10-23 18:29:45
chat_message_sent    | true       | 1           | 2025-10-23 18:29:30
```

---

## ✅ Checklist de Prueba

- [ ] **Prueba 1**: IA recuerda imagen en mensaje de seguimiento
- [ ] **Prueba 2**: IA compara múltiples imágenes
- [ ] **Prueba 3**: Límite de 3 imágenes en contexto funciona
- [ ] **Prueba 4**: IA reconoce clientes del salón
- [ ] **Analytics**: Eventos se registran en consola
- [ ] **Consola**: Logs muestran imágenes en contexto
- [ ] **Historial**: Imágenes aparecen en mensajes anteriores

---

## 🐛 Troubleshooting

### La IA no recuerda imágenes
**Revisar**:
1. Consola del navegador → buscar errores
2. Verificar que `[Chat] Images in context: N` aparece
3. Confirmar que signed URLs no expiraron (válidas 24h)

### Imágenes no se cargan
**Posibles causas**:
- Supabase Storage no configurado
- Variables de entorno faltantes
- Permisos de bucket incorrectos

**Solución**:
```bash
# Verificar env
cat .env.local | grep SUPABASE

# Verificar bucket existe
# Ir a: https://supabase.com/dashboard/project/guyxczavhtemwlrknqpm/storage/buckets
```

### Analytics no registra eventos
**Verificar**:
```sql
-- En Supabase SQL Editor
SELECT COUNT(*) FROM analytics_events;
```

Si devuelve 0, verificar que la migración se ejecutó:
```sql
SELECT tablename FROM pg_tables WHERE tablename = 'analytics_events';
```

---

## 📊 Resultados Esperados

Al completar todas las pruebas:

- ✅ **6 imágenes enviadas** (pero solo 3 en contexto activo)
- ✅ **8+ mensajes de chat** (con y sin imágenes)
- ✅ **8+ eventos de analytics** registrados
- ✅ **IA responde con contexto** de imágenes anteriores
- ✅ **0 errores en consola** del navegador

---

## 🎯 Criterios de Éxito

| Criterio | Pasa | Notas |
|----------|------|-------|
| IA recuerda 1 imagen | ☐ | |
| IA compara 2 imágenes | ☐ | |
| Límite de 3 funciona | ☐ | |
| Analytics registra eventos | ☐ | |
| Contexto de clientes funciona | ☐ | |
| Sin errores críticos | ☐ | |

---

**Fecha de prueba**: ___________
**Ejecutado por**: ___________
**Resultado general**: ⭐ PASS / ❌ FAIL

---

## 📝 Notas Adicionales

(Espacio para observaciones durante la prueba)
