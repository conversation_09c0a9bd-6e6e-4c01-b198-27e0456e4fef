# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

**Salonier AI** is a professional hair colorist assistant app built with React Native and Expo. The app helps hair stylists:
- Analyze hair color through photos using AI
- Create detailed color formulas with step-by-step workflows
- Manage client profiles with health/safety information
- <PERSON><PERSON> with an AI assistant for hair color expertise
- Track client history and chemical treatments

**Tech Stack:**
- React Native 0.79.1 with Expo SDK 53
- Expo Router (file-based routing with typed routes)
- TypeScript
- Supabase (PostgreSQL database)
- React Query (TanStack Query)
- Zustand (state management via contexts)
- Rork toolkit SDK (AI generation)
- NativeWind (styling)

## Development Commands

### Starting the App

```bash
# Install dependencies
bun i

# Start with web preview (recommended for quick testing)
bun run start-web

# Start with tunnel (for testing on physical devices)
bun run start

# Start with debug mode
bun run start-web-dev

# After starting, press 'i' to open iOS Simulator
```

### Linting

```bash
bun run lint
```

### Testing on Devices

- **iOS Simulator**: Run `bun run start`, then press `i` in terminal
- **Physical Device**: Use Expo Go app or Rork app, scan QR code from `bun run start`
- **Web**: Run `bun run start-web` and open in browser

## Architecture

### File-Based Routing

The app uses Expo Router with a file-based routing system located in `/app`:

- `app/(tabs)/` - Main tab navigation screens (clients, chat, library, settings)
- `app/formula/step*.tsx` - Multi-step formula creation workflow (steps 0-5)
- `app/clients/` - Client management screens (new, edit)
- `app/settings/` - Settings screens (profile, business, team, etc.)
- `app/_layout.tsx` - Root layout with context providers

### State Management

The app uses React Context + hooks pattern (via `@nkzw/create-context-hook`) for state:

**ClientContext** (`contexts/ClientContext.tsx`):
- Manages client CRUD operations with Supabase
- Loads clients on mount from `clients` table
- Provides: `clients`, `isLoading`, `addClient`, `updateClient`, `deleteClient`

**ChatContext** (`contexts/ChatContext.tsx`):
- Manages AI chat conversations with Supabase
- Stores conversations in `conversations` and `messages` tables
- Multi-device sync enabled (real-time persistence)
- Provides: `conversations`, `currentConversation`, `addMessage`, `startNewConversation`, etc.

**FormulaContext** (`contexts/FormulaContext.tsx`):
- Manages multi-step formula creation workflow
- Tracks images, analysis, safety checklist, client signature
- Provides: `formulaData`, `selectedClient`, `updateCurrentColorImages`, etc.

All contexts are wrapped in `app/_layout.tsx` with QueryClientProvider.

### Data Layer

**Supabase Integration** (`lib/supabase.ts`):
- PostgreSQL database for all persistent data
- Tables: `clients`, `conversations`, `messages`, `ai_usage_log`, `rate_limits`, `product_cache`
- Requires env vars: `EXPO_PUBLIC_SUPABASE_URL` and `EXPO_PUBLIC_SUPABASE_ANON_KEY`
- Migration files in `supabase/migrations/`
- All data syncs between local state and Supabase (multi-device ready)

### Formula Creation Workflow

The formula creation is a 6-step wizard:

0. **step0.tsx** - Client selection (from ClientContext)
1. **step1.tsx** - Current color analysis (photo upload + AI analysis)
2. **step2.tsx** - Desired color analysis (photo upload + AI analysis)
3. **step3.tsx** - Safety checklist (allergies, patch test, etc.)
4. **step4.tsx** - Brand/product line selection
5. **step5.tsx** - Final formula generation

Formula data flows through FormulaContext and includes:
- Current/desired color images
- Hair analysis (roots, mids, ends, gray analysis, chemical history)
- Safety checklist
- Client signature
- Brand/product preferences

### AI Integration

**AI Client**: `lib/ai-client.ts` - Cliente unificado para interacción con modelos AI (Rork toolkit SDK)

**Función principal**: `generateTextSafe()` - Genera respuestas AI con rate limiting, retry logic y error handling

**Uso en la app**:
- `app/(tabs)/chat.tsx` - Chat conversacional con análisis de imágenes
- `app/formula/step1.tsx` - Análisis de color actual
- `app/formula/step2.tsx` - Análisis de color deseado
- `app/formula/step5.tsx` - Generación de fórmulas y búsqueda de productos

**Features**:
- Soporte multi-modal (texto + imágenes)
- Rate limiting automático
- Retry con exponential backoff
- Logging de uso en Supabase (`ai_usage_log`, `rate_limits`)
- Búsqueda de productos con Perplexity API

### MCPs Instalados - Herramientas Especializadas

⚠️ **IMPORTANTE**: MCPs (Model Context Protocols) proporcionan capacidades especializadas. Úsalos PROACTIVAMENTE.

**Constante global para ejemplos**: `const P = "guyxczavhtemwlrknqpm";` (Project ID)

#### Supabase MCP - SIEMPRE usar para DB

**Capacidades**: `apply_migration`, `execute_sql`, `generate_typescript_types`, `get_logs`, `get_advisors`, `deploy_edge_function`, `list_edge_functions`, `create_branch`, `merge_branch`, `list_projects`, `list_tables`

**Workflow Post-Migración DDL (SIEMPRE ejecutar en orden)**:
```typescript
const P = "guyxczavhtemwlrknqpm";

// 1. Aplicar migración (NO usar comandos shell)
await mcp__supabase__apply_migration({ project_id: P, name: "add_table", query: "CREATE TABLE..." });

// 2. Validar seguridad SIEMPRE
const advisors = await mcp__supabase__get_advisors({ project_id: P, type: "security" });
if (advisors.some(a => a.message.includes('RLS'))) {
  await mcp__supabase__apply_migration({ project_id: P, name: "add_rls", query: "ALTER TABLE ... ENABLE ROW LEVEL SECURITY; CREATE POLICY..." });
}

// 3. Generar tipos actualizados
await mcp__supabase__generate_typescript_types({ project_id: P });

// 4. Pre-commit: validar performance
const perfAdvisors = await mcp__supabase__get_advisors({ project_id: P, type: "performance" });
```

**Debugging**: `await mcp__supabase__get_logs({ project_id: P, service: "postgres" });` (revisar ANTES de preguntar detalles)

**Branches** (features grandes DDL): `list_organizations()` → `get_cost()` → `confirm_cost()` → `create_branch()` → trabajar en branch → `merge_branch()`

**Edge Functions**: `list_edge_functions({ project_id: P })`, `get_edge_function({ project_id: P, function_slug })`, `deploy_edge_function({ project_id: P, name, files, entrypoint_path })`

#### Context7 MCP - Docs Actualizadas

**Usar cuando**: Preguntas sobre librerías externas, APIs post-enero 2025, versiones específicas (Expo SDK 53)

**Workflow**: `resolve_library_id({ libraryName })` → `get_library_docs({ context7CompatibleLibraryID: libs[0].id, topic, tokens: 5000 })`

**Librerías comunes**: `/expo/expo`, `/expo/expo-router`, `/expo/expo-image-picker`, `/supabase/supabase-js`, `/tanstack/query`

#### IDE MCP - Validación Pre-Commit

**SIEMPRE antes de commits**:
```typescript
const diagnostics = await mcp__ide__getDiagnostics();
if (diagnostics.some(d => d.severity === 'error')) { /* NO commitear */ }
```

**Post-refactor**: `await mcp__ide__getDiagnostics();` para verificar que nada se rompió

#### Playwright MCP - Testing Web (Opcional)

**Solo usar si**: Usuario pide validar flujos web, screenshots UI, debugging navegador, testing E2E

**Básico**: `browser_navigate({ url })` → `browser_snapshot()` → `browser_click({ element, ref })` → `browser_take_screenshot()` → `browser_close()`

### Type System

All types defined in `types/index.ts`:

**Key Types:**
- `Client` - Client profile with health/safety data
- `FormulaData` - Complete formula workflow state
- `HairAnalysis` - Detailed hair analysis (zones, gray, chemical history)
- `Message` - Chat message with optional images
- `TeamMember` - User roles and permissions
- `BusinessProfile` - Salon business settings

### Environment Setup

Required environment variables in `.env.local`:

```
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
```

The app will throw an error if these are missing (see `lib/supabase.ts:6-8`).

### Supabase Schema

The `clients` table schema (from migration `20251021170522_create_clients_table.sql`):
- Maps camelCase TypeScript to snake_case SQL
- Columns: id, name, email, phone, last_visit, photo, notes, known_allergies, etc.
- JSONB fields for `chemical_treatments` and `communication_preferences`

When reading/writing, use `parseClientDates()` helper to transform between formats (see `contexts/ClientContext.tsx:14-35`).

### Supabase Storage - Imágenes de Clientes

**Bucket**: `client-photos` (privado, AES-256, retención 90 días, EU West/GDPR)

**Seguridad**: Cifrado AES-256 + TLS 1.2+, RLS (solo uploader accede), Signed URLs (1h exp), consentimiento explícito requerido

**Estructura**: `client-photos/{userId}/{clientId}/timestamp-random.jpg`

**API**: `uploadClientPhoto(imageUri, clientId)`, `deleteClientPhoto(storagePath)`, `getSignedUrl(storagePath, 3600)` (ver `@/lib/storage`)

**Compliance GDPR**: Consentimiento en `step3.tsx` (`photoConsentGiven` en `SafetyChecklist`). Auto-cleanup: `cleanup_old_client_photos()` (>90 días)

**Migraciones**: `20251023_create_client_photos_bucket.sql`, `20251023_create_photo_cleanup_function.sql`

## Important Patterns

### Context Provider Pattern

All contexts use `@nkzw/create-context-hook` which combines provider + hook:

```typescript
export const [ProviderName, useHook] = createContextHook(() => {
  // context logic
  return useMemo(() => ({ ...exports }), [deps]);
});
```

### Navigation

Use `expo-router` hooks:
- `useRouter()` - for programmatic navigation via `router.push()`
- Routes are typed (enabled via `experiments.typedRoutes` in `app.json`)

### Styling

- Uses React Native StyleSheet API
- Color constants in `constants/colors.ts` (not constants/Colors.ts)
- Safe area insets via `useSafeAreaInsets()` for iOS notch/home indicator

### Image Handling

- `expo-image-picker` for photo selection
- `expo-file-system` for base64 conversion
- Multiple image selection supported (up to 6 images)

## Common Workflows

### Adding New Screens/Tables
- **Client Screen**: Crear en `app/clients/` → agregar a `_layout.tsx` → usar `useClients()` + `useRouter()`
- **Formula Step**: Crear `app/formula/stepN.tsx` → actualizar `FormulaContext` + `types/index.ts`
- **Supabase Table**: Ver workflow completo en sección "MCPs - Supabase" arriba (apply_migration → advisors → generate_types)

### AI Workflows
- **Chat**: Usar `generateTextSafe()` con system prompts especializados
- **Formula Creation**: `step1.tsx` → análisis de color actual, `step2.tsx` → análisis de color deseado, `step5.tsx` → generación de fórmula
- **Safety Check**: Validar `safetyChecklist` en `step3.tsx` antes de proceder con químicos

## Deployment

**Mobile Apps:**
```bash
# Install EAS CLI
bun i -g @expo/eas-cli

# Build for iOS/Android
eas build --platform ios
eas build --platform android

# Submit to stores
eas submit --platform ios
eas submit --platform android
```

**Web:**
```bash
eas build --platform web
eas hosting:configure
eas hosting:deploy
```

## Platform-Specific Notes

**iOS:**
- Camera/photo permissions in `app.json` infoPlist
- Bundle ID: `app.rork.coloraimaster`

**Android:**
- Permissions in `app.json` android.permissions
- Package: `app.rork.coloraimaster`

**Web:**
- Some native features unavailable (camera, native auth)
- Browser preview: `bun run start-web`

## Reglas de Desarrollo

### Archivos y Carpetas Protegidas
- **NUNCA** modifiques archivos en `node_modules/`
- **NUNCA** toques `.git/` directamente
- **EVITA** crear archivos `.md` innecesarios (READMEs genéricos, notas temporales, etc.)
  - Excepción: Documentación de sesiones (ver sección abajo)

### Antes de Commits
```bash
# SIEMPRE ejecuta lint antes de crear commits
bun run lint

# Verifica que la app inicie sin errores
bun run start-web
```

**Nota**: Este proyecto NO tiene tests configurados actualmente.

### Preferencias de Código
- **SIEMPRE edita componentes existentes** antes de crear nuevos
- **REUTILIZA** componentes en `components/` cuando sea posible
- **MANTÉN** consistencia con patrones existentes (Context hooks, StyleSheet, etc.)
- **RESPETA** la estructura de carpetas establecida (`app/`, `contexts/`, `types/`, etc.)

### Reglas de MCPs (OBLIGATORIO)

⚠️ **Ver sección "MCPs Instalados" arriba para detalles completos**

**Resumen MCPs**:
- ✅ Supabase MCP: `apply_migration` → `get_advisors` → `generate_typescript_types` (workflow completo arriba)
- ✅ IDE MCP: `getDiagnostics()` ANTES de commits
- ❌ NO usar comandos shell para migraciones ni ignorar advisors

### Uso de TodoWrite (Importante)

**NO uses TodoWrite para:**
- Lecturas simples de 1-2 archivos
- Comandos únicos (`git status`, `bun run lint`)
- Tareas triviales que se completan en 1 paso
- Responder preguntas del usuario

**USA TodoWrite SOLO para:**
- Tareas complejas con **3+ pasos interdependientes**
- Workflows multi-archivo (ej: crear nueva tabla Supabase + context + tipos + UI)
- Debugging complejo que requiere múltiples intentos
- Implementación de features completas (ej: nueva pantalla con navegación, estado, y persistencia)

**Ejemplo válido de TodoWrite:**
```
Implementar sistema de notificaciones:
1. Crear migration de tabla notifications
2. Actualizar types/index.ts con tipo Notification
3. Crear NotificationContext
4. Agregar NotificationContext a _layout.tsx
5. Crear pantalla settings/notifications.tsx
6. Agregar navegación en Stack
7. Implementar UI de notificaciones
```

## Documentación de Sesiones

**Ubicación**: `sessions/YYYY-MM-DD-descripcion-breve.md`
**Antes de trabajar**: Leer últimas 3-5 sesiones (implementaciones recientes, problemas resueltos, decisiones técnicas, TODOs)

**Template básico**:
```markdown
# [Título Feature/Fix]
**Última actualización**: YYYY-MM-DD HH:mm

## Contexto
[Problema/feature y por qué es necesario]

## Cambios Realizados
- `archivo.tsx` - [Cambio]

## Problemas y Soluciones
**Error**: [Mensaje]
**Causa**: [Por qué ocurrió]
**Solución**: [Cómo se resolvió]

## Decisiones Técnicas
**¿Por qué X vs Y?**: [Razón, trade-offs]

## TODOs
- [ ] [Pendiente]
```

**Actualizar sesión existente**: Agregar `## Update: YYYY-MM-DD HH:mm` al final
**Nueva sesión**: Si es tema nuevo, >1 semana desde última sesión, o cambios independientes

## Flujo Git/GitHub

**Contexto**: Proyecto integrado con Rork (commits automáticos desde rork.com). Cambios locales pusheados a GitHub se reflejan en Rork.

**Workflow completo**:
```bash
# 1. Setup
git status && git branch                              # Verificar estado/rama
git checkout main && git pull origin main             # Actualizar main

# 2. Crear rama
git checkout -b fix/descripcion                       # Rama descriptiva

# 3. Desarrollo
bun run lint && bun run start-web                     # Verificar antes de commit

# 4. Commit
git add archivo.tsx                                   # Agregar específicos (recomendado)
git commit -m "Fix: Título\n\n- Detalle 1\n- Detalle 2"  # Primera línea 50-70 chars

# 5. Push y PR
git push -u origin nombre-rama                        # Primera vez
gh pr create --title "..." --body "..."               # O crear en GitHub web

# 6. Post-merge
git checkout main && git pull origin main             # Actualizar
git branch -d nombre-rama                             # Limpiar rama local
```

**❌ NUNCA**: `git push --force`, `git reset --hard HEAD~1` (sin backup), `git rebase -i` (en rama compartida), `git merge main` (estando en main)

**Emergencias**: `git stash` (guardar temporal), `git reset --soft HEAD~1` (deshacer commit), `git log --oneline --graph --all`

**Conflictos**: `git status` → editar archivos (`<<<<<<<` markers) → `git add archivo.tsx` → `git commit -m "Merge: ..."`

## Al Finalizar Sesión de Trabajo

**Checklist**:
1. **MCPs**: Si cambios DB → `get_advisors` (security+performance) + `generate_typescript_types`. Si código → `getDiagnostics()` (NO commitear si hay errors)
2. **Quality**: `bun run lint` (obligatorio) + `bun run start-web` (verificar que inicia)
3. **Docs**: Si implementación significativa/bug complejo/decisiones técnicas → actualizar `sessions/`
4. **Git**: Commits descriptivos → push a rama feature → PR → limpiar rama post-merge
5. **Equipo**: Notificar cambios importantes, actualizar issues, mencionar breaking changes

**❌ NO**: `git push --force`, dejar archivos temporales, commitear credenciales, código comentado excesivo, PRs gigantes

**Resumen recomendado**: Cambios principales, archivos modificados, lint status, sesión documentada, rama/commits/PR, próximos pasos

## Supabase - Base de Datos

**Project ID**: `guyxczavhtemwlrknqpm` | **Región**: EU West (Paris) | **URL**: https://guyxczavhtemwlrknqpm.supabase.co

**Credenciales** (`.env.local`, NO commitear): `EXPO_PUBLIC_SUPABASE_URL`, `EXPO_PUBLIC_SUPABASE_ANON_KEY`, `SUPABASE_SERVICE_ROLE_KEY` (admin), `SUPABASE_DB_PASSWORD` (PostgreSQL)

**Migraciones**: SIEMPRE usar Supabase MCP (ver sección MCPs arriba). Alternativos: `supabase db push` (terminal) o SQL Editor web

**Tablas**: `clients`, `conversations`, `messages`

**Docs completas**: `sessions/2025-10-21-supabase-credentials-setup.md`

## Claude Code Agents - Herramientas de Desarrollo

**Ubicación**: `.claude/agents/` | **Docs**: `sessions/2025-10-23-claude-code-agents-setup.md`

### 11 Agentes Principales + 4 Subagentes

**Orquestación:**
1. **Salonier Orchestrator** - CEO que decide qué agentes usar y coordina trabajo paralelo

**Desarrollo:**
2. **Code Reviewer** - Reviews de código, bugs, type safety, best practices
3. **React Native Specialist** - Experto en React Native/Expo SDK 53, hooks, navigation
4. **Supabase Specialist** - Experto en DB, migraciones DDL, RLS, storage, edge functions
   - 🔧 *Subagente*: **RLS Policy Validator** - Validación de seguridad RLS
   - 🔧 *Subagente*: **TypeScript Type Generator** - Generación de tipos desde schema

**Calidad & Seguridad:**
5. **Test Engineer** - Test strategy y tests (CRÍTICO: proyecto sin tests)
6. **Security Reviewer** - Security audits, vulnerabilities, GDPR compliance
7. **Performance Optimizer** - Performance analysis, bundle size, memory leaks
   - 🔧 *Subagente*: **Context Re-render Analyzer** - Análisis de re-renders
   - 🔧 *Subagente*: **Bundle Size Analyzer** - Análisis de tamaño de bundle
   - 🔧 *Subagente*: **FlatList Optimizer** - Optimización de listas
8. **UX Reviewer** - UX/UI review, a11y, mobile best practices

**Planificación & Coordinación:**
9. **Tech Lead** - Decisiones arquitectónicas, trade-offs, refactoring
10. **Mobile Release Coordinator** - Preparación de releases iOS/Android/Web (EAS)
11. **Docs Maintainer** - Mantenimiento de documentación en sessions/

### Uso Proactivo de Agentes

**Sintaxis**: `@Agent-Name <petición>` o uso del Task tool en código

**Ejemplos**:
```
@Code-Reviewer revisa app/(tabs)/chat.tsx
@Salonier-Orchestrator Pre-release review para versión 1.1.0
@React-Native-Specialist revisa uso de FlatList en chat
```

### Workflows Comunes (Cuándo Usar Qué Agente)

#### 1. Nueva Feature/Pantalla
```
PASO 1 - PLANIFICACIÓN (paralelo):
  @Tech-Lead - Decisión arquitectónica
  @React-Native-Specialist - Patrones de implementación
  @UX-Reviewer - Diseño de UX/UI

PASO 2 - IMPLEMENTACIÓN:
  [Implementar código]

PASO 3 - REVIEW (paralelo):
  @Code-Reviewer - Quality review
  @Security-Reviewer - Si hay datos sensibles
  @Performance-Optimizer - Si hay listas/imágenes

PASO 4 - TESTING:
  @Test-Engineer - Diseño de tests

PASO 5 - DOCUMENTACIÓN:
  @Docs-Maintainer - Actualizar sessions/
```

#### 2. Cambio de Base de Datos (Migration)
```
PASO 1 - DISEÑO:
  @Supabase-Specialist - Diseño de migración DDL

PASO 2 - IMPLEMENTACIÓN:
  [Aplicar migración con Supabase MCP]

PASO 3 - VALIDACIÓN (paralelo):
  @Supabase-Specialist - Verificar indexes, performance
  @Security-Reviewer - Validar RLS policies
  @Code-Reviewer - Revisar integración TypeScript

PASO 4 - DOCUMENTACIÓN:
  @Docs-Maintainer - Documentar migración y decisiones
```

#### 3. Pre-Pull Request
```
PARALELO:
  @Code-Reviewer - SIEMPRE
  @Security-Reviewer - Si toca auth/datos/fotos/AI
  @Performance-Optimizer - Si cambios grandes
  @Test-Engineer - Validar cobertura de tests
```

#### 4. Pre-Release (iOS/Android/Web)
```
PASO 1 - QUALITY GATE (paralelo):
  @Test-Engineer - Validar tests críticos
  @Security-Reviewer - Audit completo
  @Performance-Optimizer - Bundle size, memory
  @UX-Reviewer - Audit UX/a11y

PASO 2 - PREPARACIÓN:
  @Mobile-Release-Coordinator - Builds, store submission

PASO 3 - DOCUMENTACIÓN:
  @Docs-Maintainer - Release notes, changelog
```

#### 5. Refactoring Arquitectónico
```
PASO 1 - PLANIFICACIÓN:
  @Tech-Lead - Estrategia de refactoring
  @Salonier-Orchestrator - Coordinar equipo de agentes

PASO 2 - IMPLEMENTACIÓN:
  [Refactorizar código]

PASO 3 - VALIDACIÓN (paralelo):
  @Code-Reviewer - Correctness
  @Performance-Optimizer - Performance comparison
  @Test-Engineer - Cobertura de tests
  @React-Native-Specialist - Patrones móviles

PASO 4 - DOCUMENTACIÓN:
  @Docs-Maintainer - Documentar decisiones y trade-offs
```

### Ejecución en Paralelo (Maximizar Eficiencia)

**SIEMPRE en paralelo cuando sea posible**:
```typescript
// ✅ CORRECTO: Usar Task tool con múltiples agentes en PARALELO
// Invocar en UN SOLO mensaje con múltiples Task calls

// Ejemplo: Pre-PR review
@Salonier-Orchestrator: Ejecuta pre-PR review en paralelo:
  - code-reviewer para app/formula/step3.tsx
  - security-reviewer para validación de checklist de seguridad
  - performance-optimizer para FlatList optimization

// Esto invocará 3 agentes simultáneamente
```

**Casos de paralelo común**:
- **Code-Reviewer + Security-Reviewer** - Auth/datos/fotos
- **Code-Reviewer + Performance-Optimizer** - Refactors
- **Security + Performance + Test** - Pre-release
- **React-Native-Specialist + UX-Reviewer** - Nueva UI
- **Supabase-Specialist + Security-Reviewer** - Migraciones DB

**Casos secuenciales** (dependencias):
- Tech-Lead → Implementación → Code-Reviewer
- Supabase-Specialist (migración) → Security (RLS) → Code-Reviewer (tipos)
- AI-System-Specialist → Test-Engineer → Code-Reviewer

### Orquestador de Agentes (Salonier Orchestrator)

**Cuándo usar**: Tareas complejas que requieren múltiples agentes

**El orquestador decidirá**:
- Qué agentes se necesitan
- En qué orden ejecutarlos
- Cuáles pueden trabajar en paralelo
- Qué validaciones hacer

**Ejemplo**:
```
@Salonier-Orchestrator Implementar feature de exportación PDF de fórmulas

El orquestador:
1. Analizará la feature
2. Decidirá agentes necesarios (React-Native + Supabase + Code-Reviewer + UX)
3. Coordinará ejecución (React Native + UX en paralelo → implementación → Code Review)
4. Validará resultados
```

### Ventajas del Sistema de Agentes

- **Expertise especializado** - Cada agente es experto en su área
- **Consistencia** - Todos conocen el proyecto Salonier AI
- **Paralelismo** - Múltiples agentes simultáneos = más rápido
- **Quality gates** - Validaciones automáticas antes de merge/release
- **Documentación** - Decisiones técnicas documentadas automáticamente
- **MCPs integrados** - Agentes usan Supabase/Context7/IDE MCPs proactivamente

### Subagentes (Invocados Automáticamente)

Los subagentes son especialistas ultra-enfocados invocados automáticamente por agentes principales:

**Ubicación**: `.claude/agents/subagents/`

#### 1. RLS Policy Validator
- **Parent**: Supabase Specialist
- **Invocado cuando**: Migraciones DB, cambios en tablas
- **Valida**: RLS habilitado, políticas correctas, sin bypasses
- **Crítico**: Tablas con datos de usuarios DEBEN tener RLS

#### 2. TypeScript Type Generator
- **Parent**: Supabase Specialist
- **Invocado cuando**: Cambios de schema DB
- **Genera**: Tipos TypeScript desde Supabase (snake_case → camelCase)
- **Actualiza**: `types/index.ts`

#### 3. FlatList Optimizer
- **Parent**: React Native Specialist
- **Invocado cuando**: FlatList con >50 items, listas lentas
- **Optimiza**: renderItem, keyExtractor, getItemLayout, virtualization
- **Target**: 60fps, <16ms por item

#### 4. Context Re-render Analyzer
- **Parent**: Performance Optimizer
- **Invocado cuando**: Re-renders excesivos, context changes
- **Detecta**: Context value re-creation, missing memoization
- **Optimiza**: useMemo, useCallback, context splitting

#### 5. Bundle Size Analyzer
- **Parent**: Performance Optimizer
- **Invocado cuando**: Pre-release, dependencias nuevas
- **Analiza**: Tamaño total, dependencias grandes, lazy loading
- **Target**: <50MB total, <5MB JS

**Nota**: Los subagentes NO se invocan manualmente. Se ejecutan automáticamente cuando su agente principal los necesita.

### Reglas Importantes

1. **SIEMPRE usar @Code-Reviewer antes de PR**
2. **SIEMPRE usar @Security-Reviewer para auth/datos/fotos**
3. **SIEMPRE usar @Supabase-Specialist para migraciones DB** (NO comandos shell)
   - Auto-invocará: RLS Policy Validator + TypeScript Type Generator
4. **SIEMPRE usar @Test-Engineer para features** (proyecto sin tests = crítico)
5. **SIEMPRE paralelizar** cuando agentes no dependen entre sí
6. **USAR @Salonier-Orchestrator** para tareas complejas (decidirá workflow)
7. **Documentar con @Docs-Maintainer** después de features/decisiones significativas

