# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo
.vercel

# Supabase
supabase/.temp/
.env

# Cursor MCP config (contains access tokens)
.cursor/

# Deployment scripts with secrets (use template files instead)
configure-secrets.sh
configure-edge-secrets.sh

# Test files (may contain temporary credentials)
test-*.js
test-*.ts

# Temporary documentation files
*_TEMP.md
*_OLD.md
*_BACKUP.md

# Script outputs
*.log
logs/
.test-token
/*.mjs

# Test coverage
coverage/
*.lcov
clover.xml
coverage-final.json
lcov.info
