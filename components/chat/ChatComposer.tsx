import React, { useMemo } from 'react';
import {
  ActivityIndicator,
  StyleSheet,
  TextInput,
  TouchableOpacity,
  View,
  type TextStyle,
  type ViewStyle,
} from 'react-native';
import { Plus, Send } from 'lucide-react-native';
import Colors from '@/constants/colors';

type ChatComposerVariant = 'chat' | 'formula';

interface ChatComposerProps {
  value: string;
  placeholder: string;
  onChangeText: (text: string) => void;
  onSend: () => void;
  disabled?: boolean;
  isSending?: boolean;
  showSendingIndicator?: boolean;
  maxLength?: number;
  onPressAttachment?: () => void;
  onLongPressAttachment?: () => void;
  onFocusInput?: () => void;
  sendButtonTestID?: string;
  variant?: ChatComposerVariant;
}

type VariantStyleOverrides = {
  container?: ViewStyle;
  attachmentButton?: ViewStyle;
  attachmentButtonDisabled?: ViewStyle;
  input?: TextStyle;
  sendButton?: ViewStyle;
  sendButtonDisabled?: ViewStyle;
};

const variantStyles: Record<ChatComposerVariant, VariantStyleOverrides> = {
  chat: {},
  formula: {
    container: {
      gap: 10,
      borderRadius: 34,
    },
    attachmentButton: {
      backgroundColor: Colors.light.card,
    },
    input: {
      backgroundColor: Colors.light.background,
      borderRadius: 16,
      paddingHorizontal: 4,
      fontSize: 16,
    },
    sendButton: {
      width: 50,
      height: 50,
      borderRadius: 25,
    },
    sendButtonDisabled: {
      opacity: 0.42,
    },
  },
};

const DEFAULT_MAX_LENGTH = 500;

const ChatComposer: React.FC<ChatComposerProps> = ({
  value,
  placeholder,
  onChangeText,
  onSend,
  disabled = false,
  isSending = false,
  showSendingIndicator = false,
  maxLength = DEFAULT_MAX_LENGTH,
  onPressAttachment,
  onLongPressAttachment,
  onFocusInput,
  sendButtonTestID,
  variant = 'chat',
}) => {
  const overrides = useMemo(() => variantStyles[variant], [variant]);

  const isAttachmentDisabled = !onPressAttachment;
  const isSendDisabled = disabled || isSending;
  const shouldShowSpinner = showSendingIndicator && isSending;

  const handleSend = () => {
    if (!isSendDisabled) {
      onSend();
    }
  };

  return (
    <View style={[styles.container, overrides.container]}>
      <TouchableOpacity
        accessibilityRole="button"
        style={[
          styles.attachmentButton,
          overrides.attachmentButton,
          isAttachmentDisabled && styles.attachmentButtonDisabled,
          isAttachmentDisabled && overrides.attachmentButtonDisabled,
        ]}
        onPress={onPressAttachment}
        onLongPress={onLongPressAttachment}
        disabled={isAttachmentDisabled}
      >
        <Plus color={Colors.light.primary} size={24} />
      </TouchableOpacity>

      <TextInput
        style={[styles.input, overrides.input]}
        value={value}
        onChangeText={onChangeText}
        placeholder={placeholder}
        placeholderTextColor={Colors.light.textTertiary}
        multiline
        maxLength={maxLength}
        textAlignVertical="top"
        onFocus={onFocusInput}
        accessibilityLabel="Chat input"
        returnKeyType="send"
        onSubmitEditing={handleSend}
        enablesReturnKeyAutomatically={true}
      />

      <TouchableOpacity
        accessibilityRole="button"
        testID={sendButtonTestID}
        style={[
          styles.sendButton,
          overrides.sendButton,
          isSendDisabled && styles.sendButtonDisabled,
          isSendDisabled && overrides.sendButtonDisabled,
        ]}
        onPress={handleSend}
        disabled={isSendDisabled}
      >
        {shouldShowSpinner ? (
          <ActivityIndicator size="small" color={Colors.light.background} />
        ) : (
          <Send color={Colors.light.background} size={22} />
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    backgroundColor: Colors.light.background,
    borderRadius: 36,
    borderWidth: 1.2,
    borderColor: Colors.light.border,
    paddingHorizontal: 22,
    paddingVertical: 18,
    minHeight: 82,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 8,
    elevation: 4,
  },
  attachmentButton: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.light.backgroundSecondary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.04,
    shadowRadius: 2,
  },
  attachmentButtonDisabled: {
    opacity: 0.4,
  },
  input: {
    flex: 1,
    fontSize: 17,
    lineHeight: 28,
    color: Colors.light.text,
    maxHeight: 160,
    paddingVertical: 0,
    paddingHorizontal: 4,
  },
  sendButton: {
    width: 52,
    height: 52,
    borderRadius: 26,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 5,
  },
  sendButtonDisabled: {
    opacity: 0.4,
  },
});

export default ChatComposer;
