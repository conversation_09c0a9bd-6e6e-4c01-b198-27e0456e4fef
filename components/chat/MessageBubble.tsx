import React from 'react';
import {
  Image,
  StyleSheet,
  Text,
  View,
  type ImageStyle,
  type LayoutChangeEvent,
  type TextStyle,
  type ViewStyle,
} from 'react-native';
import Colors from '@/constants/colors';
import FormattedMessageContent from '@/components/chat/FormattedMessageContent';
import type { Message } from '@/types';

type MessageBubbleVariant = 'chat' | 'formula';

interface MessageBubbleProps {
  message: Message;
  variant?: MessageBubbleVariant;
  onLayout?: (event: LayoutChangeEvent) => void;
  onImageError?: (uri: string, error?: string) => void;
}

type VariantStyleOverrides = {
  container?: ViewStyle;
  containerUser?: ViewStyle;
  imagesWrapper?: ViewStyle;
  imagesWrapperUser?: ViewStyle;
  imageContainer?: ViewStyle;
  imageContainerMultiple?: ViewStyle;
  image?: ImageStyle;
  imageMultiple?: ImageStyle;
  bubble?: ViewStyle;
  bubbleUser?: ViewStyle;
  bubbleAssistant?: ViewStyle;
  timestamp?: TextStyle;
  timestampUser?: TextStyle;
};

const variantStyles: Record<MessageBubbleVariant, VariantStyleOverrides> = {
  chat: {},
  formula: {
    container: { marginBottom: 12 },
    imagesWrapper: { maxWidth: '85%', gap: 6 },
    imageContainer: {
      backgroundColor: Colors.light.backgroundSecondary,
    },
    image: {
      width: 120,
      height: 120,
    },
    imageMultiple: {
      width: 120,
      height: 120,
    },
    bubble: {
      borderRadius: 16,
      padding: 12,
    },
    bubbleAssistant: {
      backgroundColor: Colors.light.card,
      borderWidth: 0,
      shadowOpacity: 0,
      elevation: 0,
    },
    bubbleUser: {
      backgroundColor: Colors.light.primary,
    },
    timestamp: {
      color: Colors.light.textSecondary,
    },
    timestampUser: {
      color: `${Colors.light.background}CC`,
    },
  },
};

const sanitizeImages = (message: Message): string[] => {
  const raw = [...(message.images ?? []), message.image].filter(Boolean) as string[];
  return raw
    .map(uri => uri.trim())
    .filter(uri => uri.length > 0);
};

const MessageBubble: React.FC<MessageBubbleProps> = ({
  message,
  variant = 'chat',
  onLayout,
  onImageError,
}) => {
  const isUser = message.role === 'user';
  const images = sanitizeImages(message);
  const overrides = variantStyles[variant];

  return (
    <View
      style={[
        styles.container,
        overrides.container,
        isUser && styles.containerUser,
        isUser && overrides.containerUser,
      ]}
      onLayout={onLayout}
    >
      {images.length > 0 && (
        <View
          style={[
            styles.imagesWrapper,
            overrides.imagesWrapper,
            isUser && styles.imagesWrapperUser,
            isUser && overrides.imagesWrapperUser,
          ]}
        >
          {images.map((uri, index) => (
            <View
              key={`${message.id}-img-${index}`}
              style={[
                styles.imageContainer,
                overrides.imageContainer,
                images.length > 1 && styles.imageContainerMultiple,
                images.length > 1 && overrides.imageContainerMultiple,
              ]}
            >
              <Image
                source={{ uri }}
                style={[
                  styles.image,
                  overrides.image,
                  images.length > 1 && styles.imageMultiple,
                  images.length > 1 && overrides.imageMultiple,
                ]}
                resizeMode="cover"
                onError={event => {
                  onImageError?.(uri, event?.nativeEvent?.error);
                }}
              />
            </View>
          ))}
        </View>
      )}

      {message.content && (
        <View
          style={[
            styles.bubble,
            overrides.bubble,
            isUser ? styles.bubbleUser : styles.bubbleAssistant,
            isUser
              ? overrides.bubbleUser
              : overrides.bubbleAssistant,
          ]}
        >
          <FormattedMessageContent
            content={message.content}
            tone={isUser ? 'user' : 'assistant'}
          />
          <Text
            style={[
              styles.timestamp,
              overrides.timestamp,
              isUser && styles.timestampUser,
              isUser && overrides.timestampUser,
            ]}
          >
            {message.timestamp.toLocaleTimeString('es-ES', {
              hour: '2-digit',
              minute: '2-digit',
            })}
          </Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'column',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  containerUser: {
    alignItems: 'flex-end',
  },
  imagesWrapper: {
    maxWidth: '80%',
    marginBottom: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    alignItems: 'flex-start',
  },
  imagesWrapperUser: {
    alignSelf: 'flex-end',
  },
  imageContainer: {
    borderRadius: 12,
    overflow: 'hidden',
    backgroundColor: Colors.light.background,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 3,
    elevation: 1,
  },
  imageContainerMultiple: {
    width: 120,
    height: 120,
  },
  image: {
    width: 140,
    height: 140,
  },
  imageMultiple: {
    width: 120,
    height: 120,
  },
  bubble: {
    maxWidth: '92%',
    borderRadius: 24,
    padding: 16,
    gap: 8,
  },
  bubbleUser: {
    backgroundColor: Colors.light.primary,
    borderBottomRightRadius: 6,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 2,
    alignSelf: 'flex-end',
  },
  bubbleAssistant: {
    backgroundColor: Colors.light.background,
    borderBottomLeftRadius: 6,
    borderWidth: 1,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.03,
    shadowRadius: 4,
    elevation: 1,
    alignSelf: 'flex-start',
  },
  timestamp: {
    fontSize: 11,
    color: Colors.light.textLight,
    alignSelf: 'flex-end',
  },
  timestampUser: {
    color: Colors.light.background,
    opacity: 0.7,
  },
});

export default MessageBubble;
