import React, { useMemo } from 'react';
import { View, Text, StyleSheet, Platform } from 'react-native';
import Colors from '@/constants/colors';

type Block =
  | { type: 'heading'; text: string; level: number }
  | { type: 'paragraph'; text: string }
  | { type: 'bulletList'; items: string[] }
  | { type: 'orderedList'; items: string[] }
  | { type: 'divider' }
  | { type: 'callout'; text: string; variant: 'info' | 'danger' | 'success' | 'tip' };

interface Props {
  content: string;
  tone?: 'assistant' | 'user';
}

const boldRegex = /\*\*(.*?)\*\*/g;
const codeRegex = /`([^`]+)`/g;

const normalizeText = (text: string) =>
  text.replace(/\s+/g, ' ').trim();

const createBlocks = (raw: string): Block[] => {
  const lines = raw.split(/\r?\n/);
  const blocks: Block[] = [];

  let currentList: { type: 'bulletList' | 'orderedList'; items: string[] } | null = null;
  let pendingParagraph = '';

  const flushParagraph = () => {
    if (pendingParagraph.trim().length > 0) {
      blocks.push({ type: 'paragraph', text: pendingParagraph.trim() });
    }
    pendingParagraph = '';
  };

  const flushList = () => {
    if (currentList && currentList.items.length > 0) {
      blocks.push({ type: currentList.type, items: currentList.items });
    }
    currentList = null;
  };

  lines.forEach((line) => {
    const trimmed = line.trim();

    if (!trimmed) {
      flushParagraph();
      flushList();
      return;
    }

    if (/^[-=*]{3,}$/.test(trimmed) || trimmed.includes('━━━━━━━━')) {
      flushParagraph();
      flushList();
      blocks.push({ type: 'divider' });
      return;
    }

    // Soporte para headings H1-H4 (# a ####)
    const headingMatch = trimmed.match(/^(#{1,4})\s*(.*)$/);
    if (headingMatch) {
      flushParagraph();
      flushList();

      const level = Math.min(headingMatch[1].length, 4);
      const text = headingMatch[2].trim();
      if (text.length > 0) {
        blocks.push({ type: 'heading', text, level });
      }
      return;
    }

    if (/^[📌📋📸⚙️🧪🔬💡✅⚠️⭐️]+/.test(trimmed)) {
      flushParagraph();
      flushList();

      const upper = trimmed.toUpperCase();
      let variant: 'info' | 'danger' | 'success' | 'tip' = 'info';
      if (upper.includes('⚠️')) variant = 'danger';
      else if (upper.includes('✅')) variant = 'success';
      else if (upper.includes('💡')) variant = 'tip';

      blocks.push({ type: 'callout', text: trimmed, variant });
      return;
    }

    // Soporte para bullets: -, •, ·, ▪︎, ‣, *, +
    const bulletMatch = trimmed.match(/^[-•·▪︎‣*+]\s+(.*)$/);
    if (bulletMatch) {
      flushParagraph();

      if (!currentList || currentList.type !== 'bulletList') {
        flushList();
        currentList = { type: 'bulletList', items: [] };
      }

      const item = normalizeText(bulletMatch[1]);
      if (item.length > 0) {
        currentList.items.push(item);
      }
      return;
    }

    const orderedMatch = trimmed.match(/^(\d+)[\.\)]\s+(.*)$/);
    if (orderedMatch) {
      flushParagraph();

      if (!currentList || currentList.type !== 'orderedList') {
        flushList();
        currentList = { type: 'orderedList', items: [] };
      }

      const item = normalizeText(orderedMatch[2]);
      if (item.length > 0) {
        currentList.items.push(item);
      }
      return;
    }

    flushList();

    if (pendingParagraph.length > 0) {
      pendingParagraph += ' ';
    }
    pendingParagraph += trimmed;
  });

  flushParagraph();
  flushList();

  if (blocks.length === 0 && raw.trim().length > 0) {
    blocks.push({ type: 'paragraph', text: raw.trim() });
  }

  return blocks;
};

const renderInlineText = (text: string, tone: Props['tone']) => {
  // Primero procesamos negritas, luego código inline
  const elements: React.ReactNode[] = [];
  let globalIndex = 0;

  // Procesar todas las matches de bold y code
  const matches: { type: 'bold' | 'code'; start: number; end: number; content: string }[] = [];

  // Encontrar negritas
  let boldMatch;
  const boldRegexClone = new RegExp(boldRegex.source, boldRegex.flags);
  while ((boldMatch = boldRegexClone.exec(text)) !== null) {
    matches.push({
      type: 'bold',
      start: boldMatch.index,
      end: boldMatch.index + boldMatch[0].length,
      content: boldMatch[1],
    });
  }

  // Encontrar código inline
  let codeMatch;
  const codeRegexClone = new RegExp(codeRegex.source, codeRegex.flags);
  while ((codeMatch = codeRegexClone.exec(text)) !== null) {
    matches.push({
      type: 'code',
      start: codeMatch.index,
      end: codeMatch.index + codeMatch[0].length,
      content: codeMatch[1],
    });
  }

  // Ordenar por posición
  matches.sort((a, b) => a.start - b.start);

  let lastIndex = 0;
  matches.forEach((match, idx) => {
    // Agregar texto normal antes del match
    if (match.start > lastIndex) {
      const plainText = text.substring(lastIndex, match.start);
      if (plainText) {
        elements.push(
          <Text key={`text-${globalIndex++}`} style={tone === 'user' ? styles.userText : styles.assistantText}>
            {plainText}
          </Text>
        );
      }
    }

    // Agregar el match formateado
    if (match.type === 'bold') {
      elements.push(
        <Text key={`bold-${globalIndex++}`} style={[styles.bold, tone === 'user' && styles.userText]}>
          {match.content}
        </Text>
      );
    } else if (match.type === 'code') {
      elements.push(
        <Text key={`code-${globalIndex++}`} style={[styles.inlineCode, tone === 'user' && styles.userCodeText]}>
          {match.content}
        </Text>
      );
    }

    lastIndex = match.end;
  });

  // Agregar texto restante
  if (lastIndex < text.length) {
    const remainingText = text.substring(lastIndex);
    if (remainingText) {
      elements.push(
        <Text key={`text-${globalIndex++}`} style={tone === 'user' ? styles.userText : styles.assistantText}>
          {remainingText}
        </Text>
      );
    }
  }

  // Si no hay matches, retornar el texto completo
  if (elements.length === 0) {
    return (
      <Text style={tone === 'user' ? styles.userText : styles.assistantText}>
        {text}
      </Text>
    );
  }

  return elements;
};

export default function FormattedMessageContent({ content, tone = 'assistant' }: Props) {
  const blocks = useMemo(() => createBlocks(content), [content]);

  return (
    <View style={styles.container}>
      {blocks.map((block, index) => {
        switch (block.type) {
          case 'heading':
            return (
              <Text
                key={`heading-${index}`}
                style={[
                  styles.heading,
                  block.level === 1 && styles.heading1,
                  block.level === 2 && styles.heading2,
                  block.level === 3 && styles.heading3,
                  block.level === 4 && styles.heading4,
                  tone === 'user' && styles.userText,
                ]}
              >
                {block.text}
              </Text>
            );
          case 'paragraph':
            return (
              <Text key={`paragraph-${index}`} style={[styles.paragraph, tone === 'user' && styles.userText]}>
                {renderInlineText(block.text, tone)}
              </Text>
            );
          case 'bulletList':
            return (
              <View key={`bullet-${index}`} style={styles.list}>
                {block.items.map((item, itemIndex) => (
                  <View key={`bullet-${index}-${itemIndex}`} style={styles.listItem}>
                    <View style={[styles.bullet, tone === 'user' && styles.bulletUser]} />
                    <Text style={[styles.listText, tone === 'user' && styles.userText]}>
                      {renderInlineText(item, tone)}
                    </Text>
                  </View>
                ))}
              </View>
            );
          case 'orderedList':
            return (
              <View key={`ordered-${index}`} style={styles.list}>
                {block.items.map((item, itemIndex) => (
                  <View key={`ordered-${index}-${itemIndex}`} style={styles.listItem}>
                    <Text style={[styles.listIndex, tone === 'user' && styles.userText]}>
                      {itemIndex + 1}.
                    </Text>
                    <Text style={[styles.listText, tone === 'user' && styles.userText]}>
                      {renderInlineText(item, tone)}
                    </Text>
                  </View>
                ))}
              </View>
            );
          case 'divider':
            return <View key={`divider-${index}`} style={styles.divider} />;
          case 'callout':
            return (
              <View
                key={`callout-${index}`}
                style={[
                  styles.callout,
                  block.variant === 'danger' && styles.calloutDanger,
                  block.variant === 'success' && styles.calloutSuccess,
                  block.variant === 'tip' && styles.calloutTip,
                ]}
              >
                <Text style={styles.calloutText}>{renderInlineText(block.text, 'assistant')}</Text>
              </View>
            );
          default:
            return null;
        }
      })}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    gap: 12, // Mayor espaciado entre elementos
  },
  // Estilos base para headings
  heading: {
    fontWeight: '700',
    color: Colors.light.text,
    letterSpacing: -0.3,
  },
  // Headings específicos por nivel
  heading1: {
    fontSize: 22,
    marginTop: 4,
    marginBottom: 2,
  },
  heading2: {
    fontSize: 20,
    marginTop: 4,
    marginBottom: 2,
  },
  heading3: {
    fontSize: 18,
    marginTop: 2,
    marginBottom: 1,
  },
  heading4: {
    fontSize: 16,
    marginTop: 2,
    marginBottom: 1,
  },
  paragraph: {
    fontSize: 16,
    lineHeight: 26, // Mayor line-height para mejor legibilidad
    color: Colors.light.text,
  },
  bold: {
    fontWeight: '700',
    color: Colors.light.text,
  },
  // Código inline con estilo distintivo
  inlineCode: {
    fontFamily: Platform.OS === 'ios' ? 'Menlo' : 'monospace',
    fontSize: 15,
    backgroundColor: Colors.light.backgroundSecondary,
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 4,
    color: Colors.light.primary,
    borderWidth: 1,
    borderColor: Colors.light.border,
  },
  userCodeText: {
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderColor: 'rgba(255, 255, 255, 0.3)',
    color: Colors.light.background,
  },
  list: {
    gap: 8, // Mayor espaciado entre items
  },
  listItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 10, // Mayor gap entre bullet y texto
  },
  listText: {
    flex: 1,
    fontSize: 16,
    lineHeight: 26, // Mayor line-height
    color: Colors.light.text,
  },
  listIndex: {
    fontSize: 16,
    lineHeight: 26,
    fontWeight: '700', // Más bold para números
    color: Colors.light.text,
    width: 24, // Más ancho para números de 2 dígitos
  },
  bullet: {
    width: 8, // Bullets más grandes y visibles
    height: 8,
    borderRadius: 4,
    marginTop: 8,
    backgroundColor: Colors.light.primary,
    shadowColor: Colors.light.primary,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 1,
  },
  bulletUser: {
    backgroundColor: Colors.light.background,
    shadowColor: Colors.light.background,
  },
  divider: {
    height: 1,
    backgroundColor: Colors.light.border,
    marginVertical: 8, // Mayor espaciado vertical
    opacity: 0.5,
  },
  // Callouts más prominentes
  callout: {
    backgroundColor: '#E0F2FE',
    borderRadius: 12,
    padding: 16, // Mayor padding
    borderWidth: 2, // Bordes más gruesos
    borderColor: '#BAE6FD',
    shadowColor: '#0284C7',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  calloutDanger: {
    backgroundColor: '#FEE2E2',
    borderColor: '#FCA5A5',
    shadowColor: '#DC2626',
  },
  calloutSuccess: {
    backgroundColor: '#DCFCE7',
    borderColor: '#86EFAC',
    shadowColor: '#16A34A',
  },
  calloutTip: {
    backgroundColor: '#FEF9C3',
    borderColor: '#FDE68A',
    shadowColor: '#CA8A04',
  },
  calloutText: {
    fontSize: 16,
    lineHeight: 26,
    color: Colors.light.text,
  },
  assistantText: {
    color: Colors.light.text,
  },
  userText: {
    color: Colors.light.background,
  },
});
