import React, { useState } from 'react';
import { StyleSheet, Text, View, TouchableOpacity, LayoutAnimation, Platform, UIManager } from 'react-native';
import { Camera, ChevronDown, ChevronUp } from 'lucide-react-native';
import Colors from '@/constants/colors';

// Enable LayoutAnimation on Android
if (Platform.OS === 'android' && UIManager.setLayoutAnimationEnabledExperimental) {
  UIManager.setLayoutAnimationEnabledExperimental(true);
}

interface PhotoGuidanceProps {
  /**
   * Compact mode for inline display (chat)
   */
  compact?: boolean;
}

/**
 * PhotoGuidance Component - Revolut-style minimalist design
 *
 * Displays visual instructions to help users take photos that will pass
 * OpenAI's vision safety checks (avoid face detection issues).
 *
 * Usage:
 * - step1/step2: Collapsible inline tip
 * - chat: Ultra-compact mode
 */
export default function PhotoGuidance({ compact = false }: PhotoGuidanceProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpanded = () => {
    LayoutAnimation.configureNext(LayoutAnimation.Presets.easeInEaseOut);
    setIsExpanded(!isExpanded);
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <Camera color={Colors.light.primary} size={14} />
        <Text style={styles.compactText}>
          Enfoca solo el cabello, evita rostros
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <TouchableOpacity
        style={styles.header}
        onPress={toggleExpanded}
        activeOpacity={0.7}
        accessible={true}
        accessibilityRole="button"
        accessibilityLabel={isExpanded ? "Ocultar consejos de fotografía" : "Mostrar consejos de fotografía"}
        accessibilityHint="Toca para expandir o contraer los consejos sobre cómo tomar fotos del cabello"
        accessibilityState={{ expanded: isExpanded }}
      >
        <View style={styles.headerLeft}>
          <View style={styles.iconCircle}>
            <Camera color={Colors.light.primary} size={14} strokeWidth={2.5} />
          </View>
          <Text style={styles.headerText}>
            Enfoca solo el cabello, evita rostros
          </Text>
        </View>
        <View style={styles.chevronIcon}>
          {isExpanded ? (
            <ChevronUp color={Colors.light.textSecondary} size={18} strokeWidth={2} />
          ) : (
            <ChevronDown color={Colors.light.textSecondary} size={18} strokeWidth={2} />
          )}
        </View>
      </TouchableOpacity>

      {isExpanded && (
        <View style={styles.expandedContent}>
          <View style={styles.tipRow}>
            <Text style={styles.tipDot}>•</Text>
            <Text style={styles.tipText}>Diferentes ángulos (raíces, medios, puntas)</Text>
          </View>
          <View style={styles.tipRow}>
            <Text style={styles.tipDot}>•</Text>
            <Text style={styles.tipText}>Luz natural, sin flash directo</Text>
          </View>
          <View style={styles.tipRow}>
            <Text style={styles.tipDot}>•</Text>
            <Text style={styles.tipText}>Fotos nítidas, sin movimiento</Text>
          </View>
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  // Revolut-style minimalist design
  container: {
    backgroundColor: Colors.light.background,
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.light.border,
    marginBottom: 16,
    overflow: 'hidden',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 14,
    paddingVertical: 12,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 10,
    flex: 1,
  },
  iconCircle: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#EFF6FF',
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerText: {
    flex: 1,
    fontSize: 13,
    fontWeight: '500' as const,
    color: Colors.light.text,
    lineHeight: 18,
  },
  chevronIcon: {
    marginLeft: 8,
  },
  expandedContent: {
    paddingHorizontal: 14,
    paddingBottom: 14,
    paddingTop: 4,
    gap: 8,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
  },
  tipRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 8,
  },
  tipDot: {
    fontSize: 14,
    color: Colors.light.textSecondary,
    lineHeight: 20,
    marginTop: -2,
  },
  tipText: {
    flex: 1,
    fontSize: 13,
    color: Colors.light.textSecondary,
    lineHeight: 20,
  },

  // Ultra-compact mode (for chat)
  compactContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
    backgroundColor: '#F8FAFC',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#E2E8F0',
  },
  compactText: {
    flex: 1,
    fontSize: 12,
    color: Colors.light.textSecondary,
    lineHeight: 16,
    fontWeight: '500' as const,
  },
});
