import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthContext } from '@/contexts/AuthContext';
import { ClientContext } from '@/contexts/ClientContext';
import { ChatProvider } from '@/contexts/ChatContext';
import { FormulaProvider } from '@/contexts/FormulaContext';

const queryClient = new QueryClient();

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <QueryClientProvider client={queryClient}>
      <AuthContext>
        <ClientContext>
          <ChatProvider>
            <FormulaProvider>
              {children}
            </FormulaProvider>
          </ChatProvider>
        </ClientContext>
      </AuthContext>
    </QueryClientProvider>
  );
}
