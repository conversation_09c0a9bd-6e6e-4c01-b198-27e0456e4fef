import React, { useState } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { Search, Check } from 'lucide-react-native';
import Colors from '@/constants/colors';
import { BrandAccordion } from './BrandAccordion';
import brandsData from '@/assets/data/brands.json';
import type { ProfessionalBrand } from '@/types';

const BRANDS: ProfessionalBrand[] = brandsData as ProfessionalBrand[];

type BrandSelectorMode = 'formula' | 'preferences';

type BrandSelectorProps = {
  mode: BrandSelectorMode;
  selectedBrands: string[]; // Brand IDs
  onBrandSelect: (brandId: string) => void;
  onLineSelect?: (brandId: string, line: string) => void;
  selectedLines?: Record<string, string>; // { brandId: lineName }
};

export function BrandSelector({
  mode,
  selectedBrands,
  onBrandSelect,
  onLineSelect,
  selectedLines = {},
}: BrandSelectorProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [expandedBrandId, setExpandedBrandId] = useState<string | null>(null);

  const filteredBrands = BRANDS.filter((brand) =>
    brand.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleBrandToggle = (brandId: string) => {
    if (mode === 'formula') {
      // Formula mode: single selection, auto-expand
      onBrandSelect(brandId);
      setExpandedBrandId(expandedBrandId === brandId ? null : brandId);
    } else {
      // Preferences mode: multi-selection, toggle expand
      onBrandSelect(brandId);
      if (selectedBrands.includes(brandId)) {
        // If selecting, expand
        setExpandedBrandId(expandedBrandId === brandId ? null : brandId);
      } else {
        // If deselecting, collapse
        if (expandedBrandId === brandId) {
          setExpandedBrandId(null);
        }
      }
    }
  };

  const handleLineSelect = (brandId: string, line: string) => {
    if (onLineSelect) {
      onLineSelect(brandId, line);
    }
  };

  const isSelected = (brandId: string) => selectedBrands.includes(brandId);
  const isExpanded = (brandId: string) => expandedBrandId === brandId;

  return (
    <View style={styles.container}>
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <Search color={Colors.light.textLight} size={20} />
        <TextInput
          style={styles.searchInput}
          placeholder="Buscar marca..."
          placeholderTextColor={Colors.light.textLight}
          value={searchQuery}
          onChangeText={setSearchQuery}
        />
      </View>

      {/* Results Count */}
      <Text style={styles.resultsCount}>
        {filteredBrands.length} marca{filteredBrands.length !== 1 ? 's' : ''} encontrada
        {filteredBrands.length !== 1 ? 's' : ''}
      </Text>

      {/* Brands List */}
      <View style={styles.brandsContainer}>
        {mode === 'formula' ? (
          // Formula mode: Use accordion for single-select with lines
          filteredBrands.map((brand) => (
            <BrandAccordion
              key={brand.id}
              brand={brand}
              isExpanded={isExpanded(brand.id)}
              isSelected={isSelected(brand.id)}
              onToggle={() => handleBrandToggle(brand.id)}
              selectedLine={selectedLines[brand.id]}
              onLineSelect={(line) => handleLineSelect(brand.id, line)}
            />
          ))
        ) : (
          // Preferences mode: Simple cards for multi-select
          filteredBrands.map((brand) => (
            <TouchableOpacity
              key={brand.id}
              style={[
                styles.brandCard,
                isSelected(brand.id) && styles.brandCardSelected,
              ]}
              onPress={() => handleBrandToggle(brand.id)}
              activeOpacity={0.7}
            >
              <View style={styles.brandHeader}>
                <View style={styles.brandInfo}>
                  <Text
                    style={[
                      styles.brandName,
                      isSelected(brand.id) && styles.brandNameSelected,
                    ]}
                  >
                    {brand.name}
                  </Text>
                  <Text style={styles.brandCountry}>{brand.country}</Text>
                </View>
                {isSelected(brand.id) && (
                  <View style={styles.checkBadge}>
                    <Check size={16} color="#fff" strokeWidth={3} />
                  </View>
                )}
              </View>
            </TouchableOpacity>
          ))
        )}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.light.background,
    borderRadius: 16,
    paddingHorizontal: 18,
    paddingVertical: 14,
    gap: 12,
    marginBottom: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  searchInput: {
    flex: 1,
    fontSize: 16,
    color: Colors.light.text,
    fontWeight: '500' as const,
  },
  resultsCount: {
    fontSize: 14,
    color: Colors.light.textLight,
    marginBottom: 16,
    fontWeight: '500' as const,
  },
  brandsContainer: {
    gap: 12,
  },
  brandCard: {
    backgroundColor: Colors.light.background,
    padding: 18,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  brandCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: `${Colors.light.primary}05`,
  },
  brandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 17,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandNameSelected: {
    color: Colors.light.primary,
    fontWeight: '700' as const,
  },
  brandCountry: {
    fontSize: 13,
    color: Colors.light.textLight,
    fontWeight: '500' as const,
  },
  checkBadge: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: Colors.light.primary,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
