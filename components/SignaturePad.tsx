import React, { useRef, useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  PanResponder,
  TouchableOpacity,
  Text,
} from 'react-native';
import Svg, { Path } from 'react-native-svg';
import { RotateCcw } from 'lucide-react-native';
import Colors from '@/constants/colors';

type Point = { x: number; y: number };
type PathData = string;

interface SignaturePadProps {
  onSignatureChange?: (signature: string) => void;
  height?: number;
}

export default function SignaturePad({ onSignatureChange, height = 200 }: SignaturePadProps) {
  const [paths, setPaths] = useState<PathData[]>([]);
  const [currentPath, setCurrentPath] = useState<Point[]>([]);

  useEffect(() => {
    if (onSignatureChange) {
      if (currentPath.length > 0) {
        const tempPathData = pointsToSvgPath(currentPath);
        const tempAllPaths = [...paths, tempPathData];
        onSignatureChange(JSON.stringify(tempAllPaths));
      } else if (paths.length > 0) {
        onSignatureChange(JSON.stringify(paths));
      } else {
        onSignatureChange('');
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [paths, currentPath]);

  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: () => true,
      onMoveShouldSetPanResponder: () => true,
      onPanResponderGrant: (evt) => {
        const locationX = evt.nativeEvent.locationX;
        const locationY = evt.nativeEvent.locationY;
        setCurrentPath([{ x: locationX, y: locationY }]);
      },
      onPanResponderMove: (evt) => {
        const locationX = evt.nativeEvent.locationX;
        const locationY = evt.nativeEvent.locationY;
        setCurrentPath((prev) => [...prev, { x: locationX, y: locationY }]);
      },
      onPanResponderRelease: () => {
        if (currentPath.length > 0) {
          const pathData = pointsToSvgPath(currentPath);
          setPaths((prev) => [...prev, pathData]);
          setCurrentPath([]);
        }
      },
    })
  ).current;

  const pointsToSvgPath = (points: Point[]): string => {
    if (points.length === 0) return '';
    
    let path = `M ${points[0].x} ${points[0].y}`;
    
    for (let i = 1; i < points.length; i++) {
      path += ` L ${points[i].x} ${points[i].y}`;
    }
    
    return path;
  };

  const handleClear = () => {
    setPaths([]);
    setCurrentPath([]);
  };

  const currentPathData = pointsToSvgPath(currentPath);

  return (
    <View style={styles.container}>
      <View
        style={[styles.canvas, { height }]}
        {...panResponder.panHandlers}
      >
        <Svg width="100%" height="100%" style={styles.svg}>
          {paths.map((path, index) => (
            <Path
              key={`path-${index}`}
              d={path}
              stroke={Colors.light.text}
              strokeWidth={2}
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          ))}
          {currentPathData && (
            <Path
              d={currentPathData}
              stroke={Colors.light.text}
              strokeWidth={2}
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}
        </Svg>
        
        {paths.length === 0 && currentPath.length === 0 && (
          <View style={styles.placeholder}>
            <Text style={styles.placeholderText}>Firme aquí</Text>
          </View>
        )}
      </View>
      
      <View style={styles.actions}>
        <TouchableOpacity
          style={styles.clearButton}
          onPress={handleClear}
          disabled={paths.length === 0 && currentPath.length === 0}
        >
          <RotateCcw
            color={paths.length === 0 && currentPath.length === 0 ? Colors.light.textLight : Colors.light.primary}
            size={20}
          />
          <Text
            style={[
              styles.clearButtonText,
              (paths.length === 0 && currentPath.length === 0) && styles.clearButtonTextDisabled,
            ]}
          >
            Limpiar
          </Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  canvas: {
    width: '100%',
    backgroundColor: Colors.light.background,
    borderWidth: 2,
    borderColor: Colors.light.border,
    borderRadius: 12,
    borderStyle: 'dashed',
    position: 'relative',
    overflow: 'hidden',
  },
  svg: {
    position: 'absolute',
    top: 0,
    left: 0,
  },
  placeholder: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    pointerEvents: 'none',
  },
  placeholderText: {
    fontSize: 16,
    color: Colors.light.textLight,
    fontStyle: 'italic' as const,
  },
  actions: {
    flexDirection: 'row',
    justifyContent: 'flex-end',
    marginTop: 12,
  },
  clearButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingVertical: 8,
    paddingHorizontal: 12,
  },
  clearButtonText: {
    fontSize: 15,
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  clearButtonTextDisabled: {
    color: Colors.light.textLight,
  },
});
