import React, { useEffect, useRef } from 'react';
import {
  StyleSheet,
  Text,
  View,
  TouchableOpacity,
  Animated,
  Easing,
} from 'react-native';
import { ChevronDown } from 'lucide-react-native';
import Colors from '@/constants/colors';
import type { ProfessionalBrand } from '@/types';

// Category display names (Spanish for UI)
const CATEGORY_NAMES: Record<string, string> = {
  permanent: 'Permanente',
  demi_permanent: 'Demi-Permanente',
  lighteners: 'Aclarantes',
  direct_dye: 'Directos/Fantasía',
  other: 'Otros',
};

type BrandAccordionProps = {
  brand: ProfessionalBrand;
  isExpanded: boolean;
  isSelected: boolean;
  onToggle: () => void;
  selectedLine?: string;
  onLineSelect: (line: string) => void;
  showLineSelection?: boolean; // For step decision: show lines or decision buttons
};

export function BrandAccordion({
  brand,
  isExpanded,
  isSelected,
  onToggle,
  selectedLine,
  onLineSelect,
  showLineSelection = false,
}: BrandAccordionProps) {
  const [decisionMode, setDecisionMode] = React.useState<'choose' | 'ai' | 'manual'>('choose');
  const animatedHeight = useRef(new Animated.Value(0)).current;
  const animatedRotation = useRef(new Animated.Value(0)).current;

  // Sync decisionMode with selectedLine prop changes
  useEffect(() => {
    if (selectedLine === '') {
      setDecisionMode('ai');
    } else if (selectedLine === undefined || selectedLine === null) {
      setDecisionMode('choose');
    } else {
      setDecisionMode('manual');
    }
  }, [selectedLine]);

  useEffect(() => {
    Animated.parallel([
      Animated.timing(animatedHeight, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: false,
      }),
      Animated.timing(animatedRotation, {
        toValue: isExpanded ? 1 : 0,
        duration: 300,
        easing: Easing.out(Easing.cubic),
        useNativeDriver: true,
      }),
    ]).start();
  }, [animatedHeight, animatedRotation, isExpanded]);

  const rotation = animatedRotation.interpolate({
    inputRange: [0, 1],
    outputRange: ['0deg', '180deg'],
  });

  const maxHeight = animatedHeight.interpolate({
    inputRange: [0, 1],
    outputRange: [0, 2000], // Increased from 1000 to 2000 for brands with many lines
  });

  return (
    <View style={styles.container}>
      {/* Brand Header (always visible) */}
      <TouchableOpacity
        style={[
          styles.brandCard,
          isSelected && styles.brandCardSelected,
        ]}
        onPress={onToggle}
        activeOpacity={0.7}
      >
        <View style={styles.brandHeader}>
          <View style={styles.brandInfo}>
            <Text style={[
              styles.brandName,
              isSelected && styles.brandNameSelected,
            ]}>
              {brand.name}
            </Text>
            <Text style={styles.brandCountry}>{brand.country}</Text>
          </View>
          <Animated.View style={{ transform: [{ rotate: rotation }] }}>
            <ChevronDown
              color={isSelected ? Colors.light.primary : Colors.light.textLight}
              size={20}
            />
          </Animated.View>
        </View>
      </TouchableOpacity>

      {/* Lines (collapsible) */}
      <Animated.View
        style={[
          styles.linesWrapper,
          {
            maxHeight,
            opacity: animatedHeight,
          },
        ]}
      >
        {isExpanded && (
          <View style={styles.linesContainer}>
            {/* Decision Step: AI vs Manual */}
            {!showLineSelection && decisionMode === 'choose' && (
              <View style={styles.decisionContainer}>
                <TouchableOpacity
                  style={styles.aiButton}
                  onPress={() => {
                    setDecisionMode('ai');
                    onLineSelect('');
                  }}
                  activeOpacity={0.8}
                >
                  <Text style={styles.aiButtonIcon}>✨</Text>
                  <View style={styles.buttonTextContainer}>
                    <Text style={styles.aiButtonTitle}>Dejar que la IA decida</Text>
                    <Text style={styles.aiButtonSubtitle}>Recomendado</Text>
                  </View>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.manualButton}
                  onPress={() => setDecisionMode('manual')}
                  activeOpacity={0.8}
                >
                  <Text style={styles.manualButtonIcon}>📋</Text>
                  <Text style={styles.manualButtonText}>Elegir línea específica</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Show confirmation when AI is selected */}
            {!showLineSelection && decisionMode === 'ai' && (
              <View style={styles.confirmationContainer}>
                <Text style={styles.confirmationText}>
                  ✅ La IA elegirá la mejor línea de {brand.name}
                </Text>
                <TouchableOpacity
                  style={styles.changeButton}
                  onPress={() => setDecisionMode('choose')}
                >
                  <Text style={styles.changeButtonText}>Cambiar</Text>
                </TouchableOpacity>
              </View>
            )}

            {/* Show lines when manual mode or showLineSelection is true */}
            {(showLineSelection || decisionMode === 'manual') && (
              <>
                {/* AI Auto-select option (always first) */}
                <TouchableOpacity
                  style={[
                    styles.lineCard,
                    !selectedLine && styles.lineCardSelected,
                  ]}
                  onPress={() => {
                    onLineSelect('');
                    setDecisionMode('ai');
                  }}
                  activeOpacity={0.7}
                >
                  <Text style={[
                    styles.lineName,
                    !selectedLine && styles.lineNameSelected,
                  ]}>
                    ✨ IA elige la mejor línea
                  </Text>
                </TouchableOpacity>

                {/* Lines grouped by category */}
                {Object.entries(brand.lines).map(([category, lines]) => {
              if (!lines || lines.length === 0) return null;

                  return (
                    <View key={category} style={styles.categoryGroup}>
                      <Text style={styles.categoryTitle}>
                        {CATEGORY_NAMES[category] || category}
                      </Text>
                      {lines.map((line) => (
                        <TouchableOpacity
                          key={line}
                          style={[
                            styles.lineCard,
                            selectedLine === line && styles.lineCardSelected,
                          ]}
                          onPress={() => onLineSelect(line)}
                          activeOpacity={0.7}
                        >
                          <Text style={[
                            styles.lineName,
                            selectedLine === line && styles.lineNameSelected,
                          ]}>
                            {line}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  );
                })}
              </>
            )}

            {/* Key Features */}
            {brand.key_features && brand.key_features.length > 0 && (
              <View style={styles.featuresContainer}>
                <Text style={styles.featuresTitle}>✨ Características</Text>
                {brand.key_features.map((feature, idx) => (
                  <Text key={idx} style={styles.featureText}>
                    • {feature}
                  </Text>
                ))}
              </View>
            )}
          </View>
        )}
      </Animated.View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: 12,
  },
  brandCard: {
    backgroundColor: Colors.light.background,
    padding: 18,
    borderRadius: 20,
    borderWidth: 2,
    borderColor: Colors.light.border,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 8,
    elevation: 2,
  },
  brandCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.surface,
    shadowColor: Colors.light.shadow,
    shadowOpacity: 0.08,
  },
  brandHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  brandInfo: {
    flex: 1,
  },
  brandName: {
    fontSize: 17,
    fontWeight: '600' as const,
    color: Colors.light.text,
    marginBottom: 4,
  },
  brandNameSelected: {
    color: Colors.light.primary,
    fontWeight: '700' as const,
  },
  brandCountry: {
    fontSize: 13,
    color: Colors.light.textLight,
    fontWeight: '500' as const,
  },
  linesWrapper: {
    overflow: 'hidden',
  },
  linesContainer: {
    paddingHorizontal: 18,
    paddingTop: 12,
    paddingBottom: 18,
    gap: 12,
  },
  categoryGroup: {
    gap: 8,
  },
  categoryTitle: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.textSecondary,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
    marginTop: 4,
  },
  lineCard: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 14,
    borderRadius: 12,
    borderWidth: 2,
    borderColor: 'transparent',
  },
  lineCardSelected: {
    borderColor: Colors.light.primary,
    backgroundColor: Colors.light.surface,
  },
  lineName: {
    fontSize: 15,
    fontWeight: '500' as const,
    color: Colors.light.text,
  },
  lineNameSelected: {
    color: Colors.light.primary,
    fontWeight: '600' as const,
  },
  featuresContainer: {
    backgroundColor: Colors.light.backgroundSecondary,
    padding: 14,
    borderRadius: 12,
    marginTop: 4,
    borderWidth: 1,
    borderColor: Colors.light.borderLight,
  },
  featuresTitle: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.primary,
    marginBottom: 8,
  },
  featureText: {
    fontSize: 12,
    color: Colors.light.textSecondary,
    lineHeight: 18,
    marginBottom: 4,
  },
  decisionContainer: {
    gap: 12,
    marginBottom: 8,
  },
  aiButton: {
    backgroundColor: Colors.light.primary,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 18,
    borderRadius: 16,
    gap: 14,
    shadowColor: Colors.light.shadow,
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.12,
    shadowRadius: 16,
    elevation: 8,
  },
  aiButtonIcon: {
    fontSize: 28,
  },
  buttonTextContainer: {
    flex: 1,
  },
  aiButtonTitle: {
    fontSize: 16,
    fontWeight: '700' as const,
    color: Colors.light.background,
    marginBottom: 2,
  },
  aiButtonSubtitle: {
    fontSize: 13,
    color: Colors.light.background,
    opacity: 0.9,
    fontWeight: '500' as const,
  },
  manualButton: {
    backgroundColor: Colors.light.background,
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 16,
    gap: 12,
    borderWidth: 2,
    borderColor: Colors.light.border,
  },
  manualButtonIcon: {
    fontSize: 22,
  },
  manualButtonText: {
    fontSize: 15,
    fontWeight: '600' as const,
    color: Colors.light.text,
  },
  confirmationContainer: {
    backgroundColor: Colors.light.surface,
    padding: 16,
    borderRadius: 12,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  confirmationText: {
    flex: 1,
    fontSize: 14,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
  changeButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 8,
    backgroundColor: Colors.light.background,
  },
  changeButtonText: {
    fontSize: 13,
    fontWeight: '600' as const,
    color: Colors.light.primary,
  },
});
