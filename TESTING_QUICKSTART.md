# Testing Quick Start Guide - Salonier AI

## Installation (5 minutes)

```bash
# 1. Install test dependencies
bun add -D jest@29.7.0 \
  @testing-library/react-native@12.4.0 \
  @testing-library/jest-native@5.4.3 \
  react-test-renderer@19.1.0 \
  @types/jest@29.5.11 \
  jest-expo@52.0.0

# 2. Verify installation
bun run test --version
```

## Running Tests

```bash
# Run all tests
bun run test

# Run tests in watch mode (recommended for development)
bun run test:watch

# Run security tests only
bun run test:security

# Run with coverage report
bun run test:coverage

# Open coverage report in browser
open coverage/lcov-report/index.html
```

## Project Structure

```
__tests__/
├── security/          # CRITICAL: IDOR, XSS prevention
│   ├── xss-prevention.test.ts
│   └── idor-prevention.test.ts
├── unit/
│   ├── lib/          # Utils, sanitization, AI client
│   └── contexts/     # AuthContext, ClientContext, etc.
└── integration/      # Multi-step workflows

__mocks__/
├── @supabase/        # Supabase client mock
├── lib/              # AI client, storage mocks
└── fixtures/         # Test data (clients, conversations)
```

## Current Test Status

| Test Suite | Status | Priority |
|------------|--------|----------|
| Security (XSS) | ✅ Ready | CRITICAL |
| Security (IDOR) | ✅ Ready | CRITICAL |
| Sanitization | ✅ Ready | HIGH |
| AuthContext | 🔨 TODO | HIGH |
| ClientContext | 🔨 TODO | HIGH |
| ChatContext | 🔨 TODO | HIGH |
| FormulaContext | 🔨 TODO | HIGH |
| Integration Tests | 🔨 TODO | MEDIUM |

## Quick Test Examples

### Run security tests
```bash
bun run test:security
# Expected: All CRITICAL tests pass
```

### Run specific test file
```bash
bun run test __tests__/security/xss-prevention.test.ts
```

### Run tests with coverage
```bash
bun run test:coverage
# Coverage thresholds:
# - Global: 70%
# - Contexts: 90%
# - lib/sanitize.ts: 95%
```

## Next Steps

### Week 1 (Critical Tests)
1. ✅ Setup infrastructure
2. ✅ Security tests (XSS, IDOR)
3. 🔨 TODO: AuthContext tests
4. 🔨 TODO: ClientContext tests
5. 🔨 TODO: ChatContext tests

### Week 2 (Integration Tests)
6. 🔨 TODO: FormulaContext tests
7. 🔨 TODO: Formula workflow integration test
8. 🔨 TODO: Chat with images integration test
9. 🔨 TODO: CI/CD setup (GitHub Actions)

## Writing Your First Test

**File**: `__tests__/unit/my-function.test.ts`

```typescript
import { myFunction } from '@/path/to/module';

describe('myFunction', () => {
  it('should do expected behavior', () => {
    // Arrange
    const input = 'test';

    // Act
    const result = myFunction(input);

    // Assert
    expect(result).toBe('expected');
  });

  it('should handle edge case', () => {
    expect(myFunction(null)).toBe('default');
  });
});
```

## Common Commands

```bash
# Development workflow
bun run test:watch          # Auto-run tests on file changes
bun run test:coverage       # Check coverage
bun run lint                # Check code quality

# Pre-commit checklist
bun run lint && bun run test:security

# CI/CD (runs automatically on PR)
bun run test:ci
```

## Debugging Tests

```bash
# Run tests in debug mode
bun run test:debug

# In Chrome, navigate to: chrome://inspect
# Click "Open dedicated DevTools for Node"
# Set breakpoints and debug
```

## Troubleshooting

### Error: "Cannot find module '@/...'"
**Solution**: Path aliases are configured in `jest.config.js`. Restart Jest if you just installed it.

### Error: "jest is not recognized"
**Solution**: Run `bun install` to install dependencies.

### Tests timeout
**Solution**: Increase timeout in specific test:
```typescript
it('slow test', async () => {
  // ...
}, 30000); // 30 second timeout
```

### Mock not working
**Solution**: Clear mocks in `beforeEach`:
```typescript
beforeEach(() => {
  jest.clearAllMocks();
});
```

## Coverage Goals

| Category | Current | Target | Status |
|----------|---------|--------|--------|
| Overall | 0% | 70% | 🔴 |
| Contexts | 0% | 90% | 🔴 |
| lib/sanitize.ts | 0% | 95% | 🔴 |
| Security Tests | 0% | 100% | 🔴 |

## Resources

- **Full Strategy**: See `TEST_STRATEGY.md`
- **Jest Docs**: https://jestjs.io/docs/getting-started
- **React Native Testing Library**: https://callstack.github.io/react-native-testing-library/
- **Testing Best Practices**: https://kentcdodds.com/blog/common-mistakes-with-react-testing-library

## CI/CD Status

- **GitHub Actions**: `.github/workflows/test.yml`
- **Runs on**: Push to main/develop, all PRs
- **Required**: All tests must pass before merge
- **Coverage**: Uploaded to Codecov

## Quick Wins (Easy Tests to Write)

1. ✅ `lib/sanitize.ts` - Pure functions, easy to test
2. 🔨 `types/index.ts` - Type validation tests
3. 🔨 `utils/validation.ts` - Input validation tests
4. 🔨 Simple utility functions

## Getting Help

- Check `TEST_STRATEGY.md` for detailed examples
- Review existing test files in `__tests__/security/`
- Ask in #testing Slack channel
- Tag @test-engineer agent in Salonier Orchestrator

---

**Remember**: Security tests are CRITICAL. Run `bun run test:security` before every commit.
