# Seguridad - Salonier AI

Este documento describe las prácticas de seguridad implementadas en el proyecto y las recomendaciones para mantener la seguridad.

## ⚠️ IMPORTANTE: Gestión de Secrets

### API Keys Configuradas

Las siguientes API keys están configuradas como **secrets en Supabase** y NO deben estar hardcodeadas en el código:

- `OPENAI_API_KEY` - Para servicios de OpenAI (GPT-4o, GPT-4o-mini, GPT-4o Vision)
- `PERPLEXITY_API_KEY` - Para búsqueda de productos con Sonar Pro
- `SUPABASE_SERVICE_ROLE_KEY` - Para operaciones admin de Supabase

### Dónde están las keys

✅ **CORRECTAMENTE configuradas en:**
- Supabase Edge Functions Secrets (dashboard)
- `.env.local` (archivo local, en `.gitignore`)

❌ **NUNCA deben estar en:**
- Scripts shell (`.sh`) commiteados al repositorio
- Archivos de configuración versionados
- Código fuente de la aplicación
- Test files que se commitean
- Documentación markdown

## Archivos Protegidos en .gitignore

Los siguientes archivos/patrones están protegidos y NO se commitean:

```
# Secrets locales
.env.local
.env

# Scripts de configuración con secrets
configure-secrets.sh
configure-edge-secrets.sh

# Test files que pueden contener credentials temporales
test-*.js
test-*.ts

# Cursor MCP config (tokens de acceso)
.cursor/
```

## Cómo Trabajar con Secrets Localmente

### 1. Archivo .env.local

Crea un archivo `.env.local` en la raíz del proyecto (nunca lo commitees):

```bash
# Supabase
EXPO_PUBLIC_SUPABASE_URL=https://guyxczavhtemwlrknqpm.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGc...  # Esta es pública, puede estar en código
SUPABASE_SERVICE_ROLE_KEY=eyJhbGc...     # PRIVADA - solo para admin

# API Keys (PRIVADAS)
OPENAI_API_KEY=sk-proj-...
PERPLEXITY_API_KEY=pplx-...
```

### 2. Scripts de Configuración

Si necesitas ejecutar scripts de deployment:

```bash
# Opción 1: Cargar desde .env.local
source .env.local
./configure-secrets.sh

# Opción 2: Exportar manualmente
export OPENAI_API_KEY='sk-proj-...'
export PERPLEXITY_API_KEY='pplx-...'
./configure-secrets.sh
```

### 3. Verificar Secrets en Supabase

```bash
# Ver secrets configurados (valores están ocultos)
supabase secrets list --project-ref guyxczavhtemwlrknqpm

# Agregar o actualizar un secret
supabase secrets set NOMBRE_SECRET="valor" --project-ref guyxczavhtemwlrknqpm
```

## Rotar API Keys (Si fueron expuestas)

Si accidentalmente commiteaste API keys:

### 1. Revocar inmediatamente

**OpenAI:**
1. Ir a https://platform.openai.com/api-keys
2. Encontrar la key expuesta
3. Click en "Revoke"
4. Generar nueva key

**Perplexity:**
1. Ir a https://www.perplexity.ai/settings/api
2. Revocar la key comprometida
3. Generar nueva key

### 2. Actualizar en Supabase

```bash
# Actualizar secrets con las nuevas keys
export OPENAI_API_KEY='nueva-key-sk-proj-...'
export PERPLEXITY_API_KEY='nueva-key-pplx-...'
./configure-secrets.sh
```

### 3. Limpiar historial de Git

Si la key fue commiteada:

```bash
# Eliminar archivo del historial
git filter-branch --force --index-filter \
  "git rm --cached --ignore-unmatch configure-secrets.sh" \
  --prune-empty --tag-name-filter cat -- --all

# Force push (PELIGROSO - avisar al equipo)
git push origin --force --all
```

**MEJOR opción:** Simplemente rota las keys y asume que el historial quedó comprometido.

## Prácticas de Seguridad

### ✅ Hacer

1. **Usar variables de entorno** para todas las credenciales
2. **Verificar .gitignore** antes de commitear archivos nuevos
3. **Usar archivos .example** para templates de configuración
4. **Revisar diffs** antes de hacer push para detectar secrets accidentales
5. **Configurar pre-commit hooks** para escanear secrets (opcional pero recomendado)

### ❌ No Hacer

1. **NUNCA hardcodear API keys** en el código fuente
2. **NUNCA commitear archivos .env** o similares
3. **NUNCA compartir secrets** en canales inseguros (Slack, email sin cifrar)
4. **NUNCA usar las mismas keys** para desarrollo y producción
5. **NUNCA incluir secrets** en mensajes de commit o PR descriptions

## Pre-commit Hook (Opcional)

Para prevenir commits accidentales de secrets, instala un hook:

```bash
# Instalar gitleaks
brew install gitleaks

# O usar detect-secrets
pip install detect-secrets

# Crear pre-commit hook
cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
gitleaks protect --staged --verbose
EOF

chmod +x .git/hooks/pre-commit
```

## Edge Function Security

La Edge Function `ai-proxy` implementa:

- ✅ **Autenticación JWT** obligatoria (Supabase Auth)
- ✅ **Rate limiting** por usuario (100/día, 20/hora)
- ✅ **Logging de costos** en base de datos
- ✅ **Validación de inputs** (pendiente mejoras)
- ✅ **CORS configurado** (actualmente abierto, considerar restringir en prod)

## Compliance

El proyecto cumple con:

- ✅ **GDPR** - Consentimiento de usuario, derecho al olvido
- ✅ **CCPA** - Transparencia en uso de datos
- ✅ **OpenAI Usage Policy** - Datos procesados conforme a términos

## Contacto de Seguridad

Si descubres una vulnerabilidad de seguridad:

1. **NO crear issue público** en GitHub
2. Contactar directamente al equipo de desarrollo
3. Proporcionar detalles: descripción, impacto, pasos para reproducir
4. Esperar confirmación antes de divulgar públicamente

---

**Última actualización:** 2025-10-22
**Revisión de seguridad:** Completada
