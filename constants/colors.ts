/**
 * Salonier AI Color Palette v2.0 - Slate 600 Professional
 *
 * Inspired by apps for creative professionals (Figma, Notion, Webflow, Linear)
 * Philosophy: Slate blue professional + warm grays for sophisticated, approachable aesthetic
 *
 * Design Principles:
 * - Slate 600 (#475569) for brand identity - professional + creative, not corporate
 * - Slate 500 (#64748B) for interactive elements (tabs, links, toggles)
 * - Warm grays for backgrounds and hierarchy
 * - Vibrant red (#DC2626) for safety-critical warnings
 * - Hair photos are the hero - neutral UI doesn't compete
 * - Apropiado para estilistas profesionales
 */

export default {
  light: {
    // ==========================================
    // PRIMARY COLORS - Brand Identity
    // ==========================================

    /** Slate 600 - Professional blue-gray for primary buttons, headers, active elements */
    primary: '#475569',

    /** Slate 700 - Darker slate for hover states, pressed buttons, secondary emphasis */
    primaryDark: '#334155',

    // ==========================================
    // UI ACCENTS - Interactive Elements
    // ==========================================

    /** Slate 500 - For tabs, links, toggles, interactive UI hints */
    uiAccent: '#64748B',

    /** Slate 400 - For hover states on interactive elements */
    uiAccentLight: '#94A3B8',

    // ==========================================
    // BACKGROUNDS - Warm Neutrals
    // ==========================================

    /** Pure white - Main background */
    background: '#FFFFFF',

    /** Gray 50 - Subtle secondary background (screens, sections) */
    backgroundSecondary: '#F9FAFB',

    /** Gray 100 - Elevated surfaces (cards, modals) */
    surface: '#F3F4F6',

    // ==========================================
    // TEXT - Hierarchy by Contrast
    // ==========================================

    /** Gray 900 - Primary text (not pure black - softer) */
    text: '#111827',

    /** Gray 600 - Secondary text (descriptions, labels) */
    textSecondary: '#4B5563',

    /** Gray 400 - Tertiary text (timestamps, metadata, placeholders) */
    textTertiary: '#9CA3AF',

    /** Gray 300 - Disabled text */
    textDisabled: '#D1D5DB',

    // ==========================================
    // BORDERS & DIVIDERS - Ultra Subtle
    // ==========================================

    /** Gray 200 - Visible borders (cards, inputs) */
    border: '#E5E7EB',

    /** Gray 100 - Light borders (subtle separation) */
    borderLight: '#F3F4F6',

    /** Gray 50 - Dividers (almost invisible) */
    divider: '#F9FAFB',

    // ==========================================
    // CHAT & MESSAGES
    // ==========================================

    /** Gray 50 - AI message background */
    aiMessage: '#F9FAFB',

    /** Slate 600 - User message background */
    userMessage: '#475569',

    // ==========================================
    // STATUS COLORS - Semantic
    // ==========================================

    /** Emerald 600 - Success states (muted but clear) */
    success: '#059669',

    /** Emerald 100 - Success backgrounds */
    successLight: '#D1FAE5',

    /** Red 600 - Safety-critical warnings (MUST be vibrant) */
    warning: '#DC2626',

    /** Red 100 - Warning backgrounds */
    warningLight: '#FEE2E2',

    /** Red 600 - Error states (MUST be vibrant for safety) */
    error: '#DC2626',

    /** Red 100 - Error backgrounds */
    errorLight: '#FEE2E2',

    // ==========================================
    // SHADOWS & OVERLAYS
    // ==========================================

    /** Slate 900 for shadows (softer than pure black) */
    shadow: '#0F172A',

    /** Modal/drawer overlays (Slate with opacity) */
    overlay: 'rgba(15, 23, 42, 0.4)',

    // ==========================================
    // DEPRECATED - Backward Compatibility
    // ==========================================
    // These will be removed in v3.0 - migrate to new names above

    /** @deprecated Use 'primaryDark' instead */
    primaryLight: '#334155',

    /** @deprecated Use 'uiAccent' instead */
    accent: '#64748B',

    /** @deprecated Use 'uiAccentLight' instead */
    accentLight: '#94A3B8',

    /** @deprecated Use 'textTertiary' instead */
    textLight: '#9CA3AF',

    /** @deprecated Use 'primary' instead */
    info: '#475569',

    /** @deprecated Avoid gradients in v2 palette */
    gradientStart: '#475569',

    /** @deprecated Avoid gradients in v2 palette */
    gradientEnd: '#334155',
  },
};
