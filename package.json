{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "bunx rork start -p rvdbus1wkvfzntthiqzvc --tunnel", "start-web": "bunx rork start -p rvdbus1wkvfzntthiqzvc --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p rvdbus1wkvfzntthiqzvc --web --tunnel", "lint": "expo lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --maxWorkers=2", "test:debug": "node --inspect-brk node_modules/.bin/jest --runInBand", "test:unit": "jest --testPathPatterns=__tests__/unit", "test:integration": "jest --testPathPatterns=__tests__/integration", "test:security": "jest --config jest.config.security.js"}, "dependencies": {"@ai-sdk/react": "^2.0.76", "@expo/vector-icons": "^15.0.2", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "2.2.0", "@react-navigation/native": "~7.1.18", "@stardazed/streams-text-encoding": "^1.0.2", "@supabase/supabase-js": "^2.76.1", "@tanstack/eslint-plugin-query": "^5.91.2", "@tanstack/react-query": "^5.90.5", "@ungap/structured-clone": "^1.3.0", "expo": "~54.0.0", "expo-blur": "~15.0.7", "expo-camera": "~17.0.8", "expo-constants": "~18.0.10", "expo-font": "~14.0.9", "expo-haptics": "~15.0.7", "expo-image": "~3.0.10", "expo-image-manipulator": "~14.0.7", "expo-image-picker": "~17.0.8", "expo-linear-gradient": "~15.0.7", "expo-linking": "~8.0.8", "expo-location": "~19.0.7", "expo-router": "~6.0.13", "expo-splash-screen": "~31.0.10", "expo-status-bar": "~3.0.8", "expo-symbols": "~1.0.7", "expo-system-ui": "~6.0.8", "expo-web-browser": "~15.0.8", "lucide-react-native": "^0.547.0", "nativewind": "^4.1.23", "react": "19.1.0", "react-dom": "19.1.0", "react-native": "0.81.5", "react-native-gesture-handler": "~2.28.0", "react-native-safe-area-context": "~5.6.0", "react-native-screens": "~4.16.0", "react-native-svg": "15.12.1", "react-native-web": "~0.21.0", "zod": "^4.1.12", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@types/jest": "^30.0.0", "@types/react": "~19.1.10", "@typescript-eslint/eslint-plugin": "^8.40.0", "@typescript-eslint/parser": "^8.40.0", "eslint": "9.31.0", "eslint-config-expo": "~10.0.0", "jest": "^30.2.0", "jest-expo": "^54.0.13", "pg": "^8.16.3", "react-test-renderer": "^19.2.0", "ts-jest": "^29.4.5", "typescript": "^5.8.3"}, "private": true}