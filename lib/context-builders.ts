/**
 * Context Builders for AI Contextual Awareness
 *
 * These functions build AppContext objects that are sent to ai-proxy
 * to provide the AI with awareness of where the user is in the app
 * and what they're doing.
 */

import type {
  ConversationIntent,
  ConversationMemorySnapshot,
  FormulaData,
  FormulaStepContext,
  MainChatContext,
} from '@/types';
import { loadPreferences } from './brands/preferences';
import type { UserBrandPreferences, BrandPreference } from '@/types';

// Cache for brand preferences with TTL
let cachedPreferences: UserBrandPreferences | null = null;
let cacheTimestamp = 0;
const CACHE_TTL = 5 * 60 * 1000; // 5 minutes

/**
 * Build context for formula step 5 chat
 * Provides the AI with details about the current formula being created
 * @param formulaData - The formula data from FormulaContext
 * @param generatedFormula - Optional: The generated formula text (if already generated)
 * @returns FormulaStepContext or null if formulaData is invalid
 */
export const buildFormulaContext = (
  formulaData: FormulaData | undefined,
  generatedFormula?: string,
  extras?: {
    memory?: ConversationMemorySnapshot | null;
    intent?: ConversationIntent;
  }
): FormulaStepContext | null => {
  if (!formulaData) {
    console.warn('[buildFormulaContext] formulaData is undefined');
    return null;
  }

  const current = formulaData.currentColorAnalysis;
  const desired = formulaData.desiredColorAnalysis;

  const context: FormulaStepContext = {
    screen: 'formula_step_5',
    currentFormula: {
      naturalBase: current?.roots?.level,
      grayPercentage: current?.grayAnalysis?.percentage,
      currentTone: current?.roots?.tone,
      desiredLevel: desired?.level?.toString(),
      desiredTone: desired?.tone,
      selectedBrand: formulaData.brand,
      productLine: formulaData.productLine,
      chemicalHistory: current?.chemicalHistory?.lastProcessType
        ? [current.chemicalHistory.lastProcessType]
        : undefined,
      technique: formulaData.technique,
      generatedFormula, // 🆕 NEW: Include the generated formula if available
    },
    memory: extras?.memory ?? null,
    intent: extras?.intent,
  };

  // Only log metadata in production (not full context for privacy)
  if (__DEV__) {
    console.log('[buildFormulaContext] 🎨 Built context (dev only):', {
      screen: context.screen,
      hasBrand: !!formulaData.brand,
      hasGeneratedFormula: !!generatedFormula,
      formulaLength: generatedFormula?.length || 0,
      hasMemory: !!extras?.memory,
      intent: extras?.intent,
    });
  }

  return context;
};

/**
 * Build context for main chat
 * Provides the AI with user brand preferences
 * Uses 5-minute cache to avoid AsyncStorage reads on every message
 */
export const buildMainChatContext = async (
  overrides?: {
    memory?: ConversationMemorySnapshot | null;
    intent?: ConversationIntent;
  }
): Promise<MainChatContext> => {
  try {
    const now = Date.now();

    // Check cache validity
    if (!cachedPreferences || (now - cacheTimestamp) > CACHE_TTL) {
      console.log('[ContextBuilder] Cache miss or expired, loading preferences from storage');
      cachedPreferences = await loadPreferences();
      cacheTimestamp = now;
    } else {
      console.log('[ContextBuilder] Using cached preferences (age:', Math.round((now - cacheTimestamp) / 1000), 'seconds)');
    }

    const brands = cachedPreferences.brands
      .filter((b: BrandPreference) => b.isPrimary || b.preferredLines.length > 0)
      .map((b: BrandPreference) => b.brandId);

    return {
      screen: 'main_chat',
      userPreferences: {
        brands: brands.length > 0 ? brands : [],
      },
      memory: overrides?.memory ?? null,
      intent: overrides?.intent,
    };
  } catch (error) {
    console.error('[ContextBuilder] Error loading brand preferences:', error);
    return {
      screen: 'main_chat',
      userPreferences: {
        brands: [],
      },
      memory: overrides?.memory ?? null,
      intent: overrides?.intent,
    };
  }
};

/**
 * Invalidate the preferences cache
 * Call this when user updates their brand preferences
 */
export const invalidatePreferencesCache = (): void => {
  console.log('[ContextBuilder] Preferences cache invalidated');
  cachedPreferences = null;
  cacheTimestamp = 0;
};

/**
 * Sanitize context to remove any sensitive data before sending to AI
 * Validates data types, limits sizes, and strips potentially harmful content
 */
export const sanitizeContext = (
  context: FormulaStepContext | MainChatContext | null
): FormulaStepContext | MainChatContext | null => {
  if (!context) return null;

  try {
    // Deep clone to avoid mutations
    const sanitized = JSON.parse(JSON.stringify(context));

    if (sanitized.screen === 'formula_step_5' && sanitized.currentFormula) {
      const f = sanitized.currentFormula;

      // Sanitize string fields (remove special characters, limit length)
      const stringFields = ['currentTone', 'desiredTone', 'selectedBrand', 'productLine', 'technique'] as const;
      for (const field of stringFields) {
        if (f[field]) {
          // Remove potential injection characters
          f[field] = f[field]
            .replace(/[<>{}[\]]/g, '') // Remove brackets/braces
            .replace(/[\r\n\t]/g, ' ') // Replace newlines with spaces
            .trim()
            .slice(0, 100); // Limit length
        }
      }

      // Sanitize generatedFormula (most critical)
      if (f.generatedFormula) {
        f.generatedFormula = f.generatedFormula
          .replace(/<\|im_start\|>/gi, '') // Remove LLM control tokens
          .replace(/<\|im_end\|>/gi, '')
          .trim()
          .slice(0, 20000); // Limit length (20KB max)
      }

      // Sanitize array fields
      if (f.chemicalHistory && Array.isArray(f.chemicalHistory)) {
        f.chemicalHistory = f.chemicalHistory
          .filter((item: unknown) => typeof item === 'string')
          .map((item: string) => item.slice(0, 100))
          .slice(0, 20); // Limit array size
      }

      // Validate numeric fields
      if (f.naturalBase !== undefined) {
        f.naturalBase = Math.max(1, Math.min(10, f.naturalBase));
      }
      if (f.grayPercentage !== undefined) {
        f.grayPercentage = Math.max(0, Math.min(100, f.grayPercentage));
      }

      if (sanitized.memory) {
        if (typeof sanitized.memory.summary === 'string') {
          sanitized.memory.summary = sanitized.memory.summary.slice(0, 500);
        }

        if (Array.isArray(sanitized.memory.keyFacts)) {
          sanitized.memory.keyFacts = sanitized.memory.keyFacts
            .filter((fact: unknown) => typeof fact === 'string')
            .map((fact: string) => fact.replace(/[\r\n\t]/g, ' ').slice(0, 200))
            .slice(0, 15);
        } else {
          sanitized.memory.keyFacts = [];
        }

        if (Array.isArray(sanitized.memory.followUpQuestions)) {
          sanitized.memory.followUpQuestions = sanitized.memory.followUpQuestions
            .filter((item: unknown) => typeof item === 'string')
            .map((item: string) => item.replace(/[\r\n\t]/g, ' ').slice(0, 200))
            .slice(0, 10);
        } else {
          sanitized.memory.followUpQuestions = [];
        }

        if (sanitized.memory.lastIntent && typeof sanitized.memory.lastIntent === 'string') {
          sanitized.memory.lastIntent = sanitized.memory.lastIntent.replace(/[^a-zñ]+/gi, '').slice(0, 30);
        } else {
          sanitized.memory.lastIntent = undefined;
        }
      }

      if (sanitized.intent && typeof sanitized.intent === 'string') {
        sanitized.intent = sanitized.intent.replace(/[^a-zñ]+/gi, '').slice(0, 30);
      } else {
        sanitized.intent = undefined;
      }
    }

    if (sanitized.screen === 'main_chat' && sanitized.userPreferences) {
      // Sanitize brands array
      if (Array.isArray(sanitized.userPreferences.brands)) {
        sanitized.userPreferences.brands = sanitized.userPreferences.brands
          .filter((brand: unknown) => typeof brand === 'string')
          .map((brand: string) => brand.replace(/[^a-zA-Z0-9\s'-]/g, '').slice(0, 100))
          .slice(0, 20); // Limit array size
      }

      if (sanitized.memory) {
        if (typeof sanitized.memory.summary === 'string') {
          sanitized.memory.summary = sanitized.memory.summary.slice(0, 500);
        }

        if (Array.isArray(sanitized.memory.keyFacts)) {
          sanitized.memory.keyFacts = sanitized.memory.keyFacts
            .filter((fact: unknown) => typeof fact === 'string')
            .map((fact: string) => fact.replace(/[\r\n\t]/g, ' ').slice(0, 200))
            .slice(0, 15);
        } else {
          sanitized.memory.keyFacts = [];
        }

        if (Array.isArray(sanitized.memory.followUpQuestions)) {
          sanitized.memory.followUpQuestions = sanitized.memory.followUpQuestions
            .filter((item: unknown) => typeof item === 'string')
            .map((item: string) => item.replace(/[\r\n\t]/g, ' ').slice(0, 200))
            .slice(0, 10);
        } else {
          sanitized.memory.followUpQuestions = [];
        }

        if (sanitized.memory.lastIntent && typeof sanitized.memory.lastIntent === 'string') {
          sanitized.memory.lastIntent = sanitized.memory.lastIntent.replace(/[^a-zñ]+/gi, '').slice(0, 30);
        } else {
          sanitized.memory.lastIntent = undefined;
        }
      }

      if (sanitized.intent && typeof sanitized.intent === 'string') {
        sanitized.intent = sanitized.intent.replace(/[^a-zñ]+/gi, '').slice(0, 30);
      } else {
        sanitized.intent = undefined;
      }
    }

    return sanitized;
  } catch (error) {
    console.error('[sanitizeContext] Sanitization failed:', error);
    // Return null to prevent sending potentially malformed data
    return null;
  }
};
