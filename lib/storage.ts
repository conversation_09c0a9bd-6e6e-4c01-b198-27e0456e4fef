import { Platform } from 'react-native';
import { supabase } from './supabase';
import * as FileSystem from 'expo-file-system/legacy';

/**
 * Helper to read image as base64, handling both native URIs and web blob URLs
 */
async function readImageAsBase64(uri: string): Promise<string> {
  // En web, blob URLs necesitan ser leídos con fetch
  if (Platform.OS === 'web' && uri.startsWith('blob:')) {
    const response = await fetch(uri);
    const blob = await response.blob();
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onloadend = () => {
        const base64 = reader.result as string;
        // Remover el prefijo "data:image/xxx;base64," si existe
        const base64Data = base64.split(',')[1] || base64;
        resolve(base64Data);
      };
      reader.onerror = reject;
      reader.readAsDataURL(blob);
    });
  }

  // En nativo, usar FileSystem
  return FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });
}

/**
 * Sube una foto de consulta del chat (análisis temporal)
 * Retención: 14 días (minimización de datos GDPR)
 * Path: userId/consultations/timestamp-random.jpg
 *
 * @param imageUri - URI local de la imagen (file://, content://, o blob: en web)
 * @returns URL firmada de la imagen subida (válida por 24 horas)
 *
 * @example
 * const signedUrl = await uploadConsultationPhoto('file:///path/to/image.jpg');
 * const signedUrl = await uploadConsultationPhoto('blob:http://localhost:8081/...');
 */
export async function uploadConsultationPhoto(
  imageUri: string
): Promise<string> {
  // 1. Verificar autenticación
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    throw new Error('Usuario no autenticado. Por favor, inicia sesión.');
  }

  const userId = user.id;

  // 2. Generar nombre de archivo único con timestamp y random
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  const fileName = `${timestamp}-${random}.jpg`;

  // 3. Path para consultas: userId/consultations/fileName.jpg
  const filePath = `${userId}/consultations/${fileName}`;

  // 4. Leer archivo como base64 (soporta blob URLs en web y file:// en nativo)
  const base64 = await readImageAsBase64(imageUri);

  // 5. Convertir base64 a ArrayBuffer para Supabase
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);

  // 6. Upload a Supabase Storage
  const { data, error } = await supabase.storage
    .from('hair-photos')
    .upload(filePath, byteArray, {
      contentType: 'image/jpeg',
      cacheControl: '3600',
      upsert: false,
    });

  if (error) {
    console.error('Error al subir imagen de consulta:', error);
    throw new Error(`Error al subir imagen: ${error.message}`);
  }

  // 7. Generar signed URL (expires en 24 horas = 86400 segundos)
  // Duración aumentada para soportar conversaciones multi-turn
  const { data: signedUrlData, error: signedError } = await supabase.storage
    .from('hair-photos')
    .createSignedUrl(data.path, 86400);

  if (signedError) {
    console.error('Error al generar URL firmada:', signedError);
    throw new Error(`Error al generar URL: ${signedError.message}`);
  }

  console.log('✅ Imagen de consulta subida exitosamente:', data.path);
  console.log('   Signed URL válida por 24 horas');
  return signedUrlData.signedUrl;
}

/**
 * Sube una foto del workflow de fórmulas (documentación formal del cliente)
 * Retención: 90 días (seguimiento post-servicio)
 * Path: userId/formulas/clientId/timestamp-random.jpg
 * Requiere consentimiento explícito del cliente (checkbox en step3)
 *
 * @param imageUri - URI local de la imagen (file://, content://, o blob: en web)
 * @param clientId - ID del cliente (obligatorio para fórmulas)
 * @returns URL firmada de la imagen subida (válida por 1 hora)
 *
 * @example
 * const signedUrl = await uploadFormulaPhoto('file:///path/to/image.jpg', 'client-123');
 * const signedUrl = await uploadFormulaPhoto('blob:http://localhost:8081/...', 'client-123');
 */
export async function uploadFormulaPhoto(
  imageUri: string,
  clientId: string
): Promise<string> {
  // 1. Verificar autenticación
  const { data: { user }, error: authError } = await supabase.auth.getUser();

  if (authError || !user) {
    throw new Error('Usuario no autenticado. Por favor, inicia sesión.');
  }

  const userId = user.id;

  // 2. Generar nombre de archivo único con timestamp y random
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(7);
  const fileName = `${timestamp}-${random}.jpg`;

  // 3. Path para fórmulas: userId/formulas/clientId/fileName.jpg
  const filePath = `${userId}/formulas/${clientId}/${fileName}`;

  // 4. Leer archivo como base64 (soporta blob URLs en web y file:// en nativo)
  const base64 = await readImageAsBase64(imageUri);

  // 5. Convertir base64 a ArrayBuffer para Supabase
  const byteCharacters = atob(base64);
  const byteNumbers = new Array(byteCharacters.length);
  for (let i = 0; i < byteCharacters.length; i++) {
    byteNumbers[i] = byteCharacters.charCodeAt(i);
  }
  const byteArray = new Uint8Array(byteNumbers);

  // 6. Upload a Supabase Storage
  const { data, error } = await supabase.storage
    .from('hair-photos')
    .upload(filePath, byteArray, {
      contentType: 'image/jpeg',
      cacheControl: '3600',
      upsert: false,
    });

  if (error) {
    console.error('Error al subir imagen de fórmula:', error);
    throw new Error(`Error al subir imagen: ${error.message}`);
  }

  // 7. Generar signed URL (expires en 1 hora)
  const { data: signedUrlData, error: signedError } = await supabase.storage
    .from('hair-photos')
    .createSignedUrl(data.path, 3600);

  if (signedError) {
    console.error('Error al generar URL firmada:', signedError);
    throw new Error(`Error al generar URL: ${signedError.message}`);
  }

  console.log('✅ Imagen de fórmula subida exitosamente:', data.path);
  return signedUrlData.signedUrl;
}

/**
 * Elimina una imagen del storage (funciona para consultations y formulas)
 *
 * @param storagePath - Path completo del archivo en storage
 *
 * @example
 * await deleteHairPhoto('user-123/consultations/1234567890-abc123.jpg');
 * await deleteHairPhoto('user-123/formulas/client-456/1234567890-abc123.jpg');
 */
export async function deleteHairPhoto(storagePath: string): Promise<void> {
  const { error } = await supabase.storage
    .from('hair-photos')
    .remove([storagePath]);

  if (error) {
    console.error('Error al eliminar imagen de Supabase Storage:', error);
    throw new Error(`Error al eliminar imagen: ${error.message}`);
  }

  console.log('✅ Imagen eliminada exitosamente:', storagePath);
}

/**
 * Obtiene una signed URL de una imagen existente
 *
 * @param storagePath - Path completo del archivo en storage
 * @param expiresIn - Tiempo de expiración en segundos (default: 1 hora)
 * @returns URL firmada de la imagen
 */
export async function getSignedUrl(
  storagePath: string,
  expiresIn: number = 3600
): Promise<string> {
  const { data, error } = await supabase.storage
    .from('hair-photos')
    .createSignedUrl(storagePath, expiresIn);

  if (error) {
    console.error('Error al generar URL firmada:', error);
    throw new Error(`Error al generar URL: ${error.message}`);
  }

  return data.signedUrl;
}
