/**
 * Brand Preferences Module
 *
 * Handles storage and synchronization of user brand preferences
 * Strategy: AsyncStorage (local/fast) + Supabase (remote/multi-device)
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { supabase } from '../supabase';
import type { UserBrandPreferences } from '@/types';

const STORAGE_KEY = 'user_brand_preferences';

/**
 * Get preferences from AsyncStorage (local, instant)
 */
export async function getLocalPreferences(): Promise<UserBrandPreferences | null> {
  try {
    const json = await AsyncStorage.getItem(STORAGE_KEY);
    if (!json) return null;

    const data = JSON.parse(json);
    // Convert ISO string back to Date
    data.updatedAt = new Date(data.updatedAt);
    return data;
  } catch (error) {
    console.error('Error reading local brand preferences:', error);
    return null;
  }
}

/**
 * Save preferences to AsyncStorage (local, instant)
 */
export async function saveLocalPreferences(prefs: UserBrandPreferences): Promise<void> {
  try {
    await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(prefs));
  } catch (error) {
    console.error('Error saving local brand preferences:', error);
    throw error;
  }
}

/**
 * Get preferences from Supabase (remote, multi-device)
 */
export async function getSupabasePreferences(): Promise<UserBrandPreferences | null> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) return null;

    const { data, error } = await supabase
      .from('user_brand_preferences')
      .select('brand_preferences')
      .eq('user_id', user.id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // No rows found - user hasn't saved preferences yet
        return null;
      }
      throw error;
    }

    if (!data?.brand_preferences) return null;

    // Convert ISO string back to Date
    const prefs = data.brand_preferences;
    if (prefs.updatedAt) {
      prefs.updatedAt = new Date(prefs.updatedAt);
    }

    return prefs;
  } catch (error) {
    console.error('Error reading Supabase brand preferences:', error);
    return null;
  }
}

/**
 * Save preferences to Supabase (remote, multi-device)
 * Non-blocking - errors are logged but don't throw
 */
export async function syncPreferencesToSupabase(prefs: UserBrandPreferences): Promise<void> {
  try {
    const { data: { user } } = await supabase.auth.getUser();
    if (!user) {
      console.warn('Cannot sync brand preferences: user not authenticated');
      return;
    }

    const { error } = await supabase
      .from('user_brand_preferences')
      .upsert({
        user_id: user.id,
        brand_preferences: prefs,
        updated_at: new Date().toISOString(),
      }, {
        onConflict: 'user_id'
      });

    if (error) throw error;

    console.log('✅ Brand preferences synced to Supabase');
  } catch (error) {
    console.error('Failed to sync preferences to Supabase:', error);
    // Don't throw - background sync failure should not block UI
  }
}

/**
 * Load preferences (hybrid strategy: local first, then remote)
 *
 * 1. Try AsyncStorage (instant)
 * 2. If not found, try Supabase
 * 3. If found in Supabase, cache to AsyncStorage
 * 4. Return default if nothing found
 */
export async function loadPreferences(): Promise<UserBrandPreferences> {
  // 1. Try local first (instant)
  const local = await getLocalPreferences();
  if (local) {
    console.log('📦 Loaded brand preferences from local storage');
    return local;
  }

  // 2. Try Supabase if not found locally
  const remote = await getSupabasePreferences();
  if (remote) {
    console.log('☁️ Loaded brand preferences from Supabase');
    // Cache to local for next time
    await saveLocalPreferences(remote).catch(err =>
      console.warn('Failed to cache remote preferences locally:', err)
    );
    return remote;
  }

  // 3. Return default if nothing found
  console.log('🆕 No saved brand preferences, using defaults');
  return {
    brands: [],
    updatedAt: new Date(),
  };
}

/**
 * Save preferences (hybrid strategy: local + background remote sync)
 *
 * 1. Save to AsyncStorage immediately (instant UX)
 * 2. Sync to Supabase in background (don't block UI)
 */
export async function savePreferences(prefs: UserBrandPreferences): Promise<void> {
  // Update timestamp
  prefs.updatedAt = new Date();

  // 1. Save local first (blocks UI, but instant)
  await saveLocalPreferences(prefs);
  console.log('💾 Saved brand preferences to local storage');

  // 2. Sync to Supabase in background (non-blocking)
  syncPreferencesToSupabase(prefs).catch(err =>
    console.warn('Background sync failed, preferences saved locally:', err)
  );
}

/**
 * Clear all preferences (both local and remote)
 */
export async function clearPreferences(): Promise<void> {
  try {
    // Clear local
    await AsyncStorage.removeItem(STORAGE_KEY);

    // Clear remote
    const { data: { user } } = await supabase.auth.getUser();
    if (user) {
      await supabase
        .from('user_brand_preferences')
        .delete()
        .eq('user_id', user.id);
    }

    console.log('🗑️ Cleared brand preferences');
  } catch (error) {
    console.error('Error clearing brand preferences:', error);
    throw error;
  }
}
