import * as ImageManipulator from 'expo-image-manipulator';

/**
 * Procesa una imagen para análisis de cabello:
 * 1. Resize para optimizar costos
 * 2. Compresión para reducir bandwidth
 *
 * NOTA: Face blur está pendiente de implementación.
 * Requiere instalar @react-native-community/blur o similar.
 * Por ahora solo hacemos resize y compresión.
 *
 * @param uri - URI local de la imagen
 * @returns URI de la imagen procesada
 */
export async function processImageForHairAnalysis(uri: string): Promise<string> {
  try {
    console.log('[ImageProcessor] Processing image:', uri);

    // Resize para optimizar costos de OpenAI y evitar timeouts en edge function
    // Máximo 896px en lado más largo (reducido de 1024px para evitar timeouts con GPT-4o Vision)
    // Con 896px: 1024x2220 → 896x1942 (~30% menos payload, más rápido para procesar)
    // Compresión agresiva al 60% (reducido de 75% para reducir payload)
    const { uri: finalUri, width, height } = await ImageManipulator.manipulateAsync(
      uri,
      [{ resize: { width: 896 } }], // Mantiene aspect ratio, reducido para evitar timeouts
      { compress: 0.6, format: ImageManipulator.SaveFormat.JPEG }
    );

    console.log(`[ImageProcessor] Original URI: ${uri}`);
    console.log(`[ImageProcessor] Processed image: ${width}x${height}`);
    console.log(`[ImageProcessor] Final URI: ${finalUri}`);

    return finalUri;

  } catch (error) {
    console.error('[ImageProcessor] Error processing image:', error);

    // Fallback: devolver imagen original si el procesamiento falla
    console.warn('[ImageProcessor] Returning original image due to processing error');
    return uri;
  }
}

/**
 * Procesa múltiples imágenes en paralelo
 */
export async function processMultipleImages(uris: string[]): Promise<string[]> {
  console.log(`[ImageProcessor] Processing ${uris.length} images...`);

  const processPromises = uris.map(uri => processImageForHairAnalysis(uri));
  const processedUris = await Promise.all(processPromises);

  console.log(`[ImageProcessor] Processed ${processedUris.length} images successfully`);
  return processedUris;
}
