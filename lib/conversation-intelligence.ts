import { generateTextSafe } from './ai-client';
import { logger } from './logger';
import type {
  ConversationIntent,
  ConversationMemorySnapshot,
  Message,
} from '@/types';

export interface IntentDetectionParams {
  userMessage: string;
  recentMessages: Message[];
  memory?: ConversationMemorySnapshot | null;
}

export interface IntentDetectionResult {
  intent: ConversationIntent;
  confidence: number;
  reasoning?: string;
}

export interface SummarizeConversationParams {
  messages: Message[];
  previous?: ConversationMemorySnapshot | null;
  intentHint?: ConversationIntent;
}

const INTENT_SYSTEM_PROMPT = `Eres un analista de conversaciones para un asistente de coloración capilar profesional.
Tu objetivo es entender lo que necesita el usuario.

Responde siempre en JSON con la forma:
{
  "intent": "...",
  "confidence": 0.0,
  "reasoning": "..."
}

Intents permitidas:
- diagnostico (analizar situación actual del cabello)
- formula (pedir o ajustar formulaciones químicas)
- soporte (resolución de problemas tras una fórmula o dudas técnicas)
- productos (búsqueda de productos, marcas o líneas)
- educacion (explicar conceptos o formación)
- seguimiento (planificar próximos pasos o mantenimiento)
- saludo (interacción social sin petición técnica)
- otro (cuando no encaje en las anteriores).

Cuando tengas dudas, usa "otro".`;

const SUMMARY_SYSTEM_PROMPT = `Eres un experto en gestión de memoria para un asistente de coloración capilar.
Toma los últimos mensajes y genera memoria estructurada útil para continuar la conversación.

Responde en JSON con la forma:
{
  "summary": "...",
  "key_facts": ["..."],
  "follow_up_questions": ["..."]
}

- "summary": síntesis breve (máximo 3 frases) de lo más relevante para continuar ayudando.
- "key_facts": lista de datos concretos que no se deben olvidar (niveles, marcas, restricciones, objetivos).
- "follow_up_questions": preguntas pendientes o tareas a revisar en la siguiente interacción.
- Usa español neutro y evita duplicados.
- Si no hay información aún, usa cadenas vacías.

IMPORTANTE - Privacidad y GDPR:
- NO incluyas nombres de clientes en el resumen.
- NO incluyas datos de salud específicos (alergias, embarazo, condiciones médicas).
- Usa términos genéricos: "cliente actual", "restricciones de salud conocidas", "precauciones documentadas".
- Enfócate en niveles de color, marcas, técnicas y objetivos técnicos únicamente.`;

type ParsedJSON<T> = {
  ok: true;
  value: T;
} | {
  ok: false;
};

function safeJsonParse<T>(payload: string): ParsedJSON<T> {
  try {
    const parsed = JSON.parse(payload) as T;
    return { ok: true, value: parsed };
  } catch (error) {
    const preview = payload?.slice(0, 500);
    logger.error('ConversationIntelligence', 'JSON parse failed', { error, preview });
    return { ok: false };
  }
}

function buildTranscript(messages: Message[], limit = 10): string {
  return messages
    .slice(-limit)
    .map((msg) => {
      const roleLabel = msg.role === 'assistant' ? 'Asistente' : 'Usuario';
      const content = msg.content?.trim() || '';
      return `${roleLabel}: ${content}`;
    })
    .join('\n');
}

export async function detectConversationIntent(
  params: IntentDetectionParams
): Promise<IntentDetectionResult> {
  const { userMessage, recentMessages, memory } = params;

  const memorySnippet = memory?.summary
    ? `Resumen previo: ${memory.summary}\nDatos clave: ${memory.keyFacts.join('; ')}`
    : 'Sin memoria previa relevante.';

  const transcript = buildTranscript(recentMessages.concat([{ role: 'user', content: userMessage, id: 'current', timestamp: new Date() } as Message ]));

  const userPrompt = `Mensaje del usuario: "${userMessage}"

Contexto reciente:
${transcript}

Memoria conocida:
${memorySnippet}

Analiza la intención actual.`;

  try {
    const raw = await generateTextSafe({
      messages: [
        { role: 'system', content: INTENT_SYSTEM_PROMPT },
        { role: 'user', content: userPrompt },
      ],
      maxRetries: 0,
      retryDelay: 500,
      temperature: 0,
      useCase: 'chat',
      skipThrottle: true,
    });

    const parsed = safeJsonParse<{ intent?: ConversationIntent; confidence?: number; reasoning?: string }>(raw);

    if (parsed.ok && parsed.value.intent) {
      const confidence = typeof parsed.value.confidence === 'number'
        ? Math.max(0, Math.min(1, parsed.value.confidence))
        : 0.5;

      const intent: ConversationIntent = parsed.value.intent || 'otro';

      return {
        intent,
        confidence,
        reasoning: parsed.value.reasoning,
      };
    }
  } catch (error) {
    logger.error('ConversationIntelligence', 'Intent detection failed', error);
  }

  return {
    intent: 'otro',
    confidence: 0,
    reasoning: undefined,
  };
}

export async function summarizeConversation(
  params: SummarizeConversationParams
): Promise<ConversationMemorySnapshot> {
  const { messages, previous, intentHint } = params;

  const transcript = buildTranscript(messages, 12);
  const historySummary = previous?.summary ? previous.summary : 'Sin memoria previa.';

  const intentBlock = intentHint || previous?.lastIntent
    ? `Intención más reciente: ${intentHint || previous?.lastIntent}`
    : 'Intención más reciente: desconocida';

  const userPrompt = `${intentBlock}

Memoria previa:
${historySummary}

Mensajes recientes:
${transcript}

Genera memoria actualizada.`;

  try {
    const raw = await generateTextSafe({
      messages: [
        { role: 'system', content: SUMMARY_SYSTEM_PROMPT },
        { role: 'user', content: userPrompt },
      ],
      maxRetries: 1,
      retryDelay: 750,
      temperature: 0.1,
      useCase: 'chat',
      skipThrottle: true,
    });

    const parsed = safeJsonParse<{
      summary?: string;
      key_facts?: string[];
      follow_up_questions?: string[];
    }>(raw);

    if (parsed.ok) {
      const summary = (parsed.value.summary || '').trim();
      const keyFacts = Array.isArray(parsed.value.key_facts)
        ? parsed.value.key_facts.filter((item) => typeof item === 'string' && item.trim().length > 0)
        : [];
      const followUps = Array.isArray(parsed.value.follow_up_questions)
        ? parsed.value.follow_up_questions.filter((item) => typeof item === 'string' && item.trim().length > 0)
        : [];

      return {
        summary,
        keyFacts,
        followUpQuestions: followUps,
        lastIntent: intentHint || previous?.lastIntent,
        updatedAt: new Date(),
      };
    }
  } catch (error) {
    logger.error('ConversationIntelligence', 'Conversation summary failed', error);
  }

  return {
    summary: previous?.summary || '',
    keyFacts: previous?.keyFacts || [],
    followUpQuestions: previous?.followUpQuestions || [],
    lastIntent: intentHint || previous?.lastIntent,
    updatedAt: new Date(),
  };
}

export function formatMemoryForPrompt(
  memory?: ConversationMemorySnapshot | null
): string {
  if (!memory) {
    return '## Datos recordados\n- Sin información previa relevante.';
  }

  const lines: string[] = [];

  if (memory.summary) {
    lines.push(`Resumen: ${memory.summary}`);
  }

  if (memory.keyFacts.length > 0) {
    lines.push('Hechos clave:');
    memory.keyFacts.forEach((fact) => {
      lines.push(`- ${fact}`);
    });
  }

  if (memory.followUpQuestions.length > 0) {
    lines.push('Preguntas pendientes:');
    memory.followUpQuestions.forEach((item) => {
      lines.push(`- ${item}`);
    });
  }

  return `## Datos recordados\n${lines.join('\n')}`;
}

export function describeIntent(intent: ConversationIntent | undefined): string {
  if (!intent) {
    return '## Intención detectada\n- No hay intención clara todavía.';
  }

  const mapping: Record<ConversationIntent, string> = {
    diagnostico: 'Diagnóstico técnico del estado actual del cabello.',
    formula: 'Diseño o ajuste de fórmulas de coloración.',
    soporte: 'Resolución de problemas o incidencias tras el servicio.',
    productos: 'Consulta sobre productos, marcas o líneas específicas.',
    educacion: 'Explicación de conceptos o formación profesional.',
    seguimiento: 'Planificación de próximos pasos, mantenimiento o agenda.',
    saludo: 'Interacción social o saludo sin petición técnica.',
    otro: 'No encaja en las categorías principales.',
  };

  return `## Intención detectada\n- ${mapping[intent]}`;
}
