/**
 * Supabase persistence layer for Formula History
 * CRUD operations for formulas and formula_notes tables
 */

import { supabase } from './supabase';
import type { Formula, FormulaNotes, ProductUsed, ServiceType, NoteType } from '@/types';

// =====================================================
// TYPE TRANSFORMATIONS (DB snake_case ↔ TS camelCase)
// =====================================================

type FormulaDB = {
  id: string;
  client_id: string;
  user_id: string;
  session_number: number;
  service_type: ServiceType;
  current_level?: number;
  target_level?: number;
  target_tone?: string;
  target_reflection?: string;
  brand: string;
  product_line?: string;
  technique?: string;
  formula_text: string;
  products_used: ProductUsed[];
  total_cost?: number;
  estimated_duration_minutes?: number;
  current_color_analysis?: any;
  desired_color_analysis?: any;
  safety_checklist?: any;
  created_at: string;
  updated_at: string;
};

type FormulaNotesDB = {
  id: string;
  formula_id: string;
  user_id: string;
  note_text: string;
  note_type: NoteType;
  section_reference?: string;
  created_at: string;
  updated_at: string;
};

const parseFormulaFromDB = (row: FormulaDB): Formula => ({
  id: row.id,
  clientId: row.client_id,
  userId: row.user_id,
  sessionNumber: row.session_number,
  serviceType: row.service_type,
  currentLevel: row.current_level,
  targetLevel: row.target_level,
  targetTone: row.target_tone,
  targetReflection: row.target_reflection,
  brand: row.brand,
  productLine: row.product_line,
  technique: row.technique,
  formulaText: row.formula_text,
  productsUsed: row.products_used || [],
  totalCost: row.total_cost,
  estimatedDurationMinutes: row.estimated_duration_minutes,
  currentColorAnalysis: row.current_color_analysis,
  desiredColorAnalysis: row.desired_color_analysis,
  safetyChecklist: row.safety_checklist,
  createdAt: new Date(row.created_at),
  updatedAt: new Date(row.updated_at),
});

const parseFormulaNotesFromDB = (row: FormulaNotesDB): FormulaNotes => ({
  id: row.id,
  formulaId: row.formula_id,
  userId: row.user_id,
  noteText: row.note_text,
  noteType: row.note_type,
  sectionReference: row.section_reference,
  createdAt: new Date(row.created_at),
  updatedAt: new Date(row.updated_at),
});

// =====================================================
// FORMULA CRUD OPERATIONS
// =====================================================

export type CreateFormulaInput = Omit<Formula, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Save a new formula to the database
 */
export async function saveFormula(formula: CreateFormulaInput): Promise<Formula> {
  const { data: userData } = await supabase.auth.getUser();
  if (!userData.user) {
    throw new Error('User not authenticated');
  }

  const payload = {
    client_id: formula.clientId,
    user_id: formula.userId,
    session_number: formula.sessionNumber,
    service_type: formula.serviceType,
    current_level: formula.currentLevel,
    target_level: formula.targetLevel,
    target_tone: formula.targetTone,
    target_reflection: formula.targetReflection,
    brand: formula.brand,
    product_line: formula.productLine,
    technique: formula.technique,
    formula_text: formula.formulaText,
    products_used: formula.productsUsed,
    total_cost: formula.totalCost,
    estimated_duration_minutes: formula.estimatedDurationMinutes,
    current_color_analysis: formula.currentColorAnalysis,
    desired_color_analysis: formula.desiredColorAnalysis,
    safety_checklist: formula.safetyChecklist,
  };

  const { data, error } = await supabase
    .from('formulas')
    .insert([payload])
    .select()
    .single();

  if (error) {
    console.error('[saveFormula] Error:', error);
    throw new Error(`Failed to save formula: ${error.message}`);
  }

  return parseFormulaFromDB(data as FormulaDB);
}

/**
 * Get all formulas for a specific client
 * Validates that the client belongs to the authenticated user
 */
export async function getClientFormulas(clientId: string): Promise<Formula[]> {
  const { data: userData } = await supabase.auth.getUser();
  if (!userData.user) {
    throw new Error('User not authenticated');
  }

  // First, verify that the client belongs to the authenticated user
  const { data: clientData, error: clientError } = await supabase
    .from('clients')
    .select('created_by')
    .eq('id', clientId)
    .single();

  if (clientError) {
    console.error('[getClientFormulas] Client validation error:', clientError);
    throw new Error('Cliente no encontrado');
  }

  if (clientData.created_by !== userData.user.id) {
    console.warn('[getClientFormulas] Authorization denied: Client does not belong to user');
    throw new Error('No tienes permiso para acceder a este cliente');
  }

  // Fetch formulas for the validated client
  const { data, error } = await supabase
    .from('formulas')
    .select('*')
    .eq('client_id', clientId)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('[getClientFormulas] Error:', error);
    throw new Error(`Failed to fetch formulas: ${error.message}`);
  }

  return (data as FormulaDB[]).map(parseFormulaFromDB);
}

/**
 * Get a single formula by ID
 */
export async function getFormula(formulaId: string): Promise<Formula | null> {
  const { data, error } = await supabase
    .from('formulas')
    .select('*')
    .eq('id', formulaId)
    .single();

  if (error) {
    if (error.code === 'PGRST116') {
      // Not found
      return null;
    }
    console.error('[getFormula] Error:', error);
    throw new Error(`Failed to fetch formula: ${error.message}`);
  }

  return parseFormulaFromDB(data as FormulaDB);
}

/**
 * Update an existing formula
 */
export async function updateFormula(
  formulaId: string,
  updates: Partial<CreateFormulaInput>
): Promise<Formula> {
  const payload: any = {};

  if (updates.sessionNumber !== undefined) payload.session_number = updates.sessionNumber;
  if (updates.serviceType !== undefined) payload.service_type = updates.serviceType;
  if (updates.currentLevel !== undefined) payload.current_level = updates.currentLevel;
  if (updates.targetLevel !== undefined) payload.target_level = updates.targetLevel;
  if (updates.targetTone !== undefined) payload.target_tone = updates.targetTone;
  if (updates.targetReflection !== undefined) payload.target_reflection = updates.targetReflection;
  if (updates.brand !== undefined) payload.brand = updates.brand;
  if (updates.productLine !== undefined) payload.product_line = updates.productLine;
  if (updates.technique !== undefined) payload.technique = updates.technique;
  if (updates.formulaText !== undefined) payload.formula_text = updates.formulaText;
  if (updates.productsUsed !== undefined) payload.products_used = updates.productsUsed;
  if (updates.totalCost !== undefined) payload.total_cost = updates.totalCost;
  if (updates.estimatedDurationMinutes !== undefined)
    payload.estimated_duration_minutes = updates.estimatedDurationMinutes;
  if (updates.currentColorAnalysis !== undefined)
    payload.current_color_analysis = updates.currentColorAnalysis;
  if (updates.desiredColorAnalysis !== undefined)
    payload.desired_color_analysis = updates.desiredColorAnalysis;
  if (updates.safetyChecklist !== undefined) payload.safety_checklist = updates.safetyChecklist;

  const { data, error } = await supabase
    .from('formulas')
    .update(payload)
    .eq('id', formulaId)
    .select()
    .single();

  if (error) {
    console.error('[updateFormula] Error:', error);
    throw new Error(`Failed to update formula: ${error.message}`);
  }

  return parseFormulaFromDB(data as FormulaDB);
}

/**
 * Delete a formula
 */
export async function deleteFormula(formulaId: string): Promise<void> {
  const { error } = await supabase.from('formulas').delete().eq('id', formulaId);

  if (error) {
    console.error('[deleteFormula] Error:', error);
    throw new Error(`Failed to delete formula: ${error.message}`);
  }
}

/**
 * Get formulas by brand (useful for analytics)
 */
export async function getFormulasByBrand(brand: string): Promise<Formula[]> {
  const { data, error } = await supabase
    .from('formulas')
    .select('*')
    .eq('brand', brand)
    .order('created_at', { ascending: false });

  if (error) {
    console.error('[getFormulasByBrand] Error:', error);
    throw new Error(`Failed to fetch formulas by brand: ${error.message}`);
  }

  return (data as FormulaDB[]).map(parseFormulaFromDB);
}

// =====================================================
// FORMULA NOTES CRUD OPERATIONS
// =====================================================

export type CreateFormulaNotesInput = Omit<FormulaNotes, 'id' | 'createdAt' | 'updatedAt'>;

/**
 * Add a note to a formula
 */
export async function addFormulaNote(note: CreateFormulaNotesInput): Promise<FormulaNotes> {
  const { data: userData } = await supabase.auth.getUser();
  if (!userData.user) {
    throw new Error('User not authenticated');
  }

  const payload = {
    formula_id: note.formulaId,
    user_id: note.userId,
    note_text: note.noteText,
    note_type: note.noteType,
    section_reference: note.sectionReference,
  };

  const { data, error } = await supabase
    .from('formula_notes')
    .insert([payload])
    .select()
    .single();

  if (error) {
    console.error('[addFormulaNote] Error:', error);
    throw new Error(`Failed to add note: ${error.message}`);
  }

  return parseFormulaNotesFromDB(data as FormulaNotesDB);
}

/**
 * Get all notes for a formula
 */
export async function getFormulaNotes(formulaId: string): Promise<FormulaNotes[]> {
  const { data, error } = await supabase
    .from('formula_notes')
    .select('*')
    .eq('formula_id', formulaId)
    .order('created_at', { ascending: true });

  if (error) {
    console.error('[getFormulaNotes] Error:', error);
    throw new Error(`Failed to fetch notes: ${error.message}`);
  }

  return (data as FormulaNotesDB[]).map(parseFormulaNotesFromDB);
}

/**
 * Update a note
 */
export async function updateFormulaNote(
  noteId: string,
  updates: Partial<Pick<FormulaNotes, 'noteText' | 'noteType' | 'sectionReference'>>
): Promise<FormulaNotes> {
  const payload: any = {};

  if (updates.noteText !== undefined) payload.note_text = updates.noteText;
  if (updates.noteType !== undefined) payload.note_type = updates.noteType;
  if (updates.sectionReference !== undefined) payload.section_reference = updates.sectionReference;

  const { data, error } = await supabase
    .from('formula_notes')
    .update(payload)
    .eq('id', noteId)
    .select()
    .single();

  if (error) {
    console.error('[updateFormulaNote] Error:', error);
    throw new Error(`Failed to update note: ${error.message}`);
  }

  return parseFormulaNotesFromDB(data as FormulaNotesDB);
}

/**
 * Delete a note
 */
export async function deleteFormulaNote(noteId: string): Promise<void> {
  const { error } = await supabase.from('formula_notes').delete().eq('id', noteId);

  if (error) {
    console.error('[deleteFormulaNote] Error:', error);
    throw new Error(`Failed to delete note: ${error.message}`);
  }
}

// =====================================================
// HELPER UTILITIES
// =====================================================

/**
 * Extract product list from formula text (simple regex parser)
 * Used to populate products_used field
 */
export function extractProductsFromText(formulaText: string): ProductUsed[] {
  const products: ProductUsed[] = [];

  // Match patterns like:
  // "Koleston Perfect 7/0 - 30g"
  // "Welloxon 6% (20vol) - 90ml"
  // "✓ Blondor Multi Blonde Powder 70g\n  Código: 81455719"

  const lines = formulaText.split('\n');
  let currentProduct: Partial<ProductUsed> | null = null;

  for (const line of lines) {
    const trimmed = line.trim();

    // Check for product line (starts with ✓ or • or -)
    const productMatch = trimmed.match(/^[✓•\-]\s+(.+?)\s+(\d+(?:g|ml|oz))/i);
    if (productMatch) {
      if (currentProduct && currentProduct.name) {
        products.push(currentProduct as ProductUsed);
      }
      currentProduct = {
        name: productMatch[1].trim(),
        amount: productMatch[2].trim(),
      };
      continue;
    }

    // Check for code line
    const codeMatch = trimmed.match(/C[óo]digo:\s*([A-Z0-9]+)/i);
    if (codeMatch && currentProduct) {
      currentProduct.code = codeMatch[1].trim();
    }
  }

  if (currentProduct && currentProduct.name) {
    products.push(currentProduct as ProductUsed);
  }

  return products;
}

/**
 * Calculate estimated cost based on products (placeholder)
 * Real implementation would query product database
 */
export function estimateCost(products: ProductUsed[]): number {
  // Placeholder: $10 per product
  return products.length * 10;
}

/**
 * Get the latest session number for a client
 */
export async function getLatestSessionNumber(clientId: string): Promise<number> {
  const { data, error } = await supabase
    .from('formulas')
    .select('session_number')
    .eq('client_id', clientId)
    .order('session_number', { ascending: false })
    .limit(1);

  if (error) {
    console.error('[getLatestSessionNumber] Error:', error);
    return 0;
  }

  if (!data || data.length === 0) {
    return 0;
  }

  return (data[0] as any).session_number;
}
