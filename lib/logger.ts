/**
 * Sistema de Logging Centralizado para Salonier AI
 *
 * SECURITY FIX #3 (2025-10-30): Sistema robusto que previene Information Disclosure (CVSS 7.4)
 * - Sanitización automática de datos sensibles (emails, UUIDs, tokens, passwords)
 * - Deshabilitado en producción (solo errors)
 * - Niveles de log configurables
 * - Compatible con React Native (__DEV__ global)
 * - Formato consistente con timestamps
 *
 * Reemplaza 66+ console.log en: ChatContext (33), AuthContext (17), ClientContext (16)
 * Reported by: Security-Reviewer agent
 * CVSS Score: 7.4 HIGH
 */

type LogLevel = 'debug' | 'info' | 'warn' | 'error';

interface LoggerConfig {
  enabled: boolean;
  minLevel: LogLevel;
  sanitize: boolean;
}

class Logger {
  private config: LoggerConfig;
  private readonly levels: Record<LogLevel, number> = {
    debug: 0,
    info: 1,
    warn: 2,
    error: 3,
  };

  constructor() {
    // React Native global con fallback a NODE_ENV para compatibilidad
    const isDev = typeof __DEV__ !== 'undefined' ? __DEV__ : process.env.NODE_ENV !== 'production';

    this.config = {
      enabled: isDev,
      minLevel: isDev ? 'debug' : 'error', // Solo errors en producción
      sanitize: !isDev, // Sanitizar en producción
    };
  }

  /**
   * Sanitiza datos sensibles antes de loggear
   *
   * @param data - Datos a sanitizar
   * @param seen - WeakSet para detectar referencias circulares
   */
  private sanitize(data: any, seen = new WeakSet<object>()): any {
    if (!this.config.sanitize) return data;

    if (typeof data === 'string') {
      // Redactar tokens, emails, UUIDs, signed URLs, costs
      return data
        .replace(/\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/g, '[EMAIL_REDACTED]')
        .replace(/\b[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}\b/gi, '[UUID_REDACTED]')
        .replace(/Bearer\s+[A-Za-z0-9\-._~+\/]+=*/g, 'Bearer [TOKEN_REDACTED]')
        .replace(/token[=:]\s*[A-Za-z0-9\-._~+\/]+=*/gi, 'token=[TOKEN_REDACTED]')
        .replace(/https:\/\/[^\s]+\.supabase\.co\/storage\/[^\s]+/g, '[SIGNED_URL]')
        .replace(/\$?\d+\.\d{2,4}/g, '[COST]');
    }

    if (typeof data === 'object' && data !== null) {
      // Detectar referencias circulares
      if (seen.has(data)) {
        return '[Circular Reference]';
      }
      seen.add(data);

      const sanitized: any = Array.isArray(data) ? [] : {};

      for (const key in data) {
        // Redactar campos sensibles
        if (/password|token|secret|key|auth/i.test(key)) {
          sanitized[key] = '[REDACTED]';
        } else if (/email/i.test(key)) {
          sanitized[key] = '[EMAIL_REDACTED]';
        } else if (/(user_)?id|userId/i.test(key)) {
          sanitized[key] = '[UUID_REDACTED]';
        } else if (/cost/i.test(key)) {
          sanitized[key] = '[COST]';
        } else {
          sanitized[key] = this.sanitize(data[key], seen);
        }
      }

      return sanitized;
    }

    return data;
  }

  /**
   * Formatea el mensaje de log con timestamp y contexto
   */
  private format(level: LogLevel, context: string, message: string, data?: any): string {
    const timestamp = new Date().toISOString();
    const levelUpper = level.toUpperCase().padEnd(5);

    let formatted = `[${timestamp}] ${levelUpper} [${context}] ${message}`;

    if (data !== undefined) {
      const sanitizedData = this.sanitize(data);
      try {
        formatted += `\n${JSON.stringify(sanitizedData, null, 2)}`;
      } catch (err) {
        // JSON.stringify puede fallar con BigInt, funciones, símbolos, o referencias circulares no detectadas
        formatted += `\n[Unable to stringify: ${err instanceof Error ? err.message : 'Unknown error'}]`;
      }
    }

    return formatted;
  }

  /**
   * Verifica si el nivel de log debe ejecutarse
   */
  private shouldLog(level: LogLevel): boolean {
    if (!this.config.enabled) return level === 'error';
    return this.levels[level] >= this.levels[this.config.minLevel];
  }

  /**
   * Log de debugging (solo desarrollo)
   */
  debug(context: string, message: string, data?: any): void {
    if (!this.shouldLog('debug')) return;
    console.debug(this.format('debug', context, message, data));
  }

  /**
   * Log informativo
   */
  info(context: string, message: string, data?: any): void {
    if (!this.shouldLog('info')) return;
    console.info(this.format('info', context, message, data));
  }

  /**
   * Log de advertencia
   */
  warn(context: string, message: string, data?: any): void {
    if (!this.shouldLog('warn')) return;
    console.warn(this.format('warn', context, message, data));
  }

  /**
   * Log de error (siempre se ejecuta, incluso en producción)
   */
  error(context: string, message: string, error?: Error | any): void {
    if (!this.shouldLog('error')) return;

    const errorData = error instanceof Error
      ? { message: error.message, stack: error.stack }
      : error;

    console.error(this.format('error', context, message, errorData));
  }

  /**
   * Grupo de logs (útil para operaciones complejas)
   */
  group(context: string, label: string, callback: () => void): void {
    if (!this.config.enabled) return;

    console.group(`[${context}] ${label}`);
    callback();
    console.groupEnd();
  }

  /**
   * Security event logs - Always logged and sent to monitoring
   * Use for: Auth failures, IDOR attempts, XSS attempts, rate limit violations
   */
  security(context: string, eventType: string, details: any): void {
    const sanitized = this.sanitize(details);
    const formatted = this.format('warn', context, `SECURITY: ${eventType}`, sanitized);
    console.warn(formatted);

    // TODO: Log to Supabase security_events table
    // await supabase.from('security_events').insert({
    //   event_type: eventType,
    //   user_id: auth.uid(),
    //   details: sanitized,
    //   timestamp: new Date(),
    // });
  }
}

// Exportar instancia singleton
export const logger = new Logger();

// Exportar tipos para TypeScript
export type { LogLevel };

/**
 * Performance logging utility
 */
export class PerformanceLogger {
  private startTime: number;
  private context: string;

  constructor(context: string, operation: string) {
    this.context = context;
    this.startTime = Date.now();

    if (__DEV__) {
      logger.debug(context, `Starting: ${operation}`);
    }
  }

  end(operation: string, metadata?: any) {
    const duration = Date.now() - this.startTime;

    if (__DEV__) {
      logger.info(this.context, `Completed: ${operation} in ${duration}ms`, metadata);
    }

    // TODO: Send to analytics if duration > threshold
    // if (duration > 1000) {
    //   analytics.track('slow_operation', {
    //     context: this.context,
    //     operation,
    //     duration,
    //   });
    // }
  }
}

/**
 * Usage examples:
 *
 * // Basic logging
 * logger.debug('ChatContext', 'Loading conversations', { count: 10 });
 * logger.info('Step1', 'Images processed', { count: 6 });
 * logger.warn('AIClient', 'Slow response', { latency: 3500 });
 * logger.error('AuthContext', 'Sign in failed', error);
 *
 * // Security logging
 * logger.security('ClientContext', 'idor_attempt', {
 *   attemptedClientId: 'abc-123',
 *   userId: user.id,
 * });
 *
 * // Performance tracking
 * const perf = new PerformanceLogger('ChatContext', 'loadConversations');
 * // ... do work
 * perf.end('loadConversations', { conversationCount: 20 });
 */

export default logger;
