import { Alert } from 'react-native';

/**
 * Vision Safety Error Utilities
 *
 * Shared utilities for handling VisionSafetyError rejections from OpenAI Vision API.
 * Used across step1, step2, and chat screens for consistency.
 */

/**
 * Shows a standardized Alert when OpenAI Vision API rejects images due to safety filters.
 *
 * @param onChangePhotos - Callback when user taps "Cambiar fotos"
 * @param onRetry - Callback when user taps "Reintentar"
 * @param showGuidance - Optional callback to show PhotoGuidance (only in chat)
 *
 * @example
 * // In step1/step2:
 * showVisionSafetyError(
 *   () => setImages([]),
 *   () => analyzeImages()
 * );
 *
 * @example
 * // In chat:
 * showVisionSafetyError(
 *   () => {
 *     setSelectedImages([]);
 *     setShowPhotoGuidance(true);
 *   },
 *   () => sendMessage()
 * );
 */
export const showVisionSafetyError = (
  onChangePhotos: () => void,
  onRetry: () => void,
  options?: {
    showGuidanceTip?: boolean;
  }
) => {
  const message = options?.showGuidanceTip
    ? 'El sistema de visión AI ha bloqueado estas imágenes para proteger la privacidad.\n\n' +
      '✅ Recomendaciones:\n' +
      '• Toma fotos enfocando SOLO el cabello\n' +
      '• Evita incluir rostros completos o primeros planos de caras\n' +
      '• Usa diferentes ángulos: raíces, medios, puntas\n' +
      '• Asegura buena iluminación natural\n\n' +
      '💡 Tip: Mantén presionado el botón de cámara para ver más consejos.'
    : 'El sistema de visión AI ha bloqueado estas imágenes para proteger la privacidad.\n\n' +
      '✅ Recomendaciones:\n' +
      '• Toma fotos enfocando SOLO el cabello\n' +
      '• Evita incluir rostros completos o primeros planos de caras\n' +
      '• Usa diferentes ángulos: raíces, medios, puntas\n' +
      '• Asegura buena iluminación natural\n\n' +
      '💡 Tip: Puedes fotografiar el cabello desde atrás o desde los costados sin mostrar la cara.';

  Alert.alert(
    '⚠️ Análisis bloqueado por seguridad',
    message,
    [
      {
        text: 'Cambiar fotos',
        onPress: onChangePhotos,
      },
      {
        text: 'Reintentar',
        onPress: onRetry,
        style: 'default',
      },
      {
        text: 'Entendido',
        style: 'cancel',
      },
    ]
  );
};

/**
 * Checks if an error is a VisionSafetyError from OpenAI Vision API.
 *
 * @param error - Error object to check
 * @returns true if error is a VisionSafetyError
 *
 * @example
 * try {
 *   await analyzeImages();
 * } catch (error: any) {
 *   if (isVisionSafetyError(error)) {
 *     showVisionSafetyError(...);
 *   }
 * }
 */
export const isVisionSafetyError = (error: any): boolean => {
  return (
    error?.name === 'VisionSafetyError' ||
    error?.message?.includes('restricciones de seguridad') ||
    error?.message?.includes('vision_safety_rejection')
  );
};
