/**
 * Security utilities for sanitizing user input and AI responses
 * Prevents XSS attacks in React Native Text components and IDOR vulnerabilities
 */

import type { Client } from '@/types';

// Security Configuration Constants
const MAX_DISPLAY_TEXT_LENGTH = 10000; // Max characters for display text
const MAX_AI_PROMPT_LENGTH = 5000; // Max characters for AI prompt input
const MAX_URL_LENGTH = 2048; // Max URL length (RFC 7230)
const UUID_PATTERN = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;

/**
 * Sanitize text content for display in React Native
 * Removes potential XSS vectors while preserving readability
 *
 * @param input - Raw text that may contain malicious content
 * @returns Sanitized text safe for display
 */
export function sanitizeTextForDisplay(input: string | undefined | null): string {
  if (!input) return '';

  let sanitized = String(input);

  // 1. Remove control characters (including null bytes), but preserve newlines
  sanitized = sanitized.replace(/[\u0000-\u0009\u000B-\u001F\u007F-\u009F]/g, '');

  // 2. Remove potential script injection patterns
  // React Native doesn't execute <script>, but we sanitize anyway for consistency
  sanitized = sanitized
    .replace(/<script[^>]*>.*?<\/script>/gi, '')
    .replace(/javascript:/gi, '')
    .replace(/on\w+\s*=/gi, ''); // Remove event handlers (onclick, onerror, etc.)

  // 3. Remove data URIs (can contain encoded scripts)
  sanitized = sanitized.replace(/data:text\/html[^,]*,/gi, '');

  // 4. Limit length to prevent DoS via extremely long strings
  if (sanitized.length > MAX_DISPLAY_TEXT_LENGTH) {
    sanitized = sanitized.substring(0, MAX_DISPLAY_TEXT_LENGTH) + '...';
  }

  // 5. Normalize whitespace (preserve newlines for readability)
  sanitized = sanitized
    .replace(/\r\n/g, '\n') // Normalize line endings
    .replace(/\t/g, '  ') // Convert tabs to spaces
    .trim();

  return sanitized;
}

/**
 * Sanitize hex color code
 * Ensures only valid hex colors are used (prevents injection via color fields)
 *
 * @param color - Hex color code (e.g., "#FF5733")
 * @returns Valid hex color or default gray
 */
export function sanitizeHexColor(color: string | undefined | null): string {
  if (!color) return '#808080'; // Default gray

  // Remove whitespace
  const cleaned = String(color).trim();

  // Validate hex format (#RGB or #RRGGBB)
  const hexPattern = /^#([0-9A-Fa-f]{3}|[0-9A-Fa-f]{6})$/;

  if (hexPattern.test(cleaned)) {
    return cleaned;
  }

  // If invalid, return safe default
  return '#808080';
}

/**
 * Sanitize numeric value for display
 * Ensures numbers are within expected range and formatted safely
 *
 * @param value - Numeric value
 * @param min - Minimum allowed value
 * @param max - Maximum allowed value
 * @param decimals - Number of decimal places (default: 0)
 * @returns Formatted string or empty string if invalid
 */
export function sanitizeNumericDisplay(
  value: number | string | undefined | null,
  min: number,
  max: number,
  decimals: number = 0
): string {
  if (value === undefined || value === null) return '';

  const num = typeof value === 'string' ? parseFloat(value) : value;

  if (isNaN(num)) return '';

  // Clamp to range
  const clamped = Math.max(min, Math.min(max, num));

  // Format with specified decimals
  return clamped.toFixed(decimals);
}

/**
 * Sanitize URL to prevent javascript: and data: URIs
 * Only allows http:, https:, and relative paths
 *
 * @param url - URL to sanitize
 * @returns Safe URL or empty string
 */
export function sanitizeUrl(url: string | undefined | null): string {
  if (!url) return '';

  const cleaned = String(url).trim().toLowerCase();

  // Allow only safe protocols
  if (
    cleaned.startsWith('http://') ||
    cleaned.startsWith('https://') ||
    cleaned.startsWith('/') ||
    cleaned.startsWith('./')
  ) {
    return url.trim();
  }

  // Block dangerous protocols
  if (
    cleaned.startsWith('javascript:') ||
    cleaned.startsWith('data:') ||
    cleaned.startsWith('vbscript:') ||
    cleaned.startsWith('file:')
  ) {
    return '';
  }

  // If no protocol, treat as relative path
  if (!cleaned.includes(':')) {
    return url.trim();
  }

  // Unknown protocol - block
  return '';
}

/**
 * Sanitize user-provided prompt input before sending to AI
 * More aggressive than display sanitization to prevent prompt injection
 *
 * @param input - User input for AI prompt
 * @returns Sanitized input safe for AI prompts
 */
export function sanitizeAIPromptInput(input: string | undefined | null): string {
  if (!input) return '';

  let sanitized = String(input);

  // 1. Remove control characters, but preserve newlines
  sanitized = sanitized.replace(/[\u0000-\u0009\u000B-\u001F\u007F-\u009F]/g, '');

  // 2. Remove prompt escape sequences
  sanitized = sanitized
    .replace(/```[^`]*```/g, '') // Remove code blocks
    .replace(/---+/g, '') // Remove separators
    .replace(/={3,}/g, ''); // Remove header separators

  // 3. Remove role markers and control tokens
  sanitized = sanitized
    .replace(/\[(SYSTEM|ASSISTANT|USER|AI|HUMAN|INSTRUCTION|PROMPT|ROLE)\]/gi, '')
    .replace(/(SYSTEM|ASSISTANT|USER|AI|HUMAN):/gi, '')
    .replace(/<<[^>]*>>/g, '')
    .replace(/<\|[^|]*\|>/g, '');

  // 4. Escape special characters
  sanitized = sanitized
    .replace(/\\/g, '\\\\')
    .replace(/"/g, '\\"')
    .replace(/'/g, "\\'");

  // 5. Limit length
  if (sanitized.length > MAX_AI_PROMPT_LENGTH) {
    sanitized = sanitized.substring(0, MAX_AI_PROMPT_LENGTH) + '...';
  }

  return sanitized.trim();
}

/**
 * Validate client ID from route parameters (IDOR protection)
 * Ensures user can only access their own clients
 *
 * @param clientId - Client ID from route params
 * @param userClients - List of clients belonging to current user
 * @returns Validated client or null if unauthorized
 */
export function validateClientAccess(
  clientId: string | undefined | null,
  userClients: Client[]
): Client | null {
  if (!clientId) {
    console.warn('[SECURITY] validateClientAccess: clientId is null/undefined');
    return null;
  }

  // SECURITY: Validate clientId format (UUID)
  if (!UUID_PATTERN.test(clientId)) {
    console.error('[SECURITY] validateClientAccess: Invalid clientId format:', clientId);
    return null;
  }

  // SECURITY: Verify client exists in user's list (prevent IDOR)
  // UUIDs are case-insensitive per RFC 4122
  const client = userClients.find(c => c.id.toLowerCase() === clientId.toLowerCase());
  if (!client) {
    console.error('[SECURITY] validateClientAccess: Client not found in user list:', clientId);
    return null;
  }

  return client;
}
