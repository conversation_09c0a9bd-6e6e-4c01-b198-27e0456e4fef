/**
 * Analytics tracker para medir comportamiento de usuarios
 * Usado para medir impacto de features y optimizar UX
 */

import { supabase } from './supabase';

export interface AnalyticsEvent {
  eventName: string;
  properties?: Record<string, any>;
}

/**
 * Registra un evento de analytics en Supabase
 *
 * @param eventName - Nombre del evento (ej: 'chat_message_sent')
 * @param properties - Metadata adicional (opcional)
 *
 * @example
 * await trackEvent('chat_message_sent', {
 *   has_images: true,
 *   conversation_id: '123',
 *   message_length: 150
 * });
 */
export async function trackEvent(
  eventName: string,
  properties?: Record<string, any>
): Promise<void> {
  try {
    // Verificar autenticación
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      // No trackear si no hay usuario (no fallar silenciosamente para no bloquear UX)
      console.warn('[Analytics] No authenticated user, skipping event:', eventName);
      return;
    }

    // Insertar evento
    const { error } = await supabase
      .from('analytics_events')
      .insert({
        event_name: eventName,
        properties: properties || null,
        user_id: user.id,
        timestamp: new Date().toISOString(),
      });

    if (error) {
      console.error('[Analytics] Error tracking event:', error);
      // No lanzar error para no afectar UX del usuario
      return;
    }

    console.log(`[Analytics] Event tracked: ${eventName}`, properties || {});
  } catch (error) {
    console.error('[Analytics] Unexpected error tracking event:', error);
    // No lanzar error para no afectar UX del usuario
  }
}

/**
 * Eventos pre-definidos para consistencia
 */
export const AnalyticsEvents = {
  // Chat events
  CHAT_MESSAGE_SENT: 'chat_message_sent',
  CHAT_CONVERSATION_STARTED: 'chat_conversation_started',
  CHAT_CONVERSATION_DELETED: 'chat_conversation_deleted',
  CHAT_IMAGE_UPLOADED: 'chat_image_uploaded',

  // Formula events
  FORMULA_STARTED: 'formula_started',
  FORMULA_COMPLETED: 'formula_completed',
  FORMULA_STEP_COMPLETED: 'formula_step_completed',

  // Client events
  CLIENT_CREATED: 'client_created',
  CLIENT_UPDATED: 'client_updated',
  CLIENT_DELETED: 'client_deleted',

  // Feature usage
  FEATURE_USED: 'feature_used',
} as const;

/**
 * Helper para trackear mensajes de chat con metadata relevante
 */
export async function trackChatMessage(data: {
  conversationId: string;
  hasImages: boolean;
  imageCount?: number;
  messageLength: number;
  isFirstMessage?: boolean;
}): Promise<void> {
  await trackEvent(AnalyticsEvents.CHAT_MESSAGE_SENT, {
    conversation_id: data.conversationId,
    has_images: data.hasImages,
    image_count: data.imageCount || 0,
    message_length: data.messageLength,
    is_first_message: data.isFirstMessage || false,
  });
}

/**
 * Helper para trackear inicio de conversaciones
 */
export async function trackConversationStarted(conversationId: string): Promise<void> {
  await trackEvent(AnalyticsEvents.CHAT_CONVERSATION_STARTED, {
    conversation_id: conversationId,
  });
}
