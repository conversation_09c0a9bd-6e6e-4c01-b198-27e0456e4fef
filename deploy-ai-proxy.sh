#!/bin/bash

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# Deploy AI Proxy Edge Function to Supabase
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

set -e  # Exit on error

PROJECT_REF="guyxczavhtemwlrknqpm"
FUNCTION_NAME="ai-proxy"

echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "🚀 Deploying AI Proxy Edge Function to Supabase"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 1: Pre-deployment Checks
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "📋 Step 1: Pre-deployment checks..."

# Check if brands.json exists
if [ ! -f "supabase/functions/ai-proxy/data/brands.json" ]; then
    echo "❌ ERROR: brands.json not found in supabase/functions/ai-proxy/data/"
    echo "   Copying from assets/data/brands.json..."
    mkdir -p supabase/functions/ai-proxy/data
    cp assets/data/brands.json supabase/functions/ai-proxy/data/brands.json
    echo "   ✅ brands.json copied successfully"
fi

# Verify brands.json size
BRANDS_SIZE=$(wc -l < supabase/functions/ai-proxy/data/brands.json)
echo "   ✅ brands.json verified: $BRANDS_SIZE lines"

# Check if all required files exist
REQUIRED_FILES=(
    "supabase/functions/ai-proxy/index.ts"
    "supabase/functions/ai-proxy/intelligent-router.ts"
    "supabase/functions/ai-proxy/hybrid-executor.ts"
    "supabase/functions/ai-proxy/self-correction.ts"
    "supabase/functions/ai-proxy/product-extractor.ts"
    "supabase/functions/ai-proxy/intent-detector.ts"
    "supabase/functions/ai-proxy/brand-validator.ts"
    "supabase/functions/ai-proxy/cache-manager.ts"
    "supabase/functions/ai-proxy/types.ts"
    "supabase/functions/ai-proxy/prompts.ts"
)

echo "   Checking required files..."
for file in "${REQUIRED_FILES[@]}"; do
    if [ ! -f "$file" ]; then
        echo "   ❌ ERROR: Missing required file: $file"
        exit 1
    fi
done
echo "   ✅ All required files present"

echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 2: Check Supabase Authentication
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "🔐 Step 2: Checking Supabase authentication..."

# Try to list functions (this will fail if not authenticated)
if ! supabase functions list --project-ref $PROJECT_REF &>/dev/null; then
    echo "❌ ERROR: Not authenticated with Supabase CLI"
    echo ""
    echo "Please authenticate using one of these methods:"
    echo ""
    echo "  Option A - Browser login (recommended):"
    echo "    supabase login"
    echo ""
    echo "  Option B - Access token:"
    echo "    supabase login --token YOUR_ACCESS_TOKEN"
    echo "    (Get token from: https://supabase.com/dashboard/account/tokens)"
    echo ""
    exit 1
fi

echo "   ✅ Authenticated with Supabase CLI"
echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 3: Deploy Edge Function
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "🚀 Step 3: Deploying edge function..."

# Deploy with verbose output
supabase functions deploy $FUNCTION_NAME --project-ref $PROJECT_REF

if [ $? -eq 0 ]; then
    echo ""
    echo "   ✅ Edge function deployed successfully!"
else
    echo ""
    echo "   ❌ Deployment failed. Check the error messages above."
    exit 1
fi

echo ""

# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
# STEP 4: Verify Deployment
# ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━

echo "✅ Step 4: Verifying deployment..."

# List functions to verify
echo ""
echo "Current edge functions:"
supabase functions list --project-ref $PROJECT_REF

echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo "✅ DEPLOYMENT COMPLETE!"
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
echo ""
echo "📝 Next steps:"
echo ""
echo "1. Monitor logs (in a separate terminal):"
echo "   supabase functions logs $FUNCTION_NAME --project-ref $PROJECT_REF --tail"
echo ""
echo "2. Test the deployment:"
echo "   - Open the app: bun run start-web"
echo "   - Create a formula with a brand (e.g., L'Oréal INOA)"
echo "   - Watch the logs for [Hybrid Executor] messages"
echo ""
echo "3. Check cache stats after 10-20 formulas:"
echo "   - Run SQL query in Supabase Dashboard to check intent_cache and ai_cache"
echo ""
echo "━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━"
