import { validateClientAccess } from '@/lib/sanitize';
import { mockClients } from '@/__mocks__/fixtures/clients';
import type { Client } from '@/types';

describe('IDOR Prevention - Client Access Validation', () => {
  const userAClients: Client[] = [mockClients[0]]; // <PERSON> (client-1)
  const userBClients: Client[] = [mockClients[1]]; // <PERSON> (client-2)

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('validateClientAccess', () => {
    it('CRITICAL: should prevent User A from accessing User B client', () => {
      // User A tries to access User B's client ID
      const result = validateClientAccess('550e8400-e29b-41d4-a716-446655440002', userAClients);

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY] validateClientAccess: Client not found in user list'),
        expect.anything()
      );
    });

    it('CRITICAL: should allow User A to access their own client', () => {
      const result = validateClientAccess('550e8400-e29b-41d4-a716-************', userAClients);

      expect(result).not.toBeNull();
      expect(result?.id).toBe('550e8400-e29b-41d4-a716-************');
      expect(result?.name).toBe('María García');
    });

    it('CRITICAL: should reject invalid UUID format', () => {
      const invalidIds = [
        'invalid-id',
        '12345',
        'client-sql-injection',
        '../../../etc/passwd',
        '<script>alert(1)</script>',
      ];

      invalidIds.forEach(invalidId => {
        const result = validateClientAccess(invalidId, userAClients);
        expect(result).toBeNull();
      });

      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY] validateClientAccess: Invalid clientId format'),
        expect.anything()
      );
    });

    it('CRITICAL: should reject null clientId', () => {
      const result = validateClientAccess(null, userAClients);

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY] validateClientAccess: clientId is null/undefined')
      );
    });

    it('CRITICAL: should reject undefined clientId', () => {
      const result = validateClientAccess(undefined, userAClients);

      expect(result).toBeNull();
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY] validateClientAccess: clientId is null/undefined')
      );
    });

    it('should validate valid UUID format (lowercase)', () => {
      const validUUID = '550e8400-e29b-41d4-a716-************';
      const clients: Client[] = [
        {
          id: validUUID,
          name: 'Test Client',
          isPregnantOrBreastfeeding: false,
          hasSensitiveScalp: false,
          chemicalTreatments: { henna: false, chemicalStraightening: false, keratin: false },
          communicationPreferences: { acceptsReminders: true, preferredMethod: 'whatsapp' },
        },
      ];

      const result = validateClientAccess(validUUID, clients);

      expect(result).not.toBeNull();
      expect(result?.id).toBe(validUUID);
    });

    it('should validate valid UUID format (uppercase)', () => {
      const validUUID = '550E8400-E29B-41D4-A716-************';
      const clients: Client[] = [
        {
          id: validUUID.toLowerCase(),
          name: 'Test Client',
          isPregnantOrBreastfeeding: false,
          hasSensitiveScalp: false,
          chemicalTreatments: { henna: false, chemicalStraightening: false, keratin: false },
          communicationPreferences: { acceptsReminders: true, preferredMethod: 'whatsapp' },
        },
      ];

      const result = validateClientAccess(validUUID, clients);

      expect(result).not.toBeNull();
    });

    it('CRITICAL: should handle empty client list', () => {
      const result = validateClientAccess('550e8400-e29b-41d4-a716-************', []);

      expect(result).toBeNull();
      expect(console.error).toHaveBeenCalledWith(
        expect.stringContaining('[SECURITY] validateClientAccess: Client not found in user list'),
        expect.anything()
      );
    });

    it('CRITICAL: should be case-sensitive for client ID matching', () => {
      // UUIDs should match case-insensitively (per UUID spec)
      const mixedCaseUUID = '550e8400-E29B-41d4-A716-************';
      const clients: Client[] = [
        {
          id: '550e8400-e29b-41d4-a716-************', // lowercase
          name: 'Test Client',
          isPregnantOrBreastfeeding: false,
          hasSensitiveScalp: false,
          chemicalTreatments: { henna: false, chemicalStraightening: false, keratin: false },
          communicationPreferences: { acceptsReminders: true, preferredMethod: 'whatsapp' },
        },
      ];

      const result = validateClientAccess(mixedCaseUUID, clients);

      // UUID comparison should be case-insensitive
      expect(result).not.toBeNull();
    });
  });

  describe('Real-world IDOR attack scenarios', () => {
    it('CRITICAL: should prevent sequential ID enumeration attack', () => {
      // Attacker tries sequential IDs
      const sequentialAttempts = [
        'client-1',
        'client-2',
        'client-3',
        'client-100',
      ];

      sequentialAttempts.forEach(clientId => {
        const result = validateClientAccess(clientId, userAClients);
        if (clientId !== 'client-1') {
          expect(result).toBeNull();
        }
      });
    });

    it('CRITICAL: should prevent predictable UUID guessing', () => {
      // Even with valid UUID format, must be in user's list
      const guessedUUIDs = [
        '00000000-0000-0000-0000-000000000001',
        '00000000-0000-0000-0000-000000000002',
        'ffffffff-ffff-ffff-ffff-ffffffffffff',
      ];

      guessedUUIDs.forEach(uuid => {
        const result = validateClientAccess(uuid, userAClients);
        expect(result).toBeNull();
      });
    });

    it('CRITICAL: should prevent SQL injection via clientId', () => {
      const sqlInjectionAttempts = [
        "' OR '1'='1",
        "'; DROP TABLE clients; --",
        "1' UNION SELECT * FROM users--",
      ];

      sqlInjectionAttempts.forEach(injection => {
        const result = validateClientAccess(injection, userAClients);
        expect(result).toBeNull();
      });
    });

    it('CRITICAL: should prevent path traversal attacks', () => {
      const pathTraversalAttempts = [
        '../../../etc/passwd',
        '..\\..\\..\\windows\\system32',
        './../../sensitive-data',
      ];

      pathTraversalAttempts.forEach(attempt => {
        const result = validateClientAccess(attempt, userAClients);
        expect(result).toBeNull();
      });
    });
  });
});
