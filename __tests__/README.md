# Salonier AI Test Suite

This directory contains all automated tests for the Salonier AI application.

## Directory Structure

```
__tests__/
├── security/              # CRITICAL: Security vulnerability tests
│   ├── xss-prevention.test.ts       # XSS attack prevention (24 tests)
│   └── idor-prevention.test.ts      # IDOR attack prevention (13 tests)
├── unit/                  # Unit tests for individual modules
│   ├── lib/
│   │   └── sanitize.test.ts         # Sanitization utilities (15 tests)
│   └── contexts/          # TODO: Context hook tests
│       ├── AuthContext.test.tsx     # TODO: Authentication tests
│       ├── ClientContext.test.tsx   # TODO: Client management tests
│       ├── ChatContext.test.tsx     # TODO: Chat/conversation tests
│       └── FormulaContext.test.tsx  # TODO: Formula workflow tests
├── integration/           # TODO: Multi-component integration tests
│   ├── formula-workflow.test.tsx    # TODO: Step0 → Step5 flow
│   ├── chat-with-images.test.tsx    # TODO: Image upload + AI analysis
│   └── client-crud.test.tsx         # TODO: CRUD with cascade
└── e2e/                   # TODO: End-to-end tests (future)
    └── complete-formula-flow.test.tsx

README.md                  # This file
```

## Running Tests

### Quick Start
```bash
# Run all tests
bun run test

# Run security tests only (CRITICAL)
bun run test:security

# Run with coverage
bun run test:coverage
```

### Watch Mode (Development)
```bash
bun run test:watch
```

### Specific Test Suites
```bash
# Unit tests only
bun run test:unit

# Integration tests only
bun run test:integration

# Specific file
bun run test __tests__/security/xss-prevention.test.ts
```

## Test Categories

### Security Tests (CRITICAL)
**Priority**: P0 - Must pass before every commit
**Coverage**: XSS, IDOR, SQL injection, path traversal

**Files**:
- `security/xss-prevention.test.ts` - 24 tests
- `security/idor-prevention.test.ts` - 13 tests

**Run**: `bun run test:security`

### Unit Tests
**Priority**: P1 - Should pass before PR
**Coverage**: Utils, contexts, pure functions

**Files**:
- `unit/lib/sanitize.test.ts` - 15 tests
- `unit/contexts/*.test.tsx` - TODO

**Run**: `bun run test:unit`

### Integration Tests
**Priority**: P2 - Should pass before release
**Coverage**: Multi-step workflows, user flows

**Files**: TODO

**Run**: `bun run test:integration`

### E2E Tests
**Priority**: P3 - Optional for release
**Coverage**: Complete user journeys

**Files**: TODO

**Run**: `bun run test:e2e` (not implemented yet)

## Writing Tests

### Test File Naming
- Unit tests: `ComponentName.test.tsx` or `functionName.test.ts`
- Integration tests: `feature-name.test.tsx`
- Security tests: `vulnerability-type.test.ts`

### Test Structure
```typescript
import { functionToTest } from '@/path/to/module';

describe('ModuleName', () => {
  describe('functionToTest', () => {
    it('should do expected behavior', () => {
      // Arrange
      const input = 'test';

      // Act
      const result = functionToTest(input);

      // Assert
      expect(result).toBe('expected');
    });

    it('CRITICAL: should handle security concern', () => {
      // Test security-critical behavior
    });
  });
});
```

### Best Practices
1. **Use descriptive test names**: `it('should validate UUID format')`
2. **Mark critical tests**: Prefix with `CRITICAL:` in test name
3. **Arrange-Act-Assert**: Structure tests clearly
4. **One assertion per test**: (when possible)
5. **Clean up**: Use `beforeEach()` to reset mocks

### Example: Security Test
```typescript
it('CRITICAL: should prevent User A from accessing User B client', () => {
  const result = validateClientAccess('client-b', userAClients);

  expect(result).toBeNull();
  expect(console.error).toHaveBeenCalledWith(
    expect.stringContaining('[SECURITY]')
  );
});
```

### Example: Context Test
```typescript
it('should optimistically add message before database save', async () => {
  const { result } = renderHook(() => useChat());

  await act(async () => {
    await result.current.addMessage('conv-1', newMessage);
  });

  // Message should be visible IMMEDIATELY
  expect(result.current.currentConversation?.messages).toContainEqual(
    expect.objectContaining({ id: newMessage.id })
  );
});
```

## Mocking

### Available Mocks
All mocks are in `__mocks__/` directory:

1. **Supabase**: `@supabase/supabase-js`
2. **AI Client**: `lib/ai-client`
3. **Storage**: `lib/storage`
4. **Test Fixtures**: `fixtures/clients.ts`, `fixtures/conversations.ts`

### Using Mocks
```typescript
import { mockSupabaseClient } from '@/__mocks__/@supabase/supabase-js';
import { mockClients } from '@/__mocks__/fixtures/clients';

// Setup mock response
mockSupabaseClient.from.mockReturnValue({
  select: jest.fn().mockResolvedValue({
    data: mockClients,
    error: null,
  }),
});

// Test code here
```

## Coverage

### Current Coverage
Run `bun run test:coverage` to see current coverage.

### Coverage Thresholds
- **Global**: 70% statements, 65% branches
- **Contexts**: 90% statements, 85% branches
- **lib/sanitize.ts**: 95% statements, 90% branches

### Viewing Coverage Report
```bash
bun run test:coverage
open coverage/lcov-report/index.html
```

## CI/CD

Tests run automatically on:
- Every push to `main`, `develop`, `test/**`
- Every pull request to `main`, `develop`

**Security tests must pass** before merge is allowed.

See `.github/workflows/test.yml` for details.

## Debugging Tests

### Run in Debug Mode
```bash
bun run test:debug
```

Then open Chrome DevTools (`chrome://inspect`) to debug.

### Verbose Output
```bash
bun run test -- --verbose
```

### Run Specific Test
```bash
bun run test -t "should validate UUID format"
```

## Common Issues

### Mock not working
**Solution**: Clear mocks in `beforeEach`:
```typescript
beforeEach(() => {
  jest.clearAllMocks();
});
```

### Test timeout
**Solution**: Increase timeout:
```typescript
it('slow test', async () => {
  // ...
}, 30000); // 30 seconds
```

### Module not found
**Solution**: Check path alias in `jest.config.js`. Restart Jest if recently installed.

## Test Priorities

| Priority | Category | Tests | Status |
|----------|----------|-------|--------|
| P0 | Security | 37 | ✅ DONE |
| P1 | Contexts | 80 | 🔨 TODO |
| P2 | Integration | 20 | 🔨 TODO |
| P3 | E2E | 10 | 🔨 TODO |

## Quick Commands Reference

```bash
# Essential commands
bun run test                    # Run all tests
bun run test:watch              # Watch mode
bun run test:security           # Security tests (CRITICAL)
bun run test:coverage           # With coverage

# Development
bun run test:debug              # Debug mode
bun run test -- --verbose       # Verbose output
bun run test -t "test name"     # Run specific test

# CI/CD
bun run test:ci                 # CI mode
```

## Resources

- **Full Strategy**: See `../TEST_STRATEGY.md`
- **Quick Start**: See `../TESTING_QUICKSTART.md`
- **Summary**: See `../TEST_IMPLEMENTATION_SUMMARY.md`
- **Jest Docs**: https://jestjs.io/
- **React Native Testing Library**: https://callstack.github.io/react-native-testing-library/

## Contributing

1. Write tests for new features
2. Run `bun run test:security` before commit
3. Ensure coverage thresholds are met
4. Update test documentation if needed

---

**Remember**: Security tests are CRITICAL. Always run `bun run test:security` before committing.
