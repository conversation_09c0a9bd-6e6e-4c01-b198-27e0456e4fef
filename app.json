{"expo": {"name": "Salonier AI", "slug": "coloraimaster", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.coloraimaster", "infoPlist": {"NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos", "NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.coloraimaster", "permissions": ["CAMERA", "READ_EXTERNAL_STORAGE", "WRITE_EXTERNAL_STORAGE", "INTERNET", "RECORD_AUDIO"]}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["expo-camera", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera", "microphonePermission": "Allow $(PRODUCT_NAME) to access your microphone", "recordAudioAndroid": true}]], "experiments": {"typedRoutes": true}}}