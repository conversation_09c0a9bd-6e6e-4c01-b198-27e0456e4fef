# Propuesta: Orquestación Inteligente de IA sin Base de Datos de Productos

**Fecha**: 2025-10-28

**Rama**: `feature/enhance-ai-intelligence`

**Objetivo**: Maximizar OpenAI + Perplexity para chat inteligente y formulación con productos exactos

---

## 🎯 Visión del Proyecto

**Queremos**:
- Chat que se sienta como ChatGPT/Claude con acceso a internet
- Formulación con nombres EXACTOS de productos (verificados por IA, no BD)
- Proporciones de mezcla confiables (fuentes oficiales)
- Aprovechar al máximo el poder de IA (OpenAI + Perplexity)

**NO queremos**:
- Base de datos de productos (40+ marcas = mantenimiento complejo)
- Sistema que requiera actualizaciones manuales de catálogos

---

## 📊 Estado Actual: Análisis

### Arquitectura Actual

```
Cliente (React Native)
↓
lib/ai-client.ts (generateTextSafe)
↓
Supabase Edge Function (ai-proxy)
↓
├─ useCase === 'chat' → OpenAI gpt-4o-mini
├─ useCase === 'vision_analysis' → OpenAI gpt-4o
├─ useCase === 'formula_generation' → OpenAI gpt-4o
└─ useCase === 'product_search' → Perplexity sonar-pro
```

### Problemas Identificados

#### 1. **Routing Estático y Limitado**

```typescript
// ❌ ACTUAL: Routing manual en cliente (chat.tsx línea 223-226)
const productKeywords = /\b(marca|producto|línea...)\b/i;
const isProductQuery = !hasImages && productKeywords.test(currentInput);

if (isProductQuery) {
  // Usar Perplexity
} else {
  // Usar OpenAI
}
```

**Problemas**:
- Detección vía regex básico (solo keywords explícitas)
- No detecta preguntas indirectas: "¿Qué recomiendas para canas en 2025?"
- Usuario no siente que tiene acceso a internet real

#### 2. **Productos Sin Verificación**

```typescript
// ❌ ACTUAL: Solo instrucciones de texto en prompts
// lib/formula-prompts.ts línea 243
"REGLA: Solo productos que existan en catálogo"
```

**Problemas**:
- GPT-4o puede alucinar productos que no existen
- Sin verificación post-generación
- 40+ marcas pero conocimiento de GPT-4o cortado en enero 2025

#### 3. **Mixing Ratios No Confiables**

```typescript
// ❌ ACTUAL: Solo 1 verificación al generar fórmula (step5.tsx línea 178)
const verification = await verifyMixingRatio(brand, productLine);
// Caché puede quedar obsoleto, no se re-valida
```

---

## 🚀 Propuesta: Sistema de Orquestación Inteligente

### Arquitectura Propuesta

```
┌──────────────────────────────────────────────────────┐
│ Cliente (sin cambios en lógica de routing)          │
│ - chat.tsx, step5.tsx usan generateTextSafe()       │
└────────────┬─────────────────────────────────────────┘
             │
             ↓
┌──────────────────────────────────────────────────────┐
│ Edge Function: ai-proxy (MEJORADO)                  │
│                                                      │
│ ┌─────────────────────────────────────────────────┐ │
│ │ 1. INTELLIGENT ROUTER                           │ │
│ │    ↓                                            │ │
│ │ Analiza intención con GPT-4o-mini (barato)     │ │
│ │    ↓                                            │ │
│ │ Decide: OpenAI | Perplexity | Hybrid           │ │
│ └─────────────────────────────────────────────────┘ │
│                                                      │
│ ┌─────────────────────────────────────────────────┐ │
│ │ 2. HYBRID EXECUTOR (si routing = 'hybrid')     │ │
│ │                                                 │ │
│ │    a) OpenAI genera fórmula (streaming)        │ │
│ │    b) Extrae productos con regex + NLP         │ │
│ │    c) Perplexity verifica CADA producto        │ │
│ │    d) Self-correction si hay errores           │ │
│ └─────────────────────────────────────────────────┘ │
│                                                      │
│ ┌─────────────────────────────────────────────────┐ │
│ │ 3. CACHE MANAGER (3 niveles)                   │ │
│ │                                                 │ │
│ │    - Product Catalog: 90 días                  │ │
│ │    - Mixing Ratios: 180 días                   │ │
│ │    - Formulas: 30 días                         │ │
│ └─────────────────────────────────────────────────┘ │
└──────────────────────────────────────────────────────┘
```

---

## 🔧 Componentes Clave

### 1. Intelligent Router

**Objetivo**: Decidir automáticamente OpenAI vs Perplexity vs Hybrid

**Implementación**: `supabase/functions/ai-proxy/intelligent-router.ts`

```typescript
export async function routeRequest(request: {
  useCase: string;
  prompt: string;
  brand?: string;
  hasImages: boolean;
}): Promise<RoutingDecision> {

  // REGLA 1: Imágenes → OpenAI (único con vision)
  if (hasImages) {
    return { provider: 'openai', reason: 'Vision analysis' };
  }

  // REGLA 2: Detectar si necesita info actualizada
  const needsCurrentInfo = await detectCurrentInfoIntent(prompt);

  if (needsCurrentInfo.detected) {
    return {
      provider: 'perplexity',
      reason: 'Query requires current information',
      confidence: needsCurrentInfo.confidence,
    };
  }

  // REGLA 3: Formulación con marca → Hybrid
  if (useCase === 'formula_generation' && brand) {
    return {
      provider: 'hybrid',
      reason: 'Formula requires product verification',
      needsVerification: true,
    };
  }

  // REGLA 4: Chat conversacional → OpenAI
  return { provider: 'openai', reason: 'Conversational chat' };
}
```

**Detección de Intención** (usando GPT-4o-mini, barato):

```typescript
async function detectCurrentInfoIntent(prompt: string): Promise<{
  detected: boolean;
  entities: string[];
  confidence: number;
}> {
  const intentSystemPrompt = `You are an intent classifier.
Analyze if the user query requires CURRENT information from the web.

Respond ONLY with JSON:
{
  "requires_current_info": true/false,
  "entities": ["brand", "product", "date"],
  "confidence": 0.0-1.0,
  "reason": "short explanation"
}`;

  const response = await openai.chat.completions.create({
    model: 'gpt-4o-mini', // ← Barato ($0.15/1M tokens)
    messages: [
      { role: 'system', content: intentSystemPrompt },
      { role: 'user', content: prompt },
    ],
    max_completion_tokens: 150,
    temperature: 0.1, // Determinístico
  });

  const result = JSON.parse(response.choices[0].message.content || '{}');
  return {
    detected: result.requires_current_info || false,
    entities: result.entities || [],
    confidence: result.confidence || 0.5,
  };
}
```

**Casos de uso**:

| Query del usuario | Detección | Provider | Razón |
|-------------------|-----------|----------|-------|
| "¿Cómo funciona el tono ceniza?" | Teoría | OpenAI | Conocimiento base |
| "¿Cuál es la mejor marca de tinte 2025?" | Actualidad | Perplexity | Necesita internet |
| "Nombres exactos de Wella Koleston" | Catálogo | Perplexity | Busca productos |
| "Generar fórmula con L'Oréal INOA" | Fórmula + marca | Hybrid | Verifica productos |
| "¿Puedo mezclar marcas?" | Teoría | OpenAI | Conocimiento base |

---

### 2. Hybrid Executor

**Objetivo**: OpenAI genera fórmula + Perplexity verifica productos en paralelo

**Implementación**: `supabase/functions/ai-proxy/hybrid-executor.ts`

**Flujo**:

```typescript
export async function executeHybridFormula(params: {
  systemPrompt: string;
  userPrompt: string;
  brand: string;
  productLine?: string;
}): Promise<{
  formula: string;
  verifiedProducts: VerifiedProduct[];
  warnings: string[];
  citations: any[];
}> {

  // PASO 1: OpenAI genera fórmula (streaming)
  const rawFormula = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ],
    stream: true, // ← UX fluida
  });

  let formulaText = '';
  for await (const chunk of rawFormula) {
    formulaText += chunk.choices[0]?.delta?.content || '';
  }

  // PASO 2: Extraer productos mencionados
  const extractedProducts = await extractProductsFromFormula(formulaText);

  // PASO 3: Verificar CADA producto con Perplexity (paralelo)
  const verificationPromises = extractedProducts.map((product) =>
    verifyProductWithPerplexity(product, brand, productLine)
  );

  const verificationResults = await Promise.allSettled(verificationPromises);

  // PASO 4: Consolidar resultados
  const verifiedProducts: VerifiedProduct[] = [];
  const warnings: string[] = [];

  verificationResults.forEach((result, index) => {
    if (result.status === 'fulfilled' && !result.value.exists) {
      warnings.push(
        `⚠️ Producto "${extractedProducts[index].name}" no encontrado. ` +
        `Alternativa: ${result.value.alternative}`
      );
    }
  });

  // PASO 5: Self-correction si hay errores
  if (warnings.length > 0) {
    formulaText = await selfCorrectFormula(formulaText, verificationResults);
  }

  return { formula: formulaText, verifiedProducts, warnings, citations };
}
```

**Verificación de Producto con Perplexity**:

```typescript
async function verifyProductWithPerplexity(
  product: ExtractedProduct,
  brand: string,
  productLine?: string
): Promise<ProductVerification> {

  const query = `Busca el producto EXACTO "${product.name}" de ${brand}.

RESPONDE SOLO CON JSON:
{
  "exists": true/false,
  "official_name": "Nombre exacto del fabricante",
  "code": "Código (ej: 7/0)",
  "alternative": "Si no existe, sugerir equivalente",
  "url": "URL oficial"
}`;

  const perplexityRes = await fetch('https://api.perplexity.ai/chat/completions', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${PERPLEXITY_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'sonar-pro',
      messages: [{ role: 'user', content: query }],
      temperature: 0.0, // Determinístico
      search_domain_filter: getDomainFilter(brand), // Solo dominios oficiales
      return_citations: true,
    }),
  });

  const data = await perplexityRes.json();
  const result = JSON.parse(data.choices[0].message.content);

  return {
    product: {
      name: result.official_name || product.name,
      code: result.code,
      verified: result.exists,
      source: result.url,
    },
    exists: result.exists,
    alternative: result.alternative,
    citations: data.citations || [],
  };
}
```

**Domain Filter por Marca** (solo fuentes oficiales):

```typescript
function getDomainFilter(brand: string): string[] | undefined {
  const brandDomains: Record<string, string[]> = {
    "L'Oréal Professionnel": ['lorealprofessionnel.com'],
    'Wella Professionals': ['wella.com', 'wella.es'],
    'Schwarzkopf Professional': ['schwarzkopf-professional.com'],
    'Redken': ['redken.com', 'redken.es'],
    'Matrix': ['matrix.com'],
    'Goldwell': ['goldwell.com'],
    'Salerm': ['salerm.com', 'salerm.es'],
    // ... resto de marcas de brands.json
  };

  return brandDomains[brand.trim()];
}
```

---

### 3. Product Extractor

**Objetivo**: Extraer productos de fórmulas generadas (regex + NLP híbrido)

**Implementación**: `supabase/functions/ai-proxy/product-extractor.ts`

**Estrategia Híbrida**:

```typescript
export async function extractProductsFromFormula(
  formulaText: string
): Promise<ExtractedProduct[]> {

  // PASO 1: Extracción rápida con regex (80% de casos)
  const regexProducts = extractWithRegex(formulaText);

  // PASO 2: Extracción semántica con GPT-4o-mini (casos complejos)
  const nlpProducts = await extractWithNLP(formulaText);

  // PASO 3: Merge y deduplicación
  const uniqueProducts = deduplicateProducts([...regexProducts, ...nlpProducts]);

  return uniqueProducts;
}
```

**Regex Patterns** (casos obvios):

```typescript
function extractWithRegex(text: string): ExtractedProduct[] {
  const products: ExtractedProduct[] = [];

  // PATRON 1: Tonos (7/03, 9.1, 6N)
  const tonePattern = /\b(\d{1,2}[/.]\d{1,2}|\d{1,2}[NABCRV])\b/g;

  // PATRON 2: Oxidantes (20 Vol, 6%, oxidante 30)
  const oxidantPattern = /\b(?:oxidante|revelador|developer)\s*(\d{1,2})\s*(?:vol|%)?/gi;

  // PATRON 3: Cantidades (Majirel 7/03 (60g))
  const quantityPattern = /([A-Z][a-zA-Z\s]+\d{1,2}[/.]\d{1,2})\s*\((\d+)(g|ml)\)/g;

  // PATRON 4: Decolorantes (Blond Studio, Platinium Plus)
  const lightenerPattern = /\b(Blond\s+Studio|Platinium\s+Plus|Freelights|BlondMe)\b/gi;

  // ... aplicar patterns y extraer productos
  return products;
}
```

**NLP Fallback** (casos complejos):

```typescript
async function extractWithNLP(formulaText: string): Promise<ExtractedProduct[]> {
  const extractionPrompt = `Extract ALL hair color products from this formula.

RESPOND ONLY WITH JSON ARRAY:
[
  {
    "name": "Product name",
    "code": "Code if mentioned",
    "quantity": "Quantity if mentioned",
    "type": "color|oxidant|treatment|lightener"
  }
]

Formula:
${formulaText}`;

  const completion = await openai.chat.completions.create({
    model: 'gpt-4o-mini', // Barato
    messages: [{ role: 'user', content: extractionPrompt }],
    temperature: 0.0,
    response_format: { type: 'json_object' },
  });

  return JSON.parse(completion.choices[0].message.content || '{"products":[]}').products;
}
```

---

### 4. Self-Correction Loop

**Objetivo**: Si productos no existen, regenerar sección automáticamente

**Implementación**: `supabase/functions/ai-proxy/self-correction.ts`

```typescript
export async function selfCorrectFormula(
  originalFormula: string,
  verificationResults: ProductVerification[]
): Promise<string> {

  const failedProducts = verificationResults.filter((v) => !v.exists);

  if (failedProducts.length === 0) {
    return originalFormula; // Sin errores
  }

  // Construir lista de errores
  const errors = failedProducts.map((fp) => ({
    mentioned: fp.product.name,
    issue: `Product not found in official catalog`,
    alternative: fp.alternative,
  }));

  const correctionPrompt = `You generated a formula but some products don't exist.

ERRORS:
${JSON.stringify(errors, null, 2)}

TASK: Rewrite ONLY the "🛒 TU LISTA DE COMPRA" section,
replacing non-existent products with verified alternatives.

ORIGINAL FORMULA:
${originalFormula}

CORRECTED "🛒 TU LISTA DE COMPRA" SECTION:`;

  const correction = await openai.chat.completions.create({
    model: 'gpt-4o',
    messages: [{ role: 'user', content: correctionPrompt }],
    temperature: 0.2, // Baja temperatura para corrección precisa
  });

  const correctedSection = correction.choices[0].message.content || '';

  // Reemplazar sección en fórmula original
  return replaceProductsSection(originalFormula, correctedSection);
}
```

---

### 5. Cache Manager (3 Niveles)

**Objetivo**: Reducir costos de Perplexity con caché inteligente

**Implementación**: `supabase/functions/ai-proxy/cache-manager.ts`

**Tabla Supabase**:

```sql
CREATE TABLE ai_cache (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  level TEXT NOT NULL CHECK (level IN ('product_catalog', 'mixing_ratios', 'formulas')),
  key TEXT NOT NULL,
  value JSONB NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  expires_at TIMESTAMPTZ NOT NULL,
  access_count INTEGER DEFAULT 0,
  last_accessed_at TIMESTAMPTZ,
  source TEXT CHECK (source IN ('openai', 'perplexity', 'hybrid')),
  citations JSONB,
  UNIQUE (level, key)
);
```

**Niveles de Caché**:

| Nivel | TTL | Descripción | Ejemplo |
|-------|-----|-------------|---------|
| `product_catalog` | 90 días | Nombres de productos verificados | "Wella Koleston Perfect 7/0" |
| `mixing_ratios` | 180 días | Proporciones oficiales | "L'Oréal INOA: 1:1" |
| `formulas` | 30 días | Fórmulas completas | Hash de inputs → fórmula |

**API de Caché**:

```typescript
export class CacheManager {
  async getProductCatalog(brand: string, productLine?: string): Promise<CacheEntry | null> {
    const cacheKey = `catalog:${brand}${productLine ? `:${productLine}` : ''}`;

    const { data } = await supabase
      .from('ai_cache')
      .select('*')
      .eq('level', 'product_catalog')
      .eq('key', cacheKey)
      .gte('expires_at', new Date())
      .maybeSingle();

    if (data) {
      console.log(`[Cache HIT] ${cacheKey}`);
      // Update access stats
      await supabase.from('ai_cache')
        .update({ access_count: data.access_count + 1 })
        .eq('id', data.id);
    }

    return data;
  }

  async setProductCatalog(
    brand: string,
    productLine: string | undefined,
    products: any[],
    citations: any[]
  ): Promise<void> {
    const cacheKey = `catalog:${brand}${productLine ? `:${productLine}` : ''}`;
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 90); // 90 días

    await supabase.from('ai_cache').upsert({
      level: 'product_catalog',
      key: cacheKey,
      value: { products, brand, productLine },
      expires_at: expiresAt,
      source: 'perplexity',
      citations,
    });
  }
}
```

---

## 📈 Beneficios Esperados

### Calidad

| Métrica | Actual | Con Mejoras | Mejora |
|---------|--------|-------------|--------|
| Precisión de productos | ~70% | 95%+ | +25% |
| Productos con fuentes oficiales | 0% | 100% | +100% |
| Detección de intención | 60% (regex) | 90%+ (AI) | +30% |
| Alucinaciones de productos | ~30% | <5% | -25% |

### UX

- ✅ Chat se siente como ChatGPT (acceso a internet automático)
- ✅ Fórmulas con citaciones oficiales (credibilidad)
- ✅ Warnings proactivos si producto no existe
- ✅ Alternativas sugeridas automáticamente

### Costos (estimación mensual para 1000 usuarios activos)

**Actual**:
```
OpenAI: $12.65/mes (95% del uso)
Perplexity: $0.65/mes (5% del uso)
TOTAL: $13.30/mes
```

**Con mejoras (primeros 3 meses)**:
```
OpenAI: $10.15/mes (80% - reducción por caché)
Perplexity: $3.10/mes (20% - aumento estratégico)
TOTAL: $13.25/mes (similar inicial)
```

**Con mejoras (después de 3 meses con caché maduro)**:
```
OpenAI: $8.00/mes (reducción por caché)
Perplexity: $0.60/mes (cache hit rate 70%+)
TOTAL: $8.60/mes (35% reducción)
```

**ROI**: 35% reducción de costos + 95%+ accuracy

---

## 🗓️ Roadmap de Implementación

### Fase 1: Routing Inteligente (Semana 1)

**Objetivo**: Chat detecta automáticamente cuándo usar internet

**Tareas**:
- [ ] Crear `intelligent-router.ts` con detección de intención (GPT-4o-mini)
- [ ] Modificar `ai-proxy/index.ts` para usar router en todos los casos
- [ ] Actualizar `chat.tsx` para eliminar detección manual (regex)
- [ ] Testing: Validar detección de intención con 100 queries reales

**Criterio de éxito**:
- 80%+ de queries de productos detectadas correctamente
- Latencia adicional < 500ms (GPT-4o-mini es rápido)

---

### Fase 2: Hybrid Executor (Semana 2)

**Objetivo**: Fórmulas con productos verificados en tiempo real

**Tareas**:
- [ ] Crear `hybrid-executor.ts` con OpenAI + Perplexity paralelo
- [ ] Crear `product-extractor.ts` con regex + NLP
- [ ] Integrar hybrid mode en `formula_generation` case
- [ ] Testing: Validar que 95%+ de productos sean verificados

**Criterio de éxito**:
- 95%+ de productos mencionados verificados con fuentes oficiales
- Citaciones de Perplexity presentes en todas las fórmulas
- Tiempo de generación < 15 segundos (incluyendo verificación)

---

### Fase 3: Self-Correction (Semana 3)

**Objetivo**: Eliminar productos inexistentes automáticamente

**Tareas**:
- [ ] Crear `self-correction.ts` con loop de corrección
- [ ] Integrar corrección automática en hybrid executor
- [ ] Actualizar prompts con Chain of Thought (CoT)
- [ ] Testing: Validar que productos inexistentes sean reemplazados

**Criterio de éxito**:
- 0 productos inexistentes en fórmulas finales
- Warnings claros cuando se sugieren alternativas
- Explicación del "por qué" de cada alternativa

---

### Fase 4: Caché Inteligente (Semana 4)

**Objetivo**: Reducir costos 70%+ con caché estratégico

**Tareas**:
- [ ] Crear tabla `ai_cache` (migración SQL)
- [ ] Implementar `CacheManager` con 3 niveles
- [ ] Integrar caché en todos los flujos
- [ ] Dashboard de métricas de caché

**Criterio de éxito**:
- Cache hit rate > 60% para productos
- Reducción de costos de Perplexity en 70%+
- Latencia de cache hits < 100ms

---

## 🧪 Testing

### Test Cases Críticos

```typescript
// 1. Router detecta intención correctamente
test('Detecta consulta de productos actual', async () => {
  const result = await routeRequest({
    prompt: '¿Cuál es la mejor marca de tinte 2025?',
  });
  expect(result.provider).toBe('perplexity');
});

// 2. Hybrid executor verifica productos
test('Verifica productos con Perplexity', async () => {
  const result = await executeHybridFormula({
    brand: "L'Oréal Professionnel",
    productLine: 'INOA',
  });
  expect(result.verifiedProducts.every(p => p.verified)).toBe(true);
});

// 3. Self-correction corrige productos inexistentes
test('Corrige productos inexistentes', async () => {
  // Mock: GPT genera "Majirel 999/99" (no existe)
  const result = await executeHybridFormula({...});
  expect(result.formula).not.toContain('999/99');
  expect(result.warnings.length).toBeGreaterThan(0);
});
```

---

## 📁 Archivos Modificados/Creados

### Nuevos (6 archivos principales)

```
supabase/functions/ai-proxy/
├── intelligent-router.ts      # Router OpenAI/Perplexity
├── hybrid-executor.ts         # Ejecutor híbrido paralelo
├── product-extractor.ts       # Extracción regex + NLP
├── self-correction.ts         # Self-correction loop
└── cache-manager.ts           # Gestor de caché 3 niveles

supabase/migrations/
└── 20251028_create_ai_cache_table.sql
```

### Modificados (4 archivos)

```
supabase/functions/ai-proxy/index.ts
  - Integrar intelligent-router
  - Agregar hybrid executor en formula_generation

lib/formula-prompts.ts
  - Actualizar con Chain of Thought

app/(app)/(tabs)/chat.tsx
  - Eliminar detección manual de productos (regex)

app/(app)/formula/step5.tsx
  - Mostrar warnings de verificación
```

---

## 💡 Decisiones de Diseño

### ¿Por qué NO usar base de datos de productos?

**Ventajas de NO tener BD**:
- ✅ Siempre actualizado (Perplexity busca en tiempo real)
- ✅ Sin mantenimiento manual (40+ marcas = complejo)
- ✅ Flexibilidad (nuevas marcas sin código)
- ✅ Fuentes oficiales citadas (credibilidad)

**Mitigación de desventajas**:
- ❌ Costos de Perplexity → ✅ Caché 90 días reduce 70%+
- ❌ Latencia de búsqueda → ✅ Caché hits < 100ms
- ❌ Posibles errores → ✅ Self-correction automática

### ¿Por qué usar GPT-4o-mini para detección de intención?

**Razones**:
- ✅ 10x más barato que GPT-4o ($0.15 vs $5/1M tokens)
- ✅ Suficientemente inteligente para clasificación
- ✅ Latencia baja (~500ms)
- ✅ Reduce carga a Perplexity (solo llamar cuando realmente se necesita)

### ¿Por qué hybrid mode en formulación?

**Razones**:
- ✅ OpenAI es mejor en reasoning (crear fórmulas paso a paso)
- ✅ Perplexity es mejor en búsqueda de datos actuales (productos)
- ✅ Combinación garantiza calidad + precisión
- ✅ Auto-corrección elimina alucinaciones

---

## 🎯 Próximos Pasos

### Prototipo Rápido (1-2 días)

1. **Implementar `intelligent-router.ts` básico**
   - Detección de intención con GPT-4o-mini
   - Testing con 20 queries reales

2. **Testing A/B**
   - Comparar routing inteligente vs actual
   - Métricas: accuracy, latency, costo

3. **Decisión Go/No-Go**
   - Si accuracy > 80% → implementar completo
   - Si accuracy < 80% → iterar en detección

### Rollout Gradual (2-4 semanas)

1. **Semana 1**: Routing inteligente en producción (50% usuarios)
2. **Semana 2**: Hybrid executor en formulación
3. **Semana 3**: Self-correction + Chain of Thought
4. **Semana 4**: Caché inteligente + dashboard

---

## 📊 Métricas de Éxito

### KPIs a Medir

| Métrica | Baseline | Target | Cómo medirlo |
|---------|----------|--------|--------------|
| Accuracy de productos | 70% | 95%+ | Manual review de 100 fórmulas |
| Detección de intención | 60% | 90%+ | Validación manual de 100 queries |
| Citaciones oficiales | 0% | 100% | Check automático en fórmulas |
| Cache hit rate | N/A | 60%+ | Métrica en dashboard |
| Costo por fórmula | $0.02 | $0.01 | Tracking en ai_usage_log |
| Latencia de formulación | 8s | <15s | Metric en logging |

---

## 🔍 Ejemplo Comparativo

### ANTES (Actual)

```
Usuario: "Dame fórmula con Wella Koleston para ir de nivel 6 a 8"

Sistema:
✓ Usa OpenAI gpt-4o
✓ Genera fórmula en 8 segundos
✗ Puede mencionar "Wella Koleston Perfect 8/03" (¿existe?)
✗ Sin verificación de productos
✗ Sin citaciones de fuentes oficiales
✗ Proporción "1:1.5" (¿es oficial?)

Resultado: Fórmula rápida pero sin garantía de precisión
```

### DESPUÉS (Con Mejoras)

```
Usuario: "Dame fórmula con Wella Koleston para ir de nivel 6 a 8"

Sistema:
1. Intelligent Router detecta: "Formulación con marca" → Hybrid mode
2. OpenAI genera fórmula base (streaming, 8s)
3. Product Extractor detecta:
   - "Wella Koleston Perfect 8/03"
   - "Oxidante Welloxon 6%"
4. Perplexity verifica (paralelo, 2s):
   - 8/03: ✓ Existe → "8/03 Medium Blonde Gold"
   - Welloxon 6%: ✓ Existe → URL oficial
5. Self-correction: Sin errores, fórmula OK
6. Cache: Guardar productos verificados (90 días)

Resultado:
✅ Fórmula en 10 segundos (vs 8s antes, +2s por verificación)
✅ Productos 100% verificados
✅ Citaciones oficiales: [wella.com/koleston-perfect]
✅ Proporción oficial verificada
✅ Cache futuro: próxima fórmula Wella en 8s (sin re-verificar)
```

---

## ❓ Preguntas Frecuentes

### ¿Qué pasa si Perplexity no encuentra un producto?

**Respuesta**: Self-correction sugiere alternativa:

```
⚠️ Producto "Wella Koleston Perfect 8/99" no encontrado en catálogo oficial.

Alternativa sugerida:
• Mezcla: 8/0 (70g) + 9/0 (30g) = 8.5 rubio claro equivalente
• RAZÓN: Wella Koleston no tiene tono 8/99 en catálogo 2025

📚 Fuente: [Manual Técnico Wella Koleston Perfect]
```

### ¿Cuánto aumenta la latencia con verificación?

**Respuesta**: +2-4 segundos iniciales, luego 0s con caché

- Primera fórmula con marca X: 10-12s (8s generación + 2-4s verificación)
- Segunda fórmula con marca X: 8s (caché hit, sin re-verificar)
- Cache hit rate esperado: 60-70% después de 1 mes

### ¿Qué pasa si el usuario pregunta sobre marca nueva?

**Respuesta**: Funciona igual, Perplexity busca en tiempo real

- No requiere actualización de código
- Perplexity busca en web oficial de la marca
- Si marca no tiene web oficial → Respuesta honesta: "No tengo información verificada"

### ¿Cuánto cuesta implementar esto?

**Respuesta**: Sin costos de infraestructura, solo tiempo de desarrollo

- Desarrollo: 2-4 semanas (1 desarrollador)
- Infraestructura: $0 (usa Supabase existente)
- Costos de IA: Similar inicial (~$13/mes), luego -35% con caché

---

## 📚 Referencias

**Archivos clave del proyecto**:
- `lib/ai-client.ts` - Cliente AI actual
- `supabase/functions/ai-proxy/index.ts` - Edge function principal
- `lib/formula-prompts.ts` - System prompts actuales
- `app/(app)/(tabs)/chat.tsx` - Chat UI
- `app/(app)/formula/step5.tsx` - Generación de fórmulas

**Sesiones relacionadas**:
- `sessions/2025-10-28-fix-openai-vision-rejection-502-errors.md` - Fix de prompts para Vision API
- `sessions/2025-10-27-step5-chat-style-refactor.md` - Refactor de step5 a estilo chat

---

## ✅ Conclusión

Esta propuesta maximiza el uso de IA (OpenAI + Perplexity) **sin necesidad de base de datos de productos**.

**Clave del sistema**:
1. **Routing inteligente**: IA decide cuándo buscar en internet
2. **Hybrid executor**: OpenAI crea + Perplexity verifica
3. **Self-correction**: Elimina alucinaciones automáticamente
4. **Caché inteligente**: Reduce costos 70%+ con el tiempo

**Resultado esperado**:
- Chat que se siente como ChatGPT (acceso a internet cuando se necesita)
- Fórmulas con productos 100% verificados y citados
- Sin mantenimiento de catálogos (40+ marcas)
- Costos reducidos 35% a largo plazo

**¿Siguiente paso?**: Implementar prototipo de Fase 1 (routing inteligente) en 1-2 días para validar arquitectura.