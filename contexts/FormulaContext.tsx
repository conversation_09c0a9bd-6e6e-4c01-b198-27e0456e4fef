import createContextHook from '@nkzw/create-context-hook';
import { useState, useMemo, useCallback, useEffect, useRef } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import type { FormulaData, Client } from '@/types';
import { logger } from '@/lib/logger';

const FORMULA_STORAGE_KEY = 'formula_draft';
const CLIENT_STORAGE_KEY = 'formula_selected_client';
const DEBOUNCE_DELAY_MS = 500; // Fix #5: Debounce AsyncStorage writes (500ms)

export const [FormulaProvider, useFormula] = createContextHook(() => {
  const [formulaData, setFormulaData] = useState<FormulaData>({
    currentColorImages: [],
    safetyChecklist: {
      gloves: false,
      ventilation: false,
      patchTest: false,
      strandTest: false,
      noMetalSalts: false,
      hairCondition: false,
      photoConsentGiven: false,
    },
  });

  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [isLoaded, setIsLoaded] = useState(false);

  // Refs for debounce timers (Fix #5: Performance optimization)
  const formulaSaveTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);
  const clientSaveTimerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Load formula data from AsyncStorage on mount
  useEffect(() => {
    const loadDraft = async () => {
      try {
        const [draftJson, clientJson] = await Promise.all([
          AsyncStorage.getItem(FORMULA_STORAGE_KEY),
          AsyncStorage.getItem(CLIENT_STORAGE_KEY),
        ]);

        if (draftJson) {
          const draft: FormulaData = JSON.parse(draftJson);
          setFormulaData(draft);
          logger.debug('FormulaContext', 'Borrador restaurado desde AsyncStorage');
        }

        if (clientJson) {
          const client: Client = JSON.parse(clientJson);
          setSelectedClient(client);
          logger.debug('FormulaContext', 'Cliente restaurado', { clientName: client.name });
        }
      } catch (error) {
        logger.error('FormulaContext', 'Error cargando borrador', error);
      } finally {
        setIsLoaded(true);
      }
    };

    loadDraft();
  }, []);

  // Save formula data to AsyncStorage with debounce (Fix #5: 500ms delay)
  useEffect(() => {
    if (!isLoaded) return; // Don't save on initial load

    // Clear existing timer
    if (formulaSaveTimerRef.current) {
      clearTimeout(formulaSaveTimerRef.current);
    }

    // Set new timer
    formulaSaveTimerRef.current = setTimeout(async () => {
      try {
        await AsyncStorage.setItem(FORMULA_STORAGE_KEY, JSON.stringify(formulaData));
        logger.debug('FormulaContext', 'Borrador guardado (debounced)');
      } catch (error) {
        logger.error('FormulaContext', 'Error guardando borrador', error);
      }
    }, DEBOUNCE_DELAY_MS);

    // Cleanup: clear timer on unmount or dependency change
    return () => {
      if (formulaSaveTimerRef.current) {
        clearTimeout(formulaSaveTimerRef.current);
      }
    };
  }, [formulaData, isLoaded]);

  // Save selected client to AsyncStorage with debounce (Fix #5: 500ms delay)
  useEffect(() => {
    if (!isLoaded) return; // Don't save on initial load

    // Clear existing timer
    if (clientSaveTimerRef.current) {
      clearTimeout(clientSaveTimerRef.current);
    }

    // Set new timer
    clientSaveTimerRef.current = setTimeout(async () => {
      try {
        if (selectedClient) {
          await AsyncStorage.setItem(CLIENT_STORAGE_KEY, JSON.stringify(selectedClient));
          logger.debug('FormulaContext', 'Cliente guardado (debounced)', { clientName: selectedClient.name });
        } else {
          await AsyncStorage.removeItem(CLIENT_STORAGE_KEY);
          logger.debug('FormulaContext', 'Cliente removido (debounced)');
        }
      } catch (error) {
        logger.error('FormulaContext', 'Error guardando cliente', error);
      }
    }, DEBOUNCE_DELAY_MS);

    // Cleanup: clear timer on unmount or dependency change
    return () => {
      if (clientSaveTimerRef.current) {
        clearTimeout(clientSaveTimerRef.current);
      }
    };
  }, [selectedClient, isLoaded]);

  const updateCurrentColorImages = useCallback((images: string[]) => {
    setFormulaData((prev) => ({ ...prev, currentColorImages: images }));
  }, []);

  const updateCurrentColorAnalysis = useCallback((analysis: FormulaData['currentColorAnalysis']) => {
    setFormulaData((prev) => ({ ...prev, currentColorAnalysis: analysis }));
  }, []);

  const updateDesiredColor = useCallback((
    images: string[],
    analysis: FormulaData['desiredColorAnalysis'],
    technique?: string
  ) => {
    setFormulaData((prev) => ({
      ...prev,
      desiredColorImages: images,
      desiredColorAnalysis: analysis,
      technique,
    }));
  }, []);

  const updateSafetyChecklist = useCallback((checklist: FormulaData['safetyChecklist']) => {
    setFormulaData((prev) => ({ ...prev, safetyChecklist: checklist }));
  }, []);

  const updateClientInfo = useCallback((name: string, signature?: string) => {
    setFormulaData((prev) => ({ ...prev, clientName: name, clientSignature: signature }));
  }, []);

  const updateBrand = useCallback((brand: string, productLine?: string, brandTier?: number) => {
    setFormulaData((prev) => ({ ...prev, brand, productLine, brandTier }));
  }, []);

  const selectClient = useCallback((client: Client) => {
    setSelectedClient(client);
    setFormulaData((prev) => ({ ...prev, clientName: client.name }));
    logger.info('FormulaContext', 'Cliente seleccionado para formulación', { clientName: client.name });
  }, []);

  const resetFormula = useCallback(async () => {
    logger.debug('FormulaContext', 'resetFormula() llamado');
    setFormulaData({
      currentColorImages: [],
      safetyChecklist: {
        gloves: false,
        ventilation: false,
        patchTest: false,
        strandTest: false,
        noMetalSalts: false,
        hairCondition: false,
        photoConsentGiven: false,
      },
    });
    setSelectedClient(null);

    // Clear AsyncStorage
    try {
      await Promise.all([
        AsyncStorage.removeItem(FORMULA_STORAGE_KEY),
        AsyncStorage.removeItem(CLIENT_STORAGE_KEY),
      ]);
      logger.debug('FormulaContext', 'Borrador limpiado de AsyncStorage');
    } catch (error) {
      logger.error('FormulaContext', 'Error limpiando borrador', error);
    }
  }, []);

  return useMemo(() => ({
    formulaData,
    selectedClient,
    isLoaded,
    updateCurrentColorImages,
    updateCurrentColorAnalysis,
    updateDesiredColor,
    updateSafetyChecklist,
    updateClientInfo,
    updateBrand,
    selectClient,
    resetFormula,
  }), [formulaData, selectedClient, isLoaded, updateCurrentColorImages, updateCurrentColorAnalysis, updateDesiredColor, updateSafetyChecklist, updateClientInfo, updateBrand, selectClient, resetFormula]);
});
