import { useState, useEffect, useCallback, useMemo } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import { supabase } from '@/lib/supabase';
import type { User, Session } from '@supabase/supabase-js';
import type { UserProfile } from '@/types';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { logger } from '@/lib/logger';

const AUTH_SESSION_KEY = 'salonier-auth-session';

export const [AuthContext, useAuth] = createContextHook(() => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);

  // Load session from storage and check auth status
  const loadSession = useCallback(async () => {
    try {
      setIsLoading(true);

      // Check if we have a session in Supabase
      const { data: { session: currentSession }, error } = await supabase.auth.getSession();

      if (error) {
        logger.error('AuthContext', 'Error getting session', error);
        return;
      }

      if (currentSession) {
        setSession(currentSession);
        setUser(currentSession.user);
        setIsAuthenticated(true);

        // Load user profile
        await loadProfile(currentSession.user.id);
      } else {
        setSession(null);
        setUser(null);
        setProfile(null);
        setIsAuthenticated(false);
      }
    } catch (error) {
      logger.error('AuthContext', 'Error loading session', error);
      setSession(null);
      setUser(null);
      setProfile(null);
      setIsAuthenticated(false);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load user profile from database
  const loadProfile = useCallback(async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        logger.error('AuthContext', 'Error loading profile', error);
        return;
      }

      if (data) {
        setProfile({
          id: data.id,
          name: data.name,
          email: data.email,
          photo: data.photo,
          phone: data.phone,
          specialty: data.specialty,
          licenseNumber: data.license_number,
          bio: data.bio,
          role: 'stylist', // Default, will be determined by organization membership
        });
      }
    } catch (error) {
      logger.error('AuthContext', 'Error loading profile', error);
    }
  }, []);

  // Initialize auth on mount
  useEffect(() => {
    loadSession();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, newSession) => {
      logger.info('AuthContext', 'Auth state changed', { event });

      if (event === 'SIGNED_IN' && newSession) {
        setSession(newSession);
        setUser(newSession.user);
        setIsAuthenticated(true);
        await loadProfile(newSession.user.id);
      } else if (event === 'SIGNED_OUT') {
        setSession(null);
        setUser(null);
        setProfile(null);
        setIsAuthenticated(false);
        await AsyncStorage.removeItem(AUTH_SESSION_KEY);
      } else if (event === 'TOKEN_REFRESHED' && newSession) {
        setSession(newSession);
      }
    });

    return () => {
      subscription.unsubscribe();
    };
  }, [loadSession, loadProfile]);

  // Sign up with email and password
  const signUp = useCallback(async (email: string, password: string, name: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) {
        throw new Error(error.message);
      }

      // Profile is automatically created by the database trigger
      logger.info('AuthContext', 'Sign up successful', { userId: data.user?.id });

      return { user: data.user, error: null };
    } catch (error) {
      logger.error('AuthContext', 'Sign up error', error);
      return { user: null, error: error instanceof Error ? error.message : 'Sign up failed' };
    }
  }, []);

  // Sign in with email and password
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        throw new Error(error.message);
      }

      logger.info('AuthContext', 'Sign in successful', { userId: data.user?.id });

      return { user: data.user, error: null };
    } catch (error) {
      logger.error('AuthContext', 'Sign in error', error);
      return { user: null, error: error instanceof Error ? error.message : 'Sign in failed' };
    }
  }, []);

  // Sign out
  const signOut = useCallback(async () => {
    try {
      const { error } = await supabase.auth.signOut();

      if (error) {
        throw new Error(error.message);
      }

      logger.info('AuthContext', 'Sign out successful');
    } catch (error) {
      logger.error('AuthContext', 'Sign out error', error);
      throw error;
    }
  }, []);

  // Reset password (send email)
  const resetPassword = useCallback(async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: 'salonier://reset-password', // Deep link for mobile
      });

      if (error) {
        throw new Error(error.message);
      }

      logger.info('AuthContext', 'Password reset email sent');
      return { error: null };
    } catch (error) {
      logger.error('AuthContext', 'Reset password error', error);
      return { error: error instanceof Error ? error.message : 'Failed to send reset email' };
    }
  }, []);

  // Update password (after receiving reset link)
  const updatePassword = useCallback(async (newPassword: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword,
      });

      if (error) {
        throw new Error(error.message);
      }

      logger.info('AuthContext', 'Password updated successfully');
      return { error: null };
    } catch (error) {
      logger.error('AuthContext', 'Update password error', error);
      return { error: error instanceof Error ? error.message : 'Failed to update password' };
    }
  }, []);

  // Update profile
  const updateProfile = useCallback(async (updates: Partial<UserProfile>) => {
    if (!user) {
      throw new Error('No user logged in');
    }

    try {
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.phone !== undefined) updateData.phone = updates.phone;
      if (updates.photo !== undefined) updateData.photo = updates.photo;
      if (updates.specialty !== undefined) updateData.specialty = updates.specialty;
      if (updates.licenseNumber !== undefined) updateData.license_number = updates.licenseNumber;
      if (updates.bio !== undefined) updateData.bio = updates.bio;

      const { error } = await supabase
        .from('profiles')
        .update(updateData)
        .eq('id', user.id);

      if (error) {
        throw new Error(error.message);
      }

      // Reload profile
      await loadProfile(user.id);

      logger.info('AuthContext', 'Profile updated successfully', { userId: user.id });
      return { error: null };
    } catch (error) {
      logger.error('AuthContext', 'Update profile error', error);
      return { error: error instanceof Error ? error.message : 'Failed to update profile' };
    }
  }, [user, loadProfile]);

  return useMemo(() => ({
    user,
    session,
    profile,
    isLoading,
    isAuthenticated,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  }), [
    user,
    session,
    profile,
    isLoading,
    isAuthenticated,
    signUp,
    signIn,
    signOut,
    resetPassword,
    updatePassword,
    updateProfile,
  ]);
});
