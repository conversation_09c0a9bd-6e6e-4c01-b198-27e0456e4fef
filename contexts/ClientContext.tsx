import { useState, useEffect, useCallback, useMemo } from 'react';
import createContextHook from '@nkzw/create-context-hook';
import type { Client } from '@/types';
import { supabase } from '@/lib/supabase';
import { useAuth } from '@/contexts/AuthContext';
import { logger } from '@/lib/logger';

export const [ClientContext, useClients] = createContextHook(() => {
  const { user } = useAuth();
  const [clients, setClients] = useState<Client[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  const parseClientDates = (client: any): Client => ({
    id: client.id,
    name: client.name,
    email: client.email,
    phone: client.phone,
    lastVisit: client.last_visit ? new Date(client.last_visit) : undefined,
    photo: client.photo,
    notes: client.notes,
    knownAllergies: client.known_allergies,
    isPregnantOrBreastfeeding: client.is_pregnant_or_breastfeeding ?? false,
    hasSensitiveScalp: client.has_sensitive_scalp ?? false,
    chemicalTreatments: client.chemical_treatments ?? {
      henna: false,
      chemicalStraightening: false,
      keratin: false,
    },
    communicationPreferences: client.communication_preferences ?? {
      acceptsReminders: true,
      preferredMethod: 'whatsapp',
    },
    additionalNotes: client.additional_notes,
  });

  const loadClients = useCallback(async () => {
    if (!user) {
      // No user logged in, don't load clients
      setClients([]);
      setIsLoading(false);
      return;
    }

    setIsLoading(true);
    try {
      logger.debug('ClientContext', 'Loading clients from Supabase', { userId: user.id });
      // RLS policies will automatically filter clients based on user's organization or freelance status
      const { data, error } = await supabase
        .from('clients')
        .select('*')
        .order('name', { ascending: true });

      if (error) {
        logger.error('ClientContext', 'Supabase error loading clients', error);
        throw new Error(error.message || 'Failed to load clients');
      }

      const clientsWithDates = data ? data.map(parseClientDates) : [];
      setClients(clientsWithDates);
      logger.info('ClientContext', 'Clients loaded from Supabase', { count: clientsWithDates.length });
    } catch (error) {
      logger.error('ClientContext', 'Error loading clients', error);
      setClients([]);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  useEffect(() => {
    loadClients();
  }, [loadClients]);

  const addClient = useCallback(async (client: Omit<Client, 'id'>) => {
    if (!user) {
      throw new Error('User must be logged in to add clients');
    }

    try {
      logger.info('ClientContext', 'Adding client to Supabase');
      const newClientData = {
        name: client.name,
        email: client.email,
        phone: client.phone,
        last_visit: client.lastVisit?.toISOString(),
        photo: client.photo,
        notes: client.notes,
        known_allergies: client.knownAllergies,
        is_pregnant_or_breastfeeding: client.isPregnantOrBreastfeeding,
        has_sensitive_scalp: client.hasSensitiveScalp,
        chemical_treatments: client.chemicalTreatments,
        communication_preferences: client.communicationPreferences,
        additional_notes: client.additionalNotes,
        created_by: user.id, // Set current user as creator
        organization_id: null, // Default to freelance (null), will be set if user belongs to org
      };

      const { data, error } = await supabase
        .from('clients')
        .insert(newClientData)
        .select()
        .single();

      if (error) {
        logger.error('ClientContext', 'Supabase error adding client', error);
        throw new Error(error.message || 'Failed to add client');
      }

      const newClient = parseClientDates(data);
      logger.info('ClientContext', 'Client added successfully', { clientId: newClient.id });

      await loadClients();

      return newClient;
    } catch (error) {
      logger.error('ClientContext', 'Error adding client', error);
      throw error;
    }
  }, [user, loadClients]);

  const updateClient = useCallback(async (id: string, updates: Partial<Client>) => {
    try {
      logger.debug('ClientContext', 'Updating client in Supabase', { clientId: id });
      const updateData: any = {};

      if (updates.name !== undefined) updateData.name = updates.name;
      if (updates.email !== undefined) updateData.email = updates.email;
      if (updates.phone !== undefined) updateData.phone = updates.phone;
      if (updates.lastVisit !== undefined) updateData.last_visit = updates.lastVisit?.toISOString();
      if (updates.photo !== undefined) updateData.photo = updates.photo;
      if (updates.notes !== undefined) updateData.notes = updates.notes;
      if (updates.knownAllergies !== undefined) updateData.known_allergies = updates.knownAllergies;
      if (updates.isPregnantOrBreastfeeding !== undefined) updateData.is_pregnant_or_breastfeeding = updates.isPregnantOrBreastfeeding;
      if (updates.hasSensitiveScalp !== undefined) updateData.has_sensitive_scalp = updates.hasSensitiveScalp;
      if (updates.chemicalTreatments !== undefined) updateData.chemical_treatments = updates.chemicalTreatments;
      if (updates.communicationPreferences !== undefined) updateData.communication_preferences = updates.communicationPreferences;
      if (updates.additionalNotes !== undefined) updateData.additional_notes = updates.additionalNotes;

      const { error } = await supabase
        .from('clients')
        .update(updateData)
        .eq('id', id);

      if (error) {
        logger.error('ClientContext', 'Supabase error updating client', error);
        throw new Error(error.message || 'Failed to update client');
      }

      logger.info('ClientContext', 'Client updated successfully', { clientId: id });

      await loadClients();
    } catch (error) {
      logger.error('ClientContext', 'Error updating client', error);
      throw error;
    }
  }, [loadClients]);

  const deleteClient = useCallback(async (id: string) => {
    try {
      logger.debug('ClientContext', 'Deleting client from Supabase', { clientId: id });
      const { error } = await supabase
        .from('clients')
        .delete()
        .eq('id', id);

      if (error) {
        logger.error('ClientContext', 'Supabase error deleting client', error);
        throw new Error(error.message || 'Failed to delete client');
      }

      logger.info('ClientContext', 'Client deleted successfully', { clientId: id });

      await loadClients();
    } catch (error) {
      logger.error('ClientContext', 'Error deleting client', error);
      throw error;
    }
  }, [loadClients]);

  return useMemo(() => ({
    clients,
    isLoading,
    addClient,
    updateClient,
    deleteClient,
  }), [clients, isLoading, addClient, updateClient, deleteClient]);
});
