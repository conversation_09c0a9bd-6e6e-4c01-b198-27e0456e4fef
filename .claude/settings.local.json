{"permissions": {"allow": ["mcp__supabase__list_edge_functions", "mcp__supabase__get_project", "mcp__supabase__deploy_edge_function", "mcp__supabase__list_migrations", "mcp__supabase__get_anon_key", "mcp__supabase__get_logs", "mcp__supabase__get_edge_function", "mcp__supabase__execute_sql", "mcp__context7__resolve-library-id", "mcp__context7__get-library-docs", "mcp__supabase__get_project_url", "mcp__ide__getDiagnostics", "mcp__supabase__list_projects", "mcp__supabase__list_tables", "mcp__supabase__get_advisors", "mcp__supabase__apply_migration", "mcp__playwright__browser_navigate", "mcp__playwright__browser_take_screenshot", "mcp__playwright__browser_snapshot", "mcp__playwright__browser_click", "mcp__playwright__browser_wait_for", "mcp__playwright__browser_close", "mcp__supabase__generate_typescript_types", "mcp__playwright__browser_type", "mcp__playwright__browser_console_messages"], "deny": [], "ask": []}}