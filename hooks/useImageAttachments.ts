import { useCallback, useState } from 'react';

interface UseImageAttachmentsOptions {
  maxImages?: number;
}

type AddImageInput = string | null | undefined;

const sanitizeUris = (uris: AddImageInput[]): string[] =>
  uris
    .filter((uri): uri is string => typeof uri === 'string')
    .map(uri => uri.trim())
    .filter(uri => uri.length > 0);

export const useImageAttachments = (
  { maxImages = 6 }: UseImageAttachmentsOptions = {}
) => {
  const [selectedImages, setSelectedImages] = useState<string[]>([]);
  const [showAttachmentOptions, setShowAttachmentOptions] = useState(false);

  const addImages = useCallback(
    (uris: AddImageInput[]) => {
      const sanitized = sanitizeUris(uris);
      if (sanitized.length === 0) {
        return;
      }

      setSelectedImages(prev => {
        const combined = [...prev, ...sanitized];
        return combined.slice(0, maxImages);
      });
    },
    [maxImages]
  );

  const removeImageAt = useCallback((index: number) => {
    setSelectedImages(prev => prev.filter((_, currentIndex) => currentIndex !== index));
  }, []);

  const clearImages = useCallback(() => {
    setSelectedImages([]);
  }, []);

  const toggleAttachmentOptions = useCallback(() => {
    setShowAttachmentOptions(prev => !prev);
  }, []);

  const hideAttachmentOptions = useCallback(() => {
    setShowAttachmentOptions(false);
  }, []);

  return {
    selectedImages,
    addImages,
    removeImageAt,
    clearImages,
    showAttachmentOptions,
    toggleAttachmentOptions,
    hideAttachmentOptions,
  } as const;
};
